import 'dart:io';
import 'package:flutter/material.dart' hide Border, BorderStyle;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:logging/logging.dart';

/// مساعد للتعامل مع الملفات والأذونات
class FileHelper {
  static final Logger _logger = Logger('FileHelper');

  /// حفظ صورة في جهاز المستخدم
  static Future<File> saveImage(File imageFile, String fileName) async {
    try {
      // إنشاء مجلد Sender_ID إذا لم يكن موجودًا
      final appDir = await getApplicationDocumentsDirectory();
      final senderIdDir = Directory('${appDir.path}/Sender_ID');

      if (!await senderIdDir.exists()) {
        await senderIdDir.create(recursive: true);
      }

      // تحديد اسم الملف
      final name = fileName.isEmpty ? 'temp_id_image' : fileName;

      // تحديد امتداد الملف
      final fileExtension = imageFile.path.split('.').last;

      // إنشاء مسار الملف الجديد
      final newFilePath = '${senderIdDir.path}/$name.$fileExtension';

      // نسخ الملف
      final newFile = await imageFile.copy(newFilePath);

      return newFile;
    } catch (e) {
      _logger.severe('خطأ في حفظ الصورة: $e');
      rethrow;
    }
  }

  /// التحقق من أذونات التخزين
  static Future<bool> checkStoragePermissions(BuildContext? context) async {
    // التحقق من نظام التشغيل
    if (Platform.isAndroid || Platform.isIOS) {
      // طلب أذونات الوصول إلى التخزين
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        if (!status.isGranted) {
          return false;
        }
      }
    }
    return true;
  }

  /// تصدير بيانات إلى ملف Excel
  static Future<String?> exportToExcel({
    required String sheetName,
    required List<String> headers,
    required List<List<String>> data,
    String? fileName,
  }) async {
    try {
      _logger.info('بدء تصدير البيانات إلى ملف Excel');

      // إنشاء ملف Excel جديد
      var excel = Excel.createExcel();

      // طباعة قائمة الشيتات الأصلية
      _logger.info('قائمة الشيتات الأصلية: ${excel.tables.keys.join(', ')}');

      // إنشاء شيت جديد باسم مخصص
      var sheet = excel[sheetName];
      _logger.info('تم إنشاء شيت جديد باسم $sheetName');

      // إنشاء نمط للعناوين
      var headerStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        verticalAlign: VerticalAlign.Center,
        backgroundColorHex: ExcelColor.fromHexString("#DDDDDD"),
        fontColorHex: ExcelColor.fromHexString("#000000"),
        textWrapping: TextWrapping.WrapText,
      );

      // إنشاء نمط للبيانات
      var dataStyle = CellStyle(
        horizontalAlign: HorizontalAlign.Center,
        verticalAlign: VerticalAlign.Center,
        textWrapping: TextWrapping.WrapText,
      );

      // إضافة العناوين مع التنسيق
      for (var i = 0; i < headers.length; i++) {
        var cell =
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = headerStyle;

        // إضافة حدود للخلية
        if (cell.cellStyle != null) {
          cell.cellStyle!.leftBorder = Border(borderStyle: BorderStyle.Thin);
          cell.cellStyle!.rightBorder = Border(borderStyle: BorderStyle.Thin);
          cell.cellStyle!.topBorder = Border(borderStyle: BorderStyle.Thin);
          cell.cellStyle!.bottomBorder = Border(borderStyle: BorderStyle.Thin);
        }
      }

      // إضافة البيانات مع التنسيق
      for (var i = 0; i < data.length; i++) {
        for (var j = 0; j < data[i].length; j++) {
          var cell = sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1));
          // تحديد نوع البيانات المناسب - جميع البيانات من نوع String
          cell.value = TextCellValue(data[i][j]);
          cell.cellStyle = dataStyle;

          // إضافة حدود للخلية
          if (cell.cellStyle != null) {
            cell.cellStyle!.leftBorder = Border(borderStyle: BorderStyle.Thin);
            cell.cellStyle!.rightBorder = Border(borderStyle: BorderStyle.Thin);
            cell.cellStyle!.topBorder = Border(borderStyle: BorderStyle.Thin);
            cell.cellStyle!.bottomBorder =
                Border(borderStyle: BorderStyle.Thin);
          }
        }
      }

      // تعيين الشيت كشيت افتراضي
      excel.setDefaultSheet(sheetName);
      _logger.info('تم تعيين $sheetName كشيت افتراضي');

      // طباعة قائمة الشيتات قبل الحذف
      _logger.info('قائمة الشيتات قبل الحذف: ${excel.tables.keys.join(', ')}');

      // حل مشكلة "Cannot remove from an unmodifiable list"
      try {
        // إنشاء نسخة جديدة من Excel
        var newExcel = Excel.createExcel();

        // إنشاء شيت جديد في النسخة الجديدة
        var newSheet = newExcel[sheetName];

        // نسخ البيانات من الشيت القديم إلى الشيت الجديد مع الحفاظ على التنسيق
        for (var rowIndex = 0; rowIndex < sheet.maxRows; rowIndex++) {
          for (var colIndex = 0; colIndex < sheet.maxColumns; colIndex++) {
            var cell = sheet.cell(CellIndex.indexByColumnRow(
              columnIndex: colIndex,
              rowIndex: rowIndex,
            ));

            if (cell.value != null) {
              var newCell = newSheet.cell(CellIndex.indexByColumnRow(
                columnIndex: colIndex,
                rowIndex: rowIndex,
              ));

              newCell.value = cell.value;
              newCell.cellStyle = cell.cellStyle;
            }
          }
        }

        // تعيين الشيت الجديد كشيت افتراضي
        newExcel.setDefaultSheet(sheetName);

        // ترميز الملف
        var bytes = newExcel.encode();
        if (bytes == null) {
          throw Exception('فشل في ترميز ملف Excel');
        }

        // طباعة حجم الملف
        _logger.info('حجم ملف Excel: ${bytes.length} بايت');

        // حفظ الملف - استخدام FilePicker لاختيار مكان الحفظ
        if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
          // على أنظمة سطح المكتب، نستخدم FilePicker لاختيار مكان الحفظ
          String? outputFile = await FilePicker.platform.saveFile(
            dialogTitle: 'حفظ ملف',
            fileName: fileName ?? '$sheetName.xlsx',
            type: FileType.custom,
            allowedExtensions: ['xlsx'],
          );

          if (outputFile != null) {
            // حفظ الملف في المكان المختار
            final file = File(outputFile);
            await file.writeAsBytes(bytes);
            _logger.info('تم حفظ الملف في: $outputFile');

            return outputFile;
          }
        } else {
          // على الأجهزة المحمولة، نحفظ في مجلد التطبيق
          final directory = await getApplicationDocumentsDirectory();
          final path = '${directory.path}/${fileName ?? "$sheetName.xlsx"}';
          final file = File(path);
          await file.writeAsBytes(bytes);
          _logger.info('تم حفظ الملف في: $path');

          return path;
        }
      } catch (e) {
        _logger.warning('خطأ أثناء إنشاء نسخة جديدة من Excel: $e');

        // استخدام الملف الأصلي إذا فشلت عملية النسخ
        var bytes = excel.encode();
        if (bytes == null) {
          throw Exception('فشل في ترميز ملف Excel');
        }

        // طباعة حجم الملف
        _logger.info('حجم ملف Excel: ${bytes.length} بايت');

        // حفظ الملف - استخدام FilePicker لاختيار مكان الحفظ
        if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
          // على أنظمة سطح المكتب، نستخدم FilePicker لاختيار مكان الحفظ
          String? outputFile = await FilePicker.platform.saveFile(
            dialogTitle: 'حفظ ملف',
            fileName: fileName ?? '$sheetName.xlsx',
            type: FileType.custom,
            allowedExtensions: ['xlsx'],
          );

          if (outputFile != null) {
            // حفظ الملف في المكان المختار
            final file = File(outputFile);
            await file.writeAsBytes(bytes);
            _logger.info('تم حفظ الملف في: $outputFile');

            return outputFile;
          }
        } else {
          // على الأجهزة المحمولة، نحفظ في مجلد التطبيق
          final directory = await getApplicationDocumentsDirectory();
          final path = '${directory.path}/${fileName ?? "$sheetName.xlsx"}';
          final file = File(path);
          await file.writeAsBytes(bytes);
          _logger.info('تم حفظ الملف في: $path');

          return path;
        }
      }

      return null;
    } catch (e) {
      _logger.severe('خطأ في تصدير البيانات: $e');
      rethrow;
    }
  }

  /// استيراد بيانات من ملف Excel
  static Future<Map<String, dynamic>> importFromExcel({
    List<String>? allowedExtensions,
  }) async {
    try {
      // اختيار الملف
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? ['xlsx', 'xls'],
      );

      if (result != null) {
        // قراءة الملف
        final file = File(result.files.single.path!);
        final bytes = await file.readAsBytes();
        final excel = Excel.decodeBytes(bytes);

        return {
          'success': true,
          'file': file,
          'excel': excel,
        };
      } else {
        return {
          'success': false,
          'message': 'No file selected',
        };
      }
    } catch (e) {
      _logger.severe('خطأ في استيراد البيانات: $e');
      return {
        'success': false,
        'message': 'Error occurred while importing data: $e',
      };
    }
  }

  /// عرض رسالة نجاح أو خطأ في أعلى الشاشة
  static void showMessage(BuildContext context, String message,
      {bool isError = false}) {
    if (!context.mounted) return;

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: isError ? Colors.red.shade600 : Colors.green.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isError ? Icons.error : Icons.check_circle,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد 3 ثواني
    Future.delayed(const Duration(seconds: 3), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // تجاهل الخطأ إذا كان العنصر تم حذفه بالفعل
      }
    });
  }
}
