import '../services/database_helper.dart';
import '../models/item_data.dart';
import '../utils/constants.dart';

class GoodsDataService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على جميع بيانات البضائع
  Future<List<Map<String, dynamic>>> getAllGoodsData() async {
    return await _databaseHelper.getAllGoodsData();
  }

  // الحصول على جميع بيانات البضائع كقائمة من ItemData
  Future<List<ItemData>> getAllGoodsAsItems() async {
    try {
      final goodsDataList = await _databaseHelper.getAllGoodsData();
      return goodsDataList
          .map((item) => ItemData(
                id: item[DbConstants.columnId] as int,
                nameAr: item['name_ar'] as String,
                nameEn: item['name_en'] as String,
                isSelected: false,
                quantity: 1,
                weight: 0.0,
              ))
          .toList();
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // إضافة بيانات بضاعة جديدة
  Future<int> insertGoodsData(String nameAr, String nameEn) async {
    try {
      return await _databaseHelper.insertGoodsData({
        'name_ar': nameAr,
        'name_en': nameEn,
      });
    } catch (e) {
      // ?? ??? ????? debugPrint
      return -1;
    }
  }

  // تحديث بيانات بضاعة موجودة
  Future<int> updateGoodsData(int id, String nameAr, String nameEn) async {
    try {
      return await _databaseHelper.updateGoodsData({
        DbConstants.columnId: id,
        'name_ar': nameAr,
        'name_en': nameEn,
      });
    } catch (e) {
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  // حذف بيانات بضاعة
  Future<int> deleteGoodsData(int id) async {
    try {
      return await _databaseHelper.deleteGoodsData(id);
    } catch (e) {
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  // البحث في بيانات البضائع
  Future<List<ItemData>> searchGoodsData(String query) async {
    try {
      final db = await _databaseHelper.database;
      final results = await db.query(
        DbConstants.tableGoodsData,
        where: 'name_ar LIKE ? OR name_en LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'name_ar ASC',
      );

      return results
          .map((item) => ItemData(
                id: item[DbConstants.columnId] as int,
                nameAr: item['name_ar'] as String,
                nameEn: item['name_en'] as String,
                isSelected: false,
                quantity: 1,
                weight: 0.0,
              ))
          .toList();
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }
}
