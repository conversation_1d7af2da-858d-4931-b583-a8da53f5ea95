import 'package:flutter/material.dart';
import '../../utils/constants.dart';

/// نافذة منبثقة لعرض نتائج البحث عن رقم الهاتف
class PhoneSearchDialog extends StatelessWidget {
  final String phoneNumber;
  final List<Map<String, dynamic>> results;

  const PhoneSearchDialog({
    super.key,
    required this.phoneNumber,
    required this.results,
  });

  @override
  Widget build(BuildContext context) {
    if (results.isEmpty) {
      return _buildEmptyResultsDialog(context);
    } else {
      return _buildResultsDialog(context);
    }
  }

  // بناء نافذة عندما لا توجد نتائج
  Widget _buildEmptyResultsDialog(BuildContext context) {
    return AlertDialog(
      title: const Text(SenderInfoStrings.searchResults),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('${SenderInfoStrings.searchForPhone} $phoneNumber'),
          const SizedBox(height: 10),
          const Text(SenderInfoStrings.noMatchingRecords),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(SenderInfoStrings.close),
        ),
      ],
    );
  }

  // بناء نافذة عندما توجد نتائج
  Widget _buildResultsDialog(BuildContext context) {
    return AlertDialog(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            SenderInfoStrings.searchResults,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          // نص عدد النتائج مع تمييز الرقم بلون خلفية
          RichText(
            text: TextSpan(
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Colors.black87,
              ),
              children: [
                const TextSpan(text: 'Found '),
                WidgetSpan(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${results.length}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ),
                TextSpan(text: ' records for phone: $phoneNumber'),
              ],
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: 1100, // زيادة عرض النافذة
        height: 500, // زيادة ارتفاع النافذة
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // نص ملاحظة (tips) بلون أحمر
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline,
                      color: Colors.red.shade700, size: 18),
                  const SizedBox(width: 8),
                  const Text(
                    'Tip: You can select and copy any text or information from the list',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            // عناوين القائمة مرتبة أفقيًا
            _buildHeaderRow(),
            const Divider(thickness: 1.5, color: Colors.blue),
            Expanded(
              child: ListView.builder(
                itemCount: results.length,
                itemBuilder: (context, index) =>
                    _buildResultRow(context, results[index]),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(SenderInfoStrings.close),
        ),
      ],
    );
  }

  // بناء صف العناوين
  Widget _buildHeaderRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: _buildHeaderCell('Code', Icons.qr_code, Colors.blue),
          ),
          Expanded(
            flex: 1,
            child: _buildHeaderCell('Truck', Icons.local_shipping, Colors.blue),
          ),
          Expanded(
            flex: 2,
            child: _buildHeaderCell('Sender', Icons.person, Colors.green),
          ),
          Expanded(
            flex: 1,
            child: _buildHeaderCell('Phone', Icons.phone, Colors.green),
          ),
          Expanded(
            flex: 2,
            child: _buildHeaderCell(
                'Receiver', Icons.person_outline, Colors.orange),
          ),
          Expanded(
            flex: 1,
            child:
                _buildHeaderCell('Phone', Icons.phone_outlined, Colors.orange),
          ),
        ],
      ),
    );
  }

  // بناء خلية عنوان
  Widget _buildHeaderCell(String title, IconData icon, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: color, size: 18),
        const SizedBox(width: 4),
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
            color: color,
          ),
        ),
      ],
    );
  }

  // بناء صف نتيجة
  Widget _buildResultRow(BuildContext context, Map<String, dynamic> record) {
    // التحقق مما إذا كان السجل يحتوي على معلومات عنوان
    final hasAddressInfo = record['street_name_no'] != null &&
        record['street_name_no'].toString().isNotEmpty;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الصف الأول: المعلومات الرئيسية
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // كود
                Expanded(
                  flex: 1,
                  child: SelectableText(
                    record['code_no'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // رقم الشاحنة
                Expanded(
                  flex: 1,
                  child: SelectableText(
                    record['truck_no'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // اسم المرسل
                Expanded(
                  flex: 2,
                  child: SelectableText(
                    record['sender_name'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // رقم هاتف المرسل
                Expanded(
                  flex: 1,
                  child: SelectableText(
                    record['sender_phone'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // اسم المستلم
                Expanded(
                  flex: 2,
                  child: SelectableText(
                    record['receiver_name'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // رقم هاتف المستلم
                Expanded(
                  flex: 1,
                  child: SelectableText(
                    record['receiver_phone'] ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),

            // الصف الثاني: معلومات الموقع والعنوان
            if (hasAddressInfo ||
                record['country'] != null ||
                record['city'] != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // أيقونة الموقع
                    const Icon(Icons.location_on, color: Colors.red, size: 18),
                    const SizedBox(width: 8),

                    // معلومات الموقع والعنوان
                    Expanded(
                      child: hasAddressInfo
                          // إذا كان هناك معلومات عنوان، عرض الموقع والعنوان في نفس الصف
                          ? SelectableText.rich(
                              TextSpan(
                                children: [
                                  const TextSpan(
                                    text: 'Location: ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        '${record['country'] ?? ''}, ${record['city'] ?? ''}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: ' | Address: ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        '${record['street_name_no'] ?? ''}, ${record['postal_code'] ?? ''}, ${record['city_name'] ?? ''}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          // إذا لم يكن هناك معلومات عنوان، عرض الموقع فقط
                          : SelectableText(
                              'Location: ${record['country'] ?? ''}, ${record['city'] ?? ''}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// نافذة منبثقة لعرض مؤشر التحميل أثناء البحث
class SearchingDialog extends StatelessWidget {
  const SearchingDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(SenderInfoStrings.searching),
        ],
      ),
    );
  }
}
