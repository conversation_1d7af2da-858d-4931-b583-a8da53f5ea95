import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';

/// فئة مسؤولة عن إنشاء تصميم ملصق البريد مع الرمز البريدي
class PostLabel {
  /// تخزين مؤقت للصور المحملة لتجنب إعادة التحميل
  static final Map<String, Uint8List> _imageCache = {};

  /// تخزين مؤقت لأعلام الدول
  static final Map<String, Uint8List> _flagCache = {};

  /// إنشاء ملصق البريد
  /// يعيد قائمة من ملفات PDF منفصلة، كل واحد يمثل نسخة واحدة
  static Future<List<Uint8List>> generate({
    required String companyName,
    required Map<String, dynamic> recipientData,
    Function(String)? onProgress, // إضافة callback للتقدم
    bool forPreview = false, // معامل جديد للمعاينة
  }) async {
    debugPrint('🚀 بدء إنشاء ملصق Post Label...');
    debugPrint('وضع المعاينة: $forPreview');

    // إرسال تحديث التقدم
    onProgress?.call('تحميل الخطوط العربية...');

    debugPrint('اسم الشركة: $companyName');
    debugPrint('بيانات المستلم: ${recipientData.keys.join(', ')}');

    // طباعة قيمة الدولة تحديداً
    if (recipientData.containsKey('country')) {
      debugPrint('قيمة الدولة: "${recipientData['country']}"');
    } else {
      debugPrint('❌ لا يوجد حقل country في البيانات');
    }

    // تحميل الخطوط العربية مرة واحدة
    final Map<String, pw.Font> arabicFonts = await _loadArabicFonts();

    onProgress?.call('تحميل الصور...');

    // تحميل صورة الهيدر الجديدة فقط
    final Uint8List postHeaderImage =
        await _loadImage('assets/images/post_header.png');

    onProgress?.call('استخراج صورة العلم...');

    // استخراج صورة علم الدولة من البيانات أو تحميلها من المسار المحلي
    final Uint8List cityFlagImage = await _extractCityFlagImage(recipientData);

    onProgress?.call('تحضير إعدادات PDF...');

    // استخدام اللون الرصاصي الخفيف للحدود
    const PdfColor tableBorderColor = PdfColors.grey400;

    // رسالة تشخيص لحجم صورة العلم
    debugPrint('🔍 حجم صورة العلم المُرجعة: ${cityFlagImage.length} بايت');
    if (cityFlagImage.isNotEmpty) {
      debugPrint('✅ تم استخراج صورة العلم بنجاح');
    } else {
      debugPrint('❌ لم يتم استخراج صورة العلم - المصفوفة فارغة');
    }

    // الحصول على التاريخ الحالي
    final now = DateTime.now();
    DateFormat('yyyy-MM-dd').format(now);

    // ضبط هوامش ورقة مخصصة في اتجاه العرض (العرض = 210مم ≈ 595.3 نقطة، الارتفاع = 148مم ≈ 419.5 نقطة)
    const double customWidth = 595.28; // 210mm (عرض)
    const double customHeight = 419.53; // 148mm (ارتفاع)
    final pageWidth = customWidth; // عرض الصفحة المخصص

    // حساب العرض المتاح للمحتوى بعد طرح الهوامش الجديدة
    final availableWidth =
        pageWidth - (30.0 + 20.0); // 30px من اليسار + 20px من اليمين

    // الحصول على عدد النسخ (مجموع box_no + pallet_no) من بيانات الشحنة
    int boxCount = 0;
    int palletCount = 0;

    // الحصول على box_no
    if (recipientData['box_no'] != null &&
        recipientData['box_no'].toString().isNotEmpty &&
        recipientData['box_no'] != 'null') {
      try {
        boxCount = int.parse(recipientData['box_no'].toString());
      } catch (e) {
        debugPrint('خطأ في تحويل box_no إلى رقم: $e');
      }
    }

    // الحصول على pallet_no
    if (recipientData['pallet_no'] != null &&
        recipientData['pallet_no'].toString().isNotEmpty &&
        recipientData['pallet_no'] != 'null') {
      try {
        palletCount = int.parse(recipientData['pallet_no'].toString());
      } catch (e) {
        debugPrint('خطأ في تحويل pallet_no إلى رقم: $e');
      }
    }

    // حساب المجموع الكلي للنسخ
    int totalCount = boxCount + palletCount;

    // التأكد من أن العدد الكلي لا يقل عن 1
    totalCount = totalCount < 1 ? 1 : totalCount;

    // في وضع المعاينة، إنشاء نسخة واحدة فقط
    int copyCountToGenerate = forPreview ? 1 : totalCount;

    debugPrint('عدد الصناديق (box_no): $boxCount');
    debugPrint('عدد المنصات (pallet_no): $palletCount');
    debugPrint('العدد الكلي للنسخ: $totalCount');
    debugPrint('عدد النسخ المراد إنشاؤها: $copyCountToGenerate');

    // إنشاء مستند PDF واحد يحتوي على جميع النسخ
    final pdf = pw.Document();

    // إنشاء صفحات متعددة في نفس المستند بناءً على العدد المطلوب
    for (int copyNumber = 1; copyNumber <= copyCountToGenerate; copyNumber++) {
      // إنشاء نسخة من بيانات الشحنة مع إضافة رقم النسخة الحالية
      Map<String, dynamic> copyData = Map<String, dynamic>.from(recipientData);
      copyData['current_copy'] = copyNumber.toString();
      copyData['total_copies'] =
          totalCount.toString(); // استخدام العدد الكلي دائماً

      // إضافة صفحة جديدة للمستند
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat(customWidth,
              customHeight), // استخدام الأبعاد المخصصة 148mm × 210mm
          margin: pw.EdgeInsets.only(
            top: 20.0,
            bottom: 20.0,
            left: 20.0,
            right: 30.0,
          ),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // هيدر مصغر للاتجاه الأفقي
                pw.Container(
                  width: availableWidth,
                  child: _buildHeader(postHeaderImage),
                ),

                // المحتوى الرئيسي - جدول كبير وقسم جانبي صغير
                pw.Expanded(
                  child: pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                    children: [
                      // الجدول الرئيسي (على اليسار) - تقليل العرض قليلاً
                      pw.Container(
                        width: availableWidth *
                            0.70, // تقليل من 75% إلى 70% من العرض المتاح للجدول
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                          children: [
                            // إضافة مساحة علوية للجدول ليكون بنفس مستوى قسم البضاعة
                            pw.SizedBox(height: 12),

                            // الجدول
                            pw.Expanded(
                              child: _buildShipmentTable(
                                  copyData, arabicFonts, tableBorderColor),
                            ),
                          ],
                        ),
                      ),

                      pw.SizedBox(
                          width:
                              12), // زيادة المسافة من 8 إلى 12 لإعطاء هامش أكبر للعلم

                      // القسم الجانبي - وصف البضائع فوق العلم
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                          children: [
                            // إضافة هامش علوي لقسم وصف البضائع
                            pw.SizedBox(height: 12),

                            // وصف البضائع (بدون عنوان)
                            pw.Container(
                              height: 70, // تقليل الارتفاع لتوفير مساحة
                              child: _buildGoodsDescriptionSection(
                                  copyData, arabicFonts),
                            ),

                            pw.SizedBox(height: 6),

                            // تاريخ اليوم
                            _buildDateSection(),

                            pw.SizedBox(height: 8), // تقليل المسافة من 12 إلى 8

                            // صورة العلم - يأخذ المساحة المتبقية
                            pw.Expanded(
                              child: _buildFlagSection(cityFlagImage),
                            ),

                            pw.SizedBox(height: 2), // تقليل المسافة من 4 إلى 2

                            // رسالة التأمين
                            _buildInsuranceSection(copyData),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );
    }

    // حفظ المستند وإرجاع ملف PDF واحد
    final pdfData = await pdf.save();
    return [
      pdfData
    ]; // إرجاع قائمة تحتوي على ملف واحد للتوافق مع الواجهة الحالية
  }

  /// استخراج صورة علم الدولة من البيانات أو تحميلها من المسار المحلي
  static Future<Uint8List> _extractCityFlagImage(
      Map<String, dynamic> data) async {
    try {
      debugPrint('======== استخراج صورة علم الدولة لملصق البريد ========');

      // الحصول على رمز ISO للدولة
      String countryCode = _getCountryCodeFromData(data);
      debugPrint('رمز الدولة المستخرج: "$countryCode"');

      if (countryCode.isEmpty) {
        debugPrint(
            '❌ لم يتم العثور على رمز ISO للدولة - سيتم عرض "No flag for this country"');
        return Uint8List(0);
      }

      // التحقق من وجود العلم في التخزين المؤقت
      if (_flagCache.containsKey(countryCode)) {
        debugPrint('✅ تم استخدام علم الدولة من التخزين المؤقت: $countryCode');
        return _flagCache[countryCode]!;
      }

      // استخدام خريطة مبسطة للمسارات الأكثر استخداماً
      final Map<String, String> flagPaths = {
        'de': 'assets/images/countries/post/Germany.jpg',
        'fr': 'assets/images/countries/post/france.jpg',
        'it': 'assets/images/countries/post/italy.jpg',
        'es': 'assets/images/countries/post/spain.jpg',
        'nl': 'assets/images/countries/post/netherlands.jpg',
        'gb': 'assets/images/countries/post/united-kingdom.jpg',
        'england': 'assets/images/countries/post/england.jpg',
        'scotland': 'assets/images/countries/post/scotland.jpg',
        'wales': 'assets/images/countries/post/wales.jpg',
        'ie': 'assets/images/countries/post/ireland.jpg',
        'se': 'assets/images/countries/post/sweden.jpg',
        'no': 'assets/images/countries/post/norway.jpg',
        'fi': 'assets/images/countries/post/finland.jpg',
        'dk': 'assets/images/countries/post/denmark.jpg',
        'is': 'assets/images/countries/post/iceland.jpg',
        'at': 'assets/images/countries/post/austria.jpg',
        'ch': 'assets/images/countries/post/switzerland.jpg',
        'pl': 'assets/images/countries/post/poland.jpg',
        'cz': 'assets/images/countries/post/czech-republic.jpg',
        'sk': 'assets/images/countries/post/slovakia.jpg',
        'hu': 'assets/images/countries/post/hungary.jpg',
        'si': 'assets/images/countries/post/slovenia.jpg',
        'ua': 'assets/images/countries/post/ukraine.jpg',
        'by': 'assets/images/countries/post/belarus.jpg',
        'lt': 'assets/images/countries/post/lithuania.jpg',
        'lv': 'assets/images/countries/post/latvia.jpg',
        'ee': 'assets/images/countries/post/estonia.jpg',
        'md': 'assets/images/countries/post/moldova.jpg',
        'ge': 'assets/images/countries/post/georgia.jpg',
        'ro': 'assets/images/countries/post/romania.jpg',
        'bg': 'assets/images/countries/post/bulgaria.jpg',
        'rs': 'assets/images/countries/post/serbia.jpg',
        'hr': 'assets/images/countries/post/croatia.jpg',
        'al': 'assets/images/countries/post/albania.jpg',
        'mk': 'assets/images/countries/post/north-macedonia.jpg',
        'me': 'assets/images/countries/post/montenegro.jpg',
        'xk': 'assets/images/countries/post/kosovo.jpg',
        'gr': 'assets/images/countries/post/greece.jpg',
        'be': 'assets/images/countries/post/belgium.jpg',
        'lu': 'assets/images/countries/post/luxembourg.jpg',
        'mt': 'assets/images/countries/post/malta.jpg',
        'mc': 'assets/images/countries/post/monaco.jpg',
        'li': 'assets/images/countries/post/liechtenstein.jpg',
        'ad': 'assets/images/countries/post/andorra.jpg',
        'sm': 'assets/images/countries/post/san-marino.jpg',
        'va': 'assets/images/countries/post/vatican.jpg',
        'us': 'assets/images/countries/post/united-states-of-america.jpg',
        'ca': 'assets/images/countries/post/canada.jpg',
        'au': 'assets/images/countries/post/australia.jpg',
        'nz': 'assets/images/countries/post/new-zealand.jpg',
        'pt': 'assets/images/countries/post/portugal.jpg',
      };

      String? flagPath = flagPaths[countryCode];
      if (flagPath == null) {
        debugPrint(
            '❌ لا يوجد مسار محدد للدولة: $countryCode - سيتم عرض "No flag for this country"');
        return Uint8List(0);
      }

      try {
        final ByteData flagData = await rootBundle.load(flagPath);
        final Uint8List flagImage = flagData.buffer.asUint8List();

        // حفظ العلم في التخزين المؤقت
        _flagCache[countryCode] = flagImage;

        debugPrint('✅ تم تحميل وحفظ صورة العلم: $flagPath');
        return flagImage;
      } catch (e) {
        debugPrint('❌ فشل في تحميل صورة العلم من المسار المحلي: $e');
        debugPrint('سيتم عرض "No flag for this country" بدلاً من الصورة');
        return Uint8List(0);
      }
    } catch (e) {
      debugPrint('❌ خطأ في استخراج صورة المدينة: $e');
    }

    debugPrint('سيتم عرض "No flag for this country" لعدم توفر صورة مناسبة');
    return Uint8List(0);
  }

  /// الحصول على رمز ISO للدولة من البيانات المتوفرة
  static String _getCountryCodeFromData(Map<String, dynamic> data) {
    debugPrint('======== تحليل بيانات الدولة ========');

    // طباعة جميع البيانات المتاحة للتشخيص
    debugPrint('البيانات المتاحة:');
    data.forEach((key, value) {
      debugPrint('  $key: "$value"');
    });

    // الحصول على قيمة حقل country
    String countryValue = '';
    if (data.containsKey('country') &&
        data['country'] != null &&
        data['country'].toString().trim().isNotEmpty &&
        data['country'] != 'null') {
      countryValue = data['country'].toString().trim();
      debugPrint('قيمة حقل country: "$countryValue"');
    }

    // الدول التي يمكن استخدامها مباشرة من حقل country
    final List<String> directCountries = [
      'Germany',
      'Sweden',
      'United Kingdom',
      'Finland',
      'Netherlands'
    ];

    debugPrint('==========================================');
    debugPrint('🔍 فحص الدول المباشرة: ${directCountries.join(', ')}');

    // التحقق من وجود دولة مباشرة في حقل country
    for (String directCountry in directCountries) {
      if (countryValue.toLowerCase().contains(directCountry.toLowerCase()) ||
          directCountry.toLowerCase().contains(countryValue.toLowerCase())) {
        debugPrint(
            '✅ تم العثور على دولة مباشرة في حقل country: $directCountry');
        debugPrint('🎯 سيتم استخدام: $directCountry مباشرة');
        return _getCountryCode(directCountry);
      }
    }

    // القيم العامة التي تتطلب الاعتماد على حقل city
    final List<String> generalValues = [
      'europe post',
      'outside europe post',
      'new zealand and australia'
    ];

    debugPrint('🔍 فحص القيم العامة: ${generalValues.join(', ')}');

    // التحقق من وجود قيمة عامة في حقل country
    bool isGeneralValue = false;
    String matchedGeneralValue = '';
    for (String generalValue in generalValues) {
      if (countryValue.toLowerCase().contains(generalValue.toLowerCase())) {
        isGeneralValue = true;
        matchedGeneralValue = generalValue;
        debugPrint('✅ تم العثور على قيمة عامة في حقل country: $generalValue');
        break;
      }
    }

    String searchText = '';

    if (isGeneralValue) {
      debugPrint(
          '📍 الوضع: قيمة عامة ($matchedGeneralValue) - سيتم الاعتماد على حقل city');
      // استخدام حقل city عند وجود قيمة عامة في country
      if (data.containsKey('city') &&
          data['city'] != null &&
          data['city'].toString().trim().isNotEmpty &&
          data['city'] != 'null') {
        searchText = data['city'].toString().trim();
        debugPrint('🎯 استخدام اسم الدولة من حقل city: "$searchText"');
      } else {
        debugPrint('❌ حقل city فارغ رغم وجود قيمة عامة في country');
        debugPrint('==========================================');
        return '';
      }
    } else {
      debugPrint(
          '📍 الوضع: لا توجد دولة مباشرة أو قيمة عامة - سيتم استخدام country مباشرة');
      // استخدام حقل country مباشرة إذا لم يكن من الدول المباشرة أو القيم العامة
      if (countryValue.isNotEmpty) {
        searchText = countryValue;
        debugPrint('🎯 استخدام اسم الدولة من حقل country: "$searchText"');
      } else {
        debugPrint('📍 الوضع: حقل country فارغ - محاولة استخدام city كاحتياط');
        // في حالة عدم وجود قيمة في country، استخدم city كاحتياط
        if (data.containsKey('city') &&
            data['city'] != null &&
            data['city'].toString().trim().isNotEmpty &&
            data['city'] != 'null') {
          searchText = data['city'].toString().trim();
          debugPrint(
              '🎯 لا يوجد في country، استخدام city كاحتياط: "$searchText"');
        } else {
          debugPrint('❌ لم يتم العثور على اسم دولة في أي من الحقول');
          debugPrint('==========================================');
          return '';
        }
      }
    }

    if (searchText.isEmpty) {
      debugPrint('❌ لم يتم العثور على نص للبحث');
      debugPrint('==========================================');
      return '';
    }

    debugPrint('🚀 بدء تحويل "$searchText" إلى رمز ISO');
    // تحويل اسم الدولة إلى رمز ISO
    String result = _getCountryCode(searchText);
    debugPrint('==========================================');
    return result;
  }

  /// تحميل الصور
  static Future<Uint8List> _loadImage(String path) async {
    try {
      // التحقق من وجود الصورة في التخزين المؤقت
      if (_imageCache.containsKey(path)) {
        debugPrint('تم استخدام الصورة من التخزين المؤقت: $path');
        return _imageCache[path]!;
      }

      final ByteData data = await rootBundle.load(path);
      final Uint8List imageData = data.buffer.asUint8List();

      // حفظ الصورة في التخزين المؤقت
      _imageCache[path] = imageData;
      debugPrint('تم تحميل وحفظ الصورة في التخزين المؤقت: $path');

      return imageData;
    } catch (e) {
      debugPrint('خطأ في تحميل الصورة $path: $e');
      // إرجاع مصفوفة فارغة في حالة الخطأ
      return Uint8List(0);
    }
  }

  /// تحميل الخطوط العربية
  static Future<Map<String, pw.Font>> _loadArabicFonts() async {
    final Map<String, pw.Font> fonts = {};

    try {
      // تحميل الخط العادي
      final regularFontData =
          await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      fonts['regular'] = pw.Font.ttf(regularFontData);

      // تحميل الخط الغامق
      final boldFontData = await rootBundle
          .load('assets/fonts/NotoSansArabic_Condensed-SemiBold.ttf');
      fonts['bold'] = pw.Font.ttf(boldFontData);

      // تحميل الخط المتوسط
      final mediumFontData =
          await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      fonts['medium'] = pw.Font.ttf(mediumFontData);

      // تحميل خط Cairo للعناوين
      final cairoFontData =
          await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      fonts['cairo'] = pw.Font.ttf(cairoFontData);
    } catch (e) {
      debugPrint('خطأ في تحميل الخطوط العربية: $e');
      // في حالة حدوث خطأ، استخدم الخط الافتراضي
      fonts['regular'] = pw.Font.helvetica();
      fonts['bold'] = pw.Font.helveticaBold();
      fonts['medium'] = pw.Font.helvetica();
      fonts['cairo'] = pw.Font.helveticaBold();
    }

    return fonts;
  }

  /// إنشاء هيدر بصورة واحدة فقط
  static pw.Widget _buildHeader(Uint8List postHeaderImage) {
    return pw.Container(
      width: double.infinity,
      height: 100,
      child: postHeaderImage.isNotEmpty
          ? pw.Center(
              child: pw.Container(
                width: 600, // تحديد عرض أصغر للصورة بدلاً من العرض الكامل
                height: 100, // تحديد ارتفاع أصغر للصورة
                child: pw.Image(
                  pw.MemoryImage(postHeaderImage),
                  fit: pw.BoxFit.contain, // استخدام contain للحفاظ على النسبة
                ),
              ),
            )
          : pw.Container(
              color: PdfColors.grey200,
              child: pw.Center(
                child: pw.Text(
                  'POST HEADER',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.grey600,
                  ),
                ),
              ),
            ),
    );
  }

  // ========================================
  // منطقة دوال إنشاء الجدول - Table Building Functions
  // ========================================

  /// ارتفاع ثابت لجميع صفوف الجدول - يمكن تعديله من هنا لتغيير ارتفاع جميع الصفوف
  static const double _tableRowHeight = 22.0;

  /// حجم خط عناوين الجدول
  static const double _tableTitleFontSize = 11.0;

  /// حجم خط القيم العادية في الجدول (ما عدا رقم الكود)
  static const double _tableValueFontSize = 13.0;

  /// حجم خط رقم الكود في الجدول
  static const double _codeValueFontSize = 18.0;

  /// حجم خط حالة الدفع في الجدول
  static const double _paymentStatusFontSize = 14.0;

  /// إنشاء جدول معلومات الشحنة
  static pw.Widget _buildShipmentTable(
    Map<String, dynamic> data,
    Map<String, pw.Font> fonts,
    PdfColor borderColor,
  ) {
    return pw.Table(
      // اتجاه الجدول من اليسار إلى اليمين
      // العرض النسبي للأعمدة - تحسين توزيع المساحة
      columnWidths: {
        0: const pw.FixedColumnWidth(100), // عرض ثابت لعمود العناوين
        1: const pw.FlexColumnWidth(1), // عمود البيانات يأخذ باقي المساحة
      },
      border: pw.TableBorder(
        top: pw.BorderSide(
            color: borderColor,
            width: 0.5), // توحيد سماكة الحد العلوي مع الحدود الداخلية
        bottom: pw.BorderSide(
            color: borderColor,
            width: 0.5), // توحيد سماكة الحد السفلي مع الحدود الداخلية
        left: pw.BorderSide(
            color: borderColor,
            width: 0.5), // توحيد سماكة الحد الأيسر مع الحدود الداخلية
        right: pw.BorderSide(
            color: borderColor,
            width: 0.5), // توحيد سماكة الحد الأيمن مع الحدود الداخلية
        horizontalInside: const pw.BorderSide(
            color: PdfColors.grey500,
            width: 0.5), // الحدود الأفقية الداخلية بلون رصاصي أغمق للوضوح
        verticalInside: const pw.BorderSide(
            color: PdfColors.grey500,
            width: 0.5), // الحدود العمودية الداخلية بلون رصاصي أغمق للوضوح
      ),
      defaultVerticalAlignment: pw.TableCellVerticalAlignment
          .middle, // محاذاة الخلايا عموديًا في المنتصف
      children: [
        // صف From
        _buildTableRow('From', _buildFromValue(data), fonts),

        // صف Sender name
        _buildTableRow('Sender name', data['sender_name'] ?? '', fonts),

        // صف number of parcel - إضافة عمود "Of" و رقم الصندوق الكلي
        _buildParcelRowWithOf('number of parcel', data, fonts),

        // صف Weight KG
        _buildWeightRowWithDimensions('Weight KG', data, fonts),

        // صف To
        _buildTableRow('To', '', fonts),

        // صف Receiver Name
        _buildTableRow('Receiver Name', data['receiver_name'] ?? '', fonts),

        // صف Address
        _buildTableRow('Address', data['street_name_no'] ?? '', fonts),

        // صف Postal Code
        _buildTableRow('Postal Code', data['postal_code'] ?? '', fonts),

        // صف City Name
        _buildTableRow('City Name', data['city_name'] ?? '', fonts),

        // صف Country
        _buildTableRow(
            'Country', (data['country'] ?? '').toString().toUpperCase(), fonts),

        // صف Receiver Phone
        _buildTableRow('Receiver Phone', data['receiver_phone'] ?? '', fonts),

        // صف Code No مع رسالة الدفع
        _buildCodeRowWithPaymentStatus('Code No', data, fonts),
      ],
    );
  }

  /// إنشاء صف عادي في الجدول
  static pw.TableRow _buildTableRow(
    String title,
    dynamic value,
    Map<String, pw.Font> fonts,
  ) {
    // تحويل القيمة إلى نص بغض النظر عن نوعها
    String stringValue = '';
    if (value != null) {
      stringValue = value.toString();
    }

    // تحديد ما إذا كان هذا هو صف رقم الكود
    bool isCodeNo = title == 'Code No';

    // استخدام المتغيرات المحددة لأحجام الخطوط
    double fontSize = isCodeNo ? _codeValueFontSize : _tableValueFontSize;

    return pw.TableRow(
      children: [
        // عمود العنوان
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          padding: const pw.EdgeInsets.symmetric(horizontal: 5),
          alignment: pw.Alignment.centerLeft,
          color: PdfColors.grey200,
          child: pw.Text(
            title,
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: 9, // حجم خط العناوين يبقى كما هو
            ),
          ),
        ),

        // عمود القيمة مع الأبعاد والوزن الحجمي
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          padding: const pw.EdgeInsets.symmetric(horizontal: 5),
          alignment: pw.Alignment.centerLeft,
          child: pw.Text(
            stringValue,
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: fontSize,
            ),
          ),
        ),
      ],
    );
  }

  /// إنشاء صف خاص لعدد الطرود مع كلمة Of والعدد الكلي
  static pw.TableRow _buildParcelRowWithOf(
    String title,
    Map<String, dynamic> data,
    Map<String, pw.Font> fonts,
  ) {
    // الحصول على رقم النسخة الحالية والعدد الكلي للنسخ
    String currentCopy = data['current_copy'] ?? '1';
    String totalCopies = data['total_copies'] ?? '1';

    return pw.TableRow(
      children: [
        // عمود العنوان
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          padding: const pw.EdgeInsets.symmetric(horizontal: 5),
          alignment: pw.Alignment.centerLeft,
          color: PdfColors.grey200,
          child: pw.Text(
            title,
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: _tableTitleFontSize, // استخدام متغير حجم خط العناوين
            ),
          ),
        ),

        // عمود القيمة مع كلمة Of والعدد الكلي
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          child: pw.Row(
            children: [
              // القسم الأول: رقم النسخة الحالية
              pw.Expanded(
                flex: 1,
                child: pw.Container(
                  padding: const pw.EdgeInsets.symmetric(horizontal: 5),
                  alignment: pw.Alignment.centerLeft,
                  child: pw.Text(
                    currentCopy,
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold, // جعل الخط غامق
                      fontSize:
                          _tableValueFontSize, // استخدام متغير حجم خط القيم
                    ),
                  ),
                ),
              ),

              // القسم الثاني: كلمة Of بخط غامق
              pw.Container(
                width: 30,
                padding: const pw.EdgeInsets.symmetric(horizontal: 5),
                alignment: pw.Alignment.center,
                child: pw.Text(
                  'Of',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: _tableValueFontSize, // استخدام متغير حجم خط القيم
                  ),
                ),
              ),

              // القسم الثالث: العدد الكلي للنسخ
              pw.Expanded(
                flex: 1,
                child: pw.Container(
                  padding: const pw.EdgeInsets.symmetric(horizontal: 5),
                  alignment: pw.Alignment.centerLeft,
                  child: pw.Text(
                    totalCopies,
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold, // جعل الخط غامق
                      fontSize:
                          _tableValueFontSize, // استخدام متغير حجم خط القيم
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// إنشاء صف خاص للوزن مع عرض الأبعاد والوزن الحجمي إذا كانت متوفرة
  static pw.TableRow _buildWeightRowWithDimensions(
    String title,
    Map<String, dynamic> data,
    Map<String, pw.Font> fonts,
  ) {
    // الحصول على الوزن الحقيقي
    String realWeight = '';
    if (data.containsKey('real_weight_kg') && data['real_weight_kg'] != null) {
      realWeight = data['real_weight_kg'].toString();
    }

    // التحقق من وجود أبعاد صالحة (أكبر من 0)
    bool hasDimensions = false;
    double height = 0, length = 0, width = 0;

    if (data.containsKey('height') && data['height'] != null) {
      height = double.tryParse(data['height'].toString()) ?? 0;
    }
    if (data.containsKey('length') && data['length'] != null) {
      length = double.tryParse(data['length'].toString()) ?? 0;
    }
    if (data.containsKey('width') && data['width'] != null) {
      width = double.tryParse(data['width'].toString()) ?? 0;
    }

    // التحقق من أن جميع الأبعاد أكبر من 0
    hasDimensions = height > 0 && length > 0 && width > 0;

    // الحصول على الوزن الإضافي
    double additionalWeight = 0;
    if (data.containsKey('additional_kg') && data['additional_kg'] != null) {
      additionalWeight = double.tryParse(data['additional_kg'].toString()) ?? 0;
    }

    // إنشاء النص المطلوب عرضه
    String weightDisplayText = realWeight;

    // إضافة الوزن الإضافي بجانب الوزن الحقيقي مع علامة +
    if (additionalWeight > 0) {
      weightDisplayText =
          '$realWeight + ${additionalWeight.toStringAsFixed(2)}';
    }

    weightDisplayText += ' kg';

    // إضافة الأبعاد بين القوسين إذا كانت متوفرة
    if (hasDimensions) {
      String dimensionsText =
          '${length.toStringAsFixed(0)}×${width.toStringAsFixed(0)}×${height.toStringAsFixed(0)}';
      weightDisplayText += ' ($dimensionsText)';
    }

    return pw.TableRow(
      children: [
        // عمود العنوان
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          padding: const pw.EdgeInsets.symmetric(horizontal: 5),
          alignment: pw.Alignment.centerLeft,
          color: PdfColors.grey200,
          child: pw.Text(
            title,
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: _tableTitleFontSize, // استخدام متغير حجم خط العناوين
            ),
          ),
        ),

        // عمود القيمة مع الأبعاد والوزن الحجمي
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          padding: const pw.EdgeInsets.symmetric(horizontal: 5),
          alignment: pw.Alignment.centerLeft,
          child: pw.Text(
            weightDisplayText,
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: _tableValueFontSize, // استخدام متغير حجم خط القيم
            ),
          ),
        ),
      ],
    );
  }

  /// إنشاء صف خاص لرقم الكود مع رسالة الدفع
  static pw.TableRow _buildCodeRowWithPaymentStatus(
    String title,
    Map<String, dynamic> data,
    Map<String, pw.Font> fonts,
  ) {
    // الحصول على رقم الكود ورقم الشاحنة
    String codeValue = '';
    if (data.containsKey('code_no') && data['code_no'] != null) {
      String baseCode = data['code_no'].toString();
      String truckNumber = '';

      // الحصول على رقم الشاحنة من عمود truck_no
      if (data.containsKey('truck_no') && data['truck_no'] != null) {
        truckNumber = data['truck_no'].toString();
      }

      // تكوين رقم الكود النهائي بالتنسيق المطلوب
      if (truckNumber.isNotEmpty && truckNumber != 'null') {
        codeValue = '$baseCode-$truckNumber';
      } else {
        codeValue = baseCode;
      }

      debugPrint('رقم الكود الأساسي: $baseCode');
      debugPrint('رقم الشاحنة من truck_no: $truckNumber');
      debugPrint('رقم الكود النهائي: $codeValue');
    }

    // تحديد رسالة الدفع واللون بناءً على unpaid_amount
    String paymentMessage = '';
    PdfColor backgroundColor = PdfColors.green;

    try {
      // الحصول على قيمة unpaid_amount
      double unpaidAmount = 0.0;
      if (data.containsKey('unpaid_amount') && data['unpaid_amount'] != null) {
        String unpaidStr = data['unpaid_amount'].toString();
        if (unpaidStr.isNotEmpty && unpaidStr != 'null') {
          unpaidAmount = double.parse(unpaidStr);
        }
      }

      debugPrint('قيمة unpaid_amount: $unpaidAmount');

      // تحديد الرسالة واللون
      if (unpaidAmount != 0) {
        paymentMessage = 'PAY IN EU';
        backgroundColor = PdfColors.orange;
        debugPrint('عرض رسالة: PAY IN EU بخلفية برتقالية');
      } else {
        paymentMessage = 'ALL PAID';
        backgroundColor = PdfColors.green;
        debugPrint('عرض رسالة: ALL PAID بخلفية خضراء');
      }
    } catch (e) {
      debugPrint('خطأ في تحليل unpaid_amount: $e');
      // في حالة الخطأ، استخدم القيم الافتراضية
      paymentMessage = 'ALL PAID';
      backgroundColor = PdfColors.green;
    }

    return pw.TableRow(
      children: [
        // عمود العنوان
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          padding: const pw.EdgeInsets.symmetric(horizontal: 5),
          alignment: pw.Alignment.centerLeft,
          color: PdfColors.grey200,
          child: pw.Text(
            title,
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: _tableTitleFontSize, // استخدام متغير حجم خط العناوين
            ),
          ),
        ),

        // عمود القيمة مع رسالة الدفع
        pw.Container(
          height: _tableRowHeight, // استخدام المتغير الثابت لارتفاع الصف
          child: pw.Row(
            children: [
              // القسم الأول: رقم الكود (60% من المساحة)
              pw.Expanded(
                flex: 3,
                child: pw.Container(
                  padding: const pw.EdgeInsets.symmetric(horizontal: 5),
                  alignment: pw.Alignment.centerLeft,
                  child: pw.Text(
                    codeValue,
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize:
                          _codeValueFontSize, // استخدام متغير حجم خط الكود
                    ),
                  ),
                ),
              ),

              // القسم الثاني: رسالة الدفع مع خلفية ملونة (40% من المساحة)
              pw.Expanded(
                flex: 2,
                child: pw.Align(
                  alignment: pw.Alignment.center,
                  child: pw.Container(
                    padding: const pw.EdgeInsets.symmetric(
                        horizontal: 6, vertical: 2), // زيادة padding بسيط
                    decoration: pw.BoxDecoration(
                      color: backgroundColor,
                      borderRadius: pw.BorderRadius.circular(2),
                    ),
                    child: pw.Text(
                      paymentMessage,
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                        fontSize:
                            _paymentStatusFontSize, // استخدام متغير حجم خط حالة الدفع
                        color: PdfColors.black,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قيمة صف From مع "STERS" + اسم الفرع
  static String _buildFromValue(Map<String, dynamic> data) {
    // البحث عن اسم الفرع في البيانات
    String branchName = '';

    // البحث في الحقول المحتملة لاسم الفرع (بترتيب الأولوية)
    List<String> possibleBranchFields = [
      'current_branch', // الفرع الحالي من userProvider
      'user_branch', // من معلومات المستخدم الحالي
      'login_branch', // فرع تسجيل الدخول
      'branch_name', // اسم الفرع المباشر
      'office_name', // اسم المكتب
      'sender_branch', // فرع المرسل
      'from_branch', // الفرع المرسل منه
    ];

    for (String field in possibleBranchFields) {
      if (data.containsKey(field) &&
          data[field] != null &&
          data[field].toString().trim().isNotEmpty &&
          data[field] != 'null') {
        branchName = data[field].toString().trim();
        debugPrint('تم العثور على اسم الفرع في الحقل $field: $branchName');
        break;
      }
    }

    // إذا لم يتم العثور على اسم الفرع، استخدم قيمة افتراضية
    if (branchName.isEmpty) {
      branchName = 'Baghdad';
      debugPrint(
          'لم يتم العثور على اسم الفرع، استخدام القيمة الافتراضية: $branchName');
    }

    // إنشاء النص المطلوب: "STERS" + اسم الفرع
    String fromValue = 'STERS $branchName';
    debugPrint('قيمة صف From: $fromValue');

    return fromValue;
  }

  // ======================================== نهاية منطقة دوال الجدول ========================================

  /// تحويل اسم الدولة إلى رمز ISO لاستخدامه في API
  static String _getCountryCode(String countryName) {
    debugPrint('======== تحويل اسم الدولة إلى رمز ISO ========');
    debugPrint('الاسم المطلوب تحويله: "$countryName"');

    // قاموس مبسط للدول بالأسماء الإنجليزية فقط
    final Map<String, String> countryToCode = {
      // الدول المطلوبة بالأسماء الإنجليزية
      'germany': 'de',
      'sweden': 'se',
      'united kingdom': 'gb',
      'uk': 'gb',
      'britain': 'gb',
      'great britain': 'gb',
      'england': 'england',
      'scotland': 'scotland',
      'wales': 'wales',
      'finland': 'fi',
      'netherlands': 'nl',
      'holland': 'nl',
      'france': 'fr',
      'italy': 'it',
      'spain': 'es',
      'norway': 'no',
      'ireland': 'ie',
      'denmark': 'dk',
      'iceland': 'is',
      'austria': 'at',
      'switzerland': 'ch',
      'poland': 'pl',
      'czech republic': 'cz',
      'czechia': 'cz',
      'slovakia': 'sk',
      'hungary': 'hu',
      'slovenia': 'si',
      'ukraine': 'ua',
      'belarus': 'by',
      'lithuania': 'lt',
      'latvia': 'lv',
      'estonia': 'ee',
      'moldova': 'md',
      'georgia': 'ge',
      'romania': 'ro',
      'bulgaria': 'bg',
      'serbia': 'rs',
      'croatia': 'hr',
      'albania': 'al',
      'north macedonia': 'mk',
      'macedonia': 'mk',
      'montenegro': 'me',
      'kosovo': 'xk',
      'greece': 'gr',
      'belgium': 'be',
      'luxembourg': 'lu',
      'malta': 'mt',
      'monaco': 'mc',
      'liechtenstein': 'li',
      'andorra': 'ad',
      'san marino': 'sm',
      'vatican': 'va',
      'vatican city': 'va',
      'united states': 'us',
      'usa': 'us',
      'america': 'us',
      'canada': 'ca',
      'australia': 'au',
      'new zealand': 'nz',
      'portugal': 'pt',
    };

    // تنظيف اسم الدولة (تحويل إلى أحرف صغيرة وإزالة المسافات الزائدة)
    final cleanName = countryName.trim().toLowerCase();
    debugPrint('اسم الدولة بعد التنظيف: "$cleanName"');

    // 1. البحث المباشر
    if (countryToCode.containsKey(cleanName)) {
      final code = countryToCode[cleanName]!;
      debugPrint('✅ تم العثور على رمز الدولة: $code');
      return code;
    }

    // 2. البحث بالاحتواء
    for (var entry in countryToCode.entries) {
      if (cleanName.contains(entry.key) || entry.key.contains(cleanName)) {
        debugPrint(
            '✅ تم العثور على رمز الدولة من خلال جزء من الاسم: ${entry.value}');
        return entry.value;
      }
    }

    // 3. في حالة عدم العثور على تطابق
    debugPrint('❌ لم يتم العثور على رمز مناسب لهذه الدولة: "$countryName"');
    return '';
  }

  static pw.Widget _buildFlagSection(Uint8List cityFlagImage) {
    return pw.Container(
      width: double.infinity,
      height: 100,
      child: cityFlagImage.isNotEmpty
          ? pw.Center(
              child: pw.Container(
                width: 220, // تحديد عرض محدود للعلم
                height: 120, // تحديد ارتفاع محدود للعلم
                child: pw.Image(
                  pw.MemoryImage(cityFlagImage),
                  fit: pw.BoxFit.contain, // الحفاظ على نسبة العرض للارتفاع
                ),
              ),
            )
          : pw.Container(
              height: 35,
              alignment: pw.Alignment.center,
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey500, width: 1),
                borderRadius: pw.BorderRadius.circular(5),
                color: PdfColors.grey100,
              ),
              child: pw.Text(
                'No flag for\nthis country',
                style: pw.TextStyle(
                  fontSize: 8,
                  color: PdfColors.grey700,
                  fontWeight: pw.FontWeight.bold,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
    );
  }

  /// إنشاء قسم رسالة التأمين
  static pw.Widget _buildInsuranceSection(Map<String, dynamic> data) {
    // تحديد ما إذا كان هناك تأمين
    bool hasInsurance = false;
    try {
      if (data.containsKey('insurance_amount') &&
          data['insurance_amount'] != null) {
        String insuranceStr = data['insurance_amount'].toString();
        if (insuranceStr.isNotEmpty && insuranceStr != 'null') {
          double insuranceAmount = double.parse(insuranceStr);
          hasInsurance = insuranceAmount != 0;
          debugPrint('قيمة insurance_amount: $insuranceAmount');
          debugPrint('هل يوجد تأمين: $hasInsurance');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل insurance_amount: $e');
      hasInsurance = false;
    }

    return hasInsurance
        ? pw.Container(
            margin: const pw.EdgeInsets.only(bottom: 8), // إضافة هامش سفلي
            child: pw.Center(
              child: pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: PdfColors.green,
                  borderRadius: pw.BorderRadius.circular(3),
                ),
                child: pw.Text(
                  'INSURANCE = YES',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 11, // زيادة حجم خط حالة التأمين من 9.5 إلى 11
                    color: PdfColors.black,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
          )
        : pw.Container();
  }

  /// إنشاء قسم وصف البضائع
  static pw.Widget _buildGoodsDescriptionSection(
    Map<String, dynamic> data,
    Map<String, pw.Font> fonts,
  ) {
    // تحديد وصف البضائع من البيانات
    String goodsDescription = '';
    if (data.containsKey('goods_description') &&
        data['goods_description'] != null &&
        data['goods_description'].toString().trim().isNotEmpty &&
        data['goods_description'] != 'null') {
      goodsDescription = data['goods_description'].toString().trim();
    }

    return pw.Container(
      width: double.infinity,
      height: double.infinity,
      padding: const pw.EdgeInsets.all(6),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
        borderRadius: pw.BorderRadius.circular(3),
        color: PdfColors.grey50,
      ),
      child: goodsDescription.isNotEmpty
          ? pw.Text(
              goodsDescription,
              style: pw.TextStyle(
                font: fonts['regular'], // استخدام الخط العربي
                fontWeight: pw.FontWeight.bold, // خط عريض
                fontSize: 8, // تقليل حجم الخط للمساحة الأصغر
              ),
              textDirection:
                  pw.TextDirection.rtl, // اتجاه النص من اليمين لليسار
              textAlign: pw.TextAlign.right, // محاذاة النص لليمين
              maxLines: 6, // عدد أسطر مناسب للمساحة المحددة
              overflow: pw.TextOverflow.clip, // قطع النص في حالة النص الطويل
            )
          : pw.Center(
              child: pw.Text(
                'No description\navailable',
                style: pw.TextStyle(
                  fontSize: 7,
                  color: PdfColors.grey600,
                  fontStyle: pw.FontStyle.italic,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
    );
  }

  /// إنشاء قسم تاريخ اليوم
  static pw.Widget _buildDateSection() {
    final now = DateTime.now();
    String formattedDate = DateFormat('yyyy-MM-dd').format(now);
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
        borderRadius: pw.BorderRadius.circular(3),
      ),
      child: pw.Text(
        formattedDate, // حذف كلمة "Date:" وعرض التاريخ فقط
        style: pw.TextStyle(
          fontWeight: pw.FontWeight.bold,
          fontSize: 10, // زيادة حجم الخط من 8 إلى 10
          color: PdfColors.blue800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
}
