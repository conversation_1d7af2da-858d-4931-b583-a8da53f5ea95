import 'package:flutter_riverpod/flutter_riverpod.dart';

// نموذج بيانات الإعدادات
class AppSettings {
  final String language;

  AppSettings({
    this.language = 'en',
  });

  AppSettings copyWith({
    String? language,
  }) {
    return AppSettings(
      language: language ?? this.language,
    );
  }
}

// مزود حالة الإعدادات
class SettingsNotifier extends StateNotifier<AppSettings> {
  SettingsNotifier() : super(AppSettings());

  void setLanguage(String language) {
    state = state.copyWith(language: language);
  }
}

// مزود Riverpod للإعدادات
final settingsProvider =
    StateNotifierProvider<SettingsNotifier, AppSettings>((ref) {
  return SettingsNotifier();
});
