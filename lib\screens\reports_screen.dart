import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:euknet_company_app/services/database_helper.dart';
import 'package:euknet_company_app/services/event_bus_service.dart';
import 'package:euknet_company_app/utils/ui_helper.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:async';

// فئة لتمثيل إحصائيات الدول
class CountryStats {
  final String name;
  final int count;
  final int boxes;
  final double boxesPercentage;
  final double weight;
  final double weightPercentage;

  CountryStats({
    required this.name,
    required this.count,
    required this.boxes,
    required this.boxesPercentage,
    required this.weight,
    required this.weightPercentage,
  });
}

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  // الاشتراك في حدث EventBus
  StreamSubscription? _eventSubscription;

  DateTime? _startDate;
  DateTime? _endDate;
  String? _truckNumber;

  // متغيرات للتبويب الجديد
  DateTime? _exportStartDate;
  DateTime? _exportEndDate;
  String? _exportTruckNumber;
  String? _selectedCountry;
  String? _selectedCity;
// لتتبع التبويب الحالي

  // حالة تحميل البيانات
  bool _isLoading = false;
  bool _isLoadingTruckNumbers = false;
  bool _isLoadingCountries = false;
  bool _isLoadingCities = false;

  // بيانات التقرير
  int _totalTrucks = 0;
  int _totalBoxes = 0;
  int _totalPallets = 0;
  double _totalRealWeight = 0;
  double _totalAdditionalKg = 0;
  double _totalPaid = 0;
  double _totalUnpaidEur = 0;

  // بيانات إحصائيات الدول
  List<CountryStats> _countriesStats = [];

  // قائمة بأرقام الشاحنات
  List<String> _truckNumbers = [];
  List<String> _countries = []; // قائمة البلدان
  List<String> _cities = []; // قائمة المدن

  @override
  void initState() {
    super.initState();
    // تحميل البيانات الافتراضية (البيانات الكلية)
    _loadTruckNumbers().then((_) => _loadReportData());
    _loadCountries();

    // الاشتراك في حدث تحديث البيانات
    _subscribeToDataUpdates();
  }

  @override
  void dispose() {
    // إلغاء الاشتراك في الأحداث عند التخلص من الشاشة
    _eventSubscription?.cancel();
    super.dispose();
  }

  // دالة الاشتراك في حدث تحديث البيانات
  void _subscribeToDataUpdates() {
    final eventBus = EventBusService();
    _eventSubscription = eventBus.eventStream.listen((event) {
      if (event.type == EventType.dataRefreshNeeded) {
        debugPrint('تم استلام إشعار تحديث البيانات في شاشة التقارير');
        // تحديث بيانات التقارير وأرقام الشاحنات عند تلقي الإشعار
        if (mounted) {
          _refreshAllData();
        }
      }
    });
  }

  // دالة لتحديث جميع البيانات (أرقام الشاحنات + بيانات التقارير)
  Future<void> _refreshAllData() async {
    debugPrint('**** _refreshAllData: بدء تحديث جميع البيانات');

    try {
      // تحميل أرقام الشاحنات أولاً (مع تعطيل التحديث التلقائي للبيانات)
      await _loadTruckNumbers(autoRefreshData: false);
      // ثم تحميل بيانات التقارير بشكل منفصل
      await _loadReportData();
      debugPrint('**** _refreshAllData: انتهاء تحديث جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('**** _refreshAllData: خطأ في تحديث البيانات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating data: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // استدعاء البيانات من قاعدة البيانات
  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // محاولة الحصول على قاعدة بيانات الفرع أولاً، ثم الرئيسية
      Database db;
      try {
        db = await _databaseHelper.branchDatabase;
        debugPrint('**** _loadReportData: استخدام قاعدة بيانات الفرع');
      } catch (e) {
        debugPrint(
            '**** _loadReportData: فشل في الحصول على قاعدة بيانات الفرع، استخدام الرئيسية: $e');
        db = await _databaseHelper.database;
      }

      // إنشاء شروط البحث
      List<String> whereConditions = [];
      List<dynamic> whereArgs = [];

      if (_startDate != null) {
        whereConditions.add("date >= ?");
        whereArgs.add(DateFormat('yyyy-MM-dd').format(_startDate!));
      }

      if (_endDate != null) {
        whereConditions.add("date <= ?");
        whereArgs.add(DateFormat('yyyy-MM-dd').format(_endDate!));
      }

      if (_truckNumber != null && _truckNumber!.isNotEmpty) {
        whereConditions.add("truck_no LIKE ?");
        whereArgs.add("%$_truckNumber%");
      }

      String whereClause = whereConditions.isEmpty
          ? ""
          : "WHERE ${whereConditions.join(" AND ")}";

      // استعلام مجموع الشاحنات
      final truckResults = await db.rawQuery('''
        SELECT COUNT(DISTINCT truck_no) as total_trucks
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام مجموع الصناديق
      final boxResults = await db.rawQuery('''
        SELECT SUM(box_no) as total_boxes
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام مجموع المنصات
      final palletResults = await db.rawQuery('''
        SELECT SUM(pallet_no) as total_pallets
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام مجموع الوزن الحقيقي
      final weightResults = await db.rawQuery('''
        SELECT SUM(real_weight_kg) as total_real_weight
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام مجموع additional_kg
      final additionalKgResults = await db.rawQuery('''
        SELECT SUM(additional_kg) as total_additional_kg
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام مجموع المدفوعات
      final paidResults = await db.rawQuery('''
        SELECT SUM(total_paid) as total_paid
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام مجموع المبالغ غير المدفوعة باليورو
      final unpaidResults = await db.rawQuery('''
        SELECT SUM(unpaid_eur) as total_unpaid_eur
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
      ''', whereArgs);

      // استعلام إحصائيات الدول
      final countryStatsResults = await db.rawQuery('''
        SELECT 
          country,
          COUNT(*) as count,
          SUM(box_no) as boxes,
          SUM(real_weight_kg) as weight
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
        GROUP BY country
        ORDER BY boxes DESC
      ''', whereArgs);

      // حساب إجمالي عدد الصناديق والوزن لجميع الدول
      int totalBoxesAllCountries = boxResults.first['total_boxes'] == null
          ? 0
          : int.parse(boxResults.first['total_boxes'].toString());

      double totalWeightAllCountries = weightResults
                  .first['total_real_weight'] ==
              null
          ? 0.0
          : double.parse(weightResults.first['total_real_weight'].toString());

      // تحويل نتائج استعلام الدول إلى قائمة من كائنات CountryStats
      List<CountryStats> countriesStats = [];
      for (var countryData in countryStatsResults) {
        if (countryData['country'] == null) continue;

        int boxes = countryData['boxes'] == null
            ? 0
            : int.parse(countryData['boxes'].toString());

        double weight = countryData['weight'] == null
            ? 0.0
            : double.parse(countryData['weight'].toString());

        // تجاهل الدول التي لديها قيمة 0 في كل من الصناديق والوزن
        if (boxes <= 0 || weight <= 0) continue;

        double boxesPercentage = totalBoxesAllCountries > 0
            ? (boxes / totalBoxesAllCountries * 100)
            : 0.0;

        double weightPercentage = totalWeightAllCountries > 0
            ? (weight / totalWeightAllCountries * 100)
            : 0.0;

        countriesStats.add(
          CountryStats(
            name: countryData['country'].toString(),
            count: int.parse(countryData['count'].toString()),
            boxes: boxes,
            boxesPercentage: boxesPercentage,
            weight: weight,
            weightPercentage: weightPercentage,
          ),
        );
      }

      setState(() {
        _totalTrucks = truckResults.first['total_trucks'] == null
            ? 0
            : int.parse(truckResults.first['total_trucks'].toString());
        _totalBoxes = totalBoxesAllCountries;
        _totalPallets = palletResults.first['total_pallets'] == null
            ? 0
            : int.parse(palletResults.first['total_pallets'].toString());
        _totalRealWeight = totalWeightAllCountries;
        _totalAdditionalKg = additionalKgResults.first['total_additional_kg'] ==
                null
            ? 0.0
            : double.parse(
                additionalKgResults.first['total_additional_kg'].toString());
        _totalPaid = paidResults.first['total_paid'] == null
            ? 0.0
            : double.parse(paidResults.first['total_paid'].toString());
        _totalUnpaidEur = unpaidResults.first['total_unpaid_eur'] == null
            ? 0.0
            : double.parse(unpaidResults.first['total_unpaid_eur'].toString());
        _countriesStats = countriesStats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (!mounted) return;
      UiHelper.showSnackBar(
        context,
        'Error loading data: $e',
        isError: true,
      );
    }
  }

  // اختيار تاريخ البداية
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _startDate) {
      if (!mounted) return; // التحقق من أن الـ widget لا يزال موجودًا
      setState(() {
        _startDate = picked;
      });
      _loadReportData();
    }
  }

  // اختيار تاريخ النهاية
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _endDate) {
      if (!mounted) return; // التحقق من أن الـ widget لا يزال موجودًا
      setState(() {
        _endDate = picked;
      });
      _loadReportData();
    }
  }

  // تصميم كارت لعرض معلومات إحصائية - تصميم أبسط وأجمل بلون واحد
  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: _getIconForTitle(title, Colors.white),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: color.withValues(alpha: 0.8),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // دالة لتنسيق الأرقام مع فواصل الآلاف (مع عشرية)
  String _formatNumberWithCommas(double number) {
    final formatter = NumberFormat('#,##0.00');
    return formatter.format(number);
  }

  // دالة لتنسيق الأرقام مع فواصل الآلاف (بدون عشرية)
  String _formatNumberWithoutDecimals(double number) {
    final formatter = NumberFormat('#,##0');
    return formatter.format(number);
  }

  // دالة لبناء نص نطاق التاريخ
  String _buildDateRangeText() {
    if (_startDate != null && _endDate != null) {
      return 'From ${DateFormat('yyyy-MM-dd').format(_startDate!)} to ${DateFormat('yyyy-MM-dd').format(_endDate!)}';
    } else if (_startDate != null) {
      return 'From ${DateFormat('yyyy-MM-dd').format(_startDate!)} onwards';
    } else if (_endDate != null) {
      return 'Until ${DateFormat('yyyy-MM-dd').format(_endDate!)}';
    }
    return '';
  }

  // دالة لإرجاع الأيقونة المناسبة حسب العنوان
  Widget _getIconForTitle(String title, Color color) {
    IconData iconData;

    if (title.contains('Trucks')) {
      iconData = Icons.local_shipping;
    } else if (title.contains('Boxes')) {
      iconData = Icons.inventory_2;
    } else if (title.contains('Pallets')) {
      iconData = Icons.view_module;
    } else if (title.contains('Weight')) {
      iconData = Icons.scale;
    } else if (title.contains('Paid')) {
      iconData = Icons.payments;
    } else if (title.contains('Unpaid')) {
      iconData = Icons.money_off;
    } else {
      iconData = Icons.bar_chart;
    }

    return Icon(
      iconData,
      size: 16,
      color: color,
    );
  }

  // دالة مخصصة لبناء كارت Real Weight مع عرض Additional Kg
  Widget _buildWeightStatCard() {
    const color = Colors.orange;

    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.scale,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عرض Real Weight
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _totalRealWeight.toStringAsFixed(2),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    // عرض Additional Kg بجانب Real Weight
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: color.withValues(alpha: 0.2),
                          width: 0.5,
                        ),
                      ),
                      child: Text(
                        '+${_totalAdditionalKg.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: color.withValues(alpha: 0.8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Real Weight (kg)',
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: Color.fromRGBO(255, 152, 0, 0.8),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Reports'),
          bottom: TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            isScrollable: true,
            tabAlignment: TabAlignment.center,
            labelPadding: const EdgeInsets.symmetric(horizontal: 12),
            onTap: (index) {
              setState(() {});
            },
            tabs: const [
              Tab(
                icon: Icon(Icons.bar_chart, size: 20),
                text: 'Statistics',
                iconMargin: EdgeInsets.only(bottom: 4),
              ),
              Tab(
                icon: Icon(Icons.description, size: 20),
                text: 'Export Reports',
                iconMargin: EdgeInsets.only(bottom: 4),
              ),
            ],
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                children: [
                  // التبويب الأول: عرض الإحصائيات
                  SafeArea(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: isSmallScreen
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // فلاتر البحث (الجزء العلوي للشاشات الصغيرة)
                                  _buildFiltersCard(),
                                  const SizedBox(height: 16),
                                  // البيانات الإحصائية (الجزء السفلي للشاشات الصغيرة)
                                  _buildStatisticsSection(),
                                ],
                              )
                            : Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // البيانات الإحصائية (الجانب الأيسر للشاشات الكبيرة)
                                  Expanded(
                                    flex: 3,
                                    child: _buildStatisticsSection(),
                                  ),
                                  const SizedBox(width: 16),
                                  // فلاتر البحث (الجانب الأيمن للشاشات الكبيرة)
                                  Expanded(
                                    flex: 2,
                                    child: _buildFiltersCard(),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),

                  // التبويب الثاني: تصدير التقارير
                  SafeArea(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: _buildExportReportTab(),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // بناء جزء الإحصائيات
  Widget _buildStatisticsSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    // تحديد عدد الأعمدة بناءً على عرض الشاشة
    final crossAxisCount = screenWidth < 600 ? 2 : 3;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Summary Statistics',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              // زر التحديث في الجهة اليمنى
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadReportData,
                tooltip: 'Refresh Data',
                color: Colors.blue,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // عرض البيانات الإحصائية في صفوف - تصميم محسن للكارتات الجديدة
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.5,
            children: [
              _buildStatCard(
                  'Total Trucks', _totalTrucks.toString(), Colors.blue),
              _buildStatCard(
                  'Total Boxes', _totalBoxes.toString(), Colors.green),
              _buildStatCard(
                  'Total Pallets', _totalPallets.toString(), Colors.purple),
              _buildWeightStatCard(),
              _buildStatCard('Total Paid',
                  _formatNumberWithoutDecimals(_totalPaid), Colors.teal),
              _buildStatCard('Unpaid (EUR)',
                  _formatNumberWithCommas(_totalUnpaidEur), Colors.red),
            ],
          ),

          const SizedBox(height: 20),

          // قسم إحصائيات الدول
          if (_countriesStats.isNotEmpty) _buildCountriesStatsSection(),

          const SizedBox(height: 20),

          // إضافة زر لتصدير التقرير
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                if (mounted) {
                  UiHelper.showSnackBar(
                    context,
                    'Export functionality will be implemented soon',
                  );
                }
              },
              icon: const Icon(Icons.download),
              label: const Text('Export Report'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  // بناء قسم إحصائيات الدول
  Widget _buildCountriesStatsSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Countries Statistics',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        // نوع عرض قائمة الدول حسب حجم الشاشة
        isSmallScreen ? _buildCountriesListView() : _buildCountriesTableView(),
      ],
    );
  }

  // بناء عرض جدول الدول للشاشات الكبيرة
  Widget _buildCountriesTableView() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.blue.shade200, width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان الجدول
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade800, Colors.blue.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'Country',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 13,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Boxes',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 13,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Box %',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 13,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Weight (kg)',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 13,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Weight %',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 13,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // بيانات الدول
          ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 300),
            child: Scrollbar(
              thickness: 6,
              child: ListView.separated(
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
                itemCount: _countriesStats.length,
                separatorBuilder: (context, index) => Divider(
                  height: 1,
                  color: Colors.blue.shade100,
                  indent: 8,
                  endIndent: 8,
                ),
                itemBuilder: (context, index) {
                  final country = _countriesStats[index];
                  final isEvenRow = index % 2 == 0;

                  // حساب ألوان القيم بناءً على ترتيب الدول
                  final boxPercentColor =
                      _getColorByRank(index, _countriesStats.length);
                  final weightPercentColor =
                      _getColorByRank(index, _countriesStats.length);

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 16),
                    color: isEvenRow
                        ? Colors.white
                        : Colors.blue.shade50.withAlpha(77),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            country.name,
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: Colors.blue.shade900,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            country.boxes.toString(),
                            style: const TextStyle(fontSize: 13),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${country.boxesPercentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: boxPercentColor.withAlpha(204),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            country.weight.toStringAsFixed(1),
                            style: const TextStyle(fontSize: 13),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${country.weightPercentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: weightPercentColor.withAlpha(204),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء عرض قائمة الدول للشاشات الصغيرة
  Widget _buildCountriesListView() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.blue.shade200, width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان القائمة
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade800, Colors.blue.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Text(
              'Countries Overview',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),

          // بيانات القائمة
          ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 250),
            child: Scrollbar(
              thickness: 6,
              child: ListView.separated(
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
                itemCount: _countriesStats.length,
                separatorBuilder: (context, index) => Divider(
                  height: 1,
                  color: Colors.blue.shade100,
                  indent: 8,
                  endIndent: 8,
                ),
                itemBuilder: (context, index) {
                  final country = _countriesStats[index];
                  final isEvenRow = index % 2 == 0;

                  // حساب درجة اللون بناءً على ترتيب الدول
                  final boxPercentColor =
                      _getColorByRank(index, _countriesStats.length);
                  final weightPercentColor =
                      _getColorByRank(index, _countriesStats.length);

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 16),
                    color: isEvenRow
                        ? Colors.white
                        : Colors.blue.shade50.withAlpha(77),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.flag_rounded,
                              size: 18,
                              color: Colors.blue.shade800,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                country.name,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.blue.shade800,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: boxPercentColor.withAlpha(204),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${country.boxesPercentage.toStringAsFixed(1)}%',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Expanded(
                              child: _buildStatInfoBox('Boxes',
                                  country.boxes.toString(), boxPercentColor),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: _buildStatInfoBox(
                                  'Weight',
                                  '${country.weight.toStringAsFixed(1)} kg',
                                  weightPercentColor,
                                  percentText:
                                      '${country.weightPercentage.toStringAsFixed(1)}%'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دالة تحديد لون حسب ترتيب الدولة
  Color _getColorByRank(int index, int totalCount) {
    // الدولة الأعلى نسبة (الأولى) بلون أخضر
    if (index == 0) {
      return Colors.green;
    }
    // الدولة الثانية بلون أزرق
    else if (index == 1) {
      return Colors.blue;
    }
    // الدولة الثالثة بلون برتقالي
    else if (index == 2) {
      return Colors.orange;
    }
    // الدولة الرابعة بلون أرجواني
    else if (index == 3) {
      return Colors.purple;
    }
    // الدولة الخامسة بلون أزرق فاتح
    else if (index == 4) {
      return Colors.teal;
    }
    // الدولة الأخيرة بلون أحمر
    else if (index == totalCount - 1) {
      return Colors.red;
    }
    // باقي الدول بألوان متنوعة
    else {
      List<Color> colors = [
        Colors.amber,
        Colors.deepOrange,
        Colors.lightBlue,
        Colors.indigoAccent,
        Colors.pinkAccent,
        Colors.cyan
      ];
      return colors[index % colors.length];
    }
  }

  // بناء مربع معلومات إحصائية
  Widget _buildStatInfoBox(String label, String value, Color color,
      {String? percentText}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 2),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (percentText != null) ...[
                const SizedBox(width: 6),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: color.withAlpha(51),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    percentText,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة الفلترة
  Widget _buildFiltersCard() {
    return Card(
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Filter by Truck Number',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Default: Latest truck number',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 16),
              InkWell(
                onTap: () => _selectStartDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _startDate == null
                        ? 'Select Date'
                        : DateFormat('yyyy-MM-dd').format(_startDate!),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: () => _selectEndDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _endDate == null
                        ? 'Select Date'
                        : DateFormat('yyyy-MM-dd').format(_endDate!),
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // قائمة منسدلة لاختيار رقم الشاحنة
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Truck Number',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        // زر تحديث قائمة أرقام الشاحنات مع tooltip
                        if (!_isLoadingTruckNumbers)
                          Tooltip(
                            message: 'Refresh truck numbers',
                            child: InkWell(
                              onTap: () {
                                debugPrint(
                                    '**** تم الضغط على زر تحديث أرقام الشاحنات يدوياً');
                                _refreshAllData();
                              },
                              borderRadius: BorderRadius.circular(12),
                              child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: Icon(
                                  Icons.refresh,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const Icon(Icons.local_shipping,
                            color: Colors.grey, size: 22),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _isLoadingTruckNumbers
                              ? Row(
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.blue[300],
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    const Text(
                                      'Loading truck numbers...',
                                      style: TextStyle(
                                        color: Colors.grey,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                )
                              : _truckNumbers.isEmpty
                                  ? const Text(
                                      'No truck numbers found',
                                      style: TextStyle(
                                        color: Colors.grey,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    )
                                  : DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        isExpanded: true,
                                        isDense: true,
                                        hint: Text(
                                            'Latest: ${_truckNumbers.isNotEmpty ? _truckNumbers.first : 'N/A'}'),
                                        value: _truckNumber,
                                        items: [
                                          // خيار لإظهار جميع الشاحنات
                                          const DropdownMenuItem<String>(
                                            value: '',
                                            child: Text('🚛 All Trucks'),
                                          ),
                                          // خيارات أرقام الشاحنات مع تمييز الأحدث
                                          ..._truckNumbers
                                              .asMap()
                                              .entries
                                              .map((entry) {
                                            int index = entry.key;
                                            String number = entry.value;
                                            return DropdownMenuItem<String>(
                                              value: number,
                                              child: Text(
                                                index == 0
                                                    ? '🔢 $number (Latest)'
                                                    : '🚛 $number',
                                                style: TextStyle(
                                                  fontWeight: index == 0
                                                      ? FontWeight.bold
                                                      : FontWeight.normal,
                                                  color: index == 0
                                                      ? Colors.blue
                                                      : null,
                                                ),
                                              ),
                                            );
                                          }),
                                        ],
                                        onChanged: (value) {
                                          setState(() {
                                            // إذا كان الاختيار "جميع الشاحنات" نجعل القيمة فارغة
                                            _truckNumber =
                                                value == '' ? null : value;
                                          });
                                          // تطبيق الفلترة مباشرة عند تغيير رقم الشاحنة
                                          _loadReportData();
                                        },
                                      ),
                                    ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // عرض الرسائل التوضيحية للفلتر المختار
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  color: (_truckNumber != null ||
                          _startDate != null ||
                          _endDate != null)
                      ? Colors.blue.shade50
                      : Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: (_truckNumber != null ||
                            _startDate != null ||
                            _endDate != null)
                        ? Colors.blue.shade200
                        : Colors.red.shade200,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رسالة رقم الشاحنة
                    Row(
                      children: [
                        Icon(
                          _truckNumber != null
                              ? Icons.local_shipping
                              : Icons.warning_amber_outlined,
                          size: 16,
                          color: _truckNumber != null
                              ? Colors.blue[600]
                              : Colors.red[600],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _truckNumber != null
                              ? Text(
                                  'Truck: $_truckNumber',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              : const Text(
                                  'All trucks',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                        ),
                      ],
                    ),

                    // رسالة التاريخ (إذا تم اختيار تاريخ)
                    if (_startDate != null || _endDate != null) ...[
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          Icon(
                            Icons.date_range,
                            size: 16,
                            color: Colors.blue[600],
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _buildDateRangeText(),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _startDate = null;
                        _endDate = null;
                        _truckNumber = null;
                      });
                      _loadReportData();
                    },
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear Filters'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // استرجاع قائمة أرقام الشاحنات مع تحديد أحدث شاحنة افتراضياً
  Future<void> _loadTruckNumbers({bool autoRefreshData = true}) async {
    debugPrint(
        '**** _loadTruckNumbers: بدء تحميل أرقام الشاحنات (ترتيب حسب أكبر رقم)');
    setState(() {
      _isLoadingTruckNumbers = true;
    });

    try {
      // محاولة الحصول على قاعدة بيانات الفرع أولاً، ثم الرئيسية
      Database db;
      try {
        db = await _databaseHelper.branchDatabase;
        debugPrint('**** _loadTruckNumbers: استخدام قاعدة بيانات الفرع');
      } catch (e) {
        debugPrint(
            '**** _loadTruckNumbers: فشل في الحصول على قاعدة بيانات الفرع، استخدام الرئيسية: $e');
        db = await _databaseHelper.database;
      }
      debugPrint('**** _loadTruckNumbers: تم الحصول على قاعدة البيانات');

      // التحقق من وجود الجدول أولاً
      final tableCheck = await db.rawQuery('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='${DatabaseHelper.tableCodeData}'
      ''');

      if (tableCheck.isEmpty) {
        debugPrint(
            '**** _loadTruckNumbers: الجدول ${DatabaseHelper.tableCodeData} غير موجود');
        setState(() {
          _truckNumbers = [];
          _isLoadingTruckNumbers = false;
        });
        return;
      }

      debugPrint('**** _loadTruckNumbers: الجدول موجود، بدء الاستعلام');

      // التحقق من وجود بيانات في الجدول
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as total
        FROM ${DatabaseHelper.tableCodeData}
      ''');

      int totalRecords = countResult.first['total'] as int;
      debugPrint(
          '**** _loadTruckNumbers: إجمالي السجلات في الجدول: $totalRecords');

      // التحقق من وجود سجلات تحتوي على truck_no
      final truckCountResult = await db.rawQuery('''
        SELECT COUNT(*) as truck_count
        FROM ${DatabaseHelper.tableCodeData}
        WHERE truck_no IS NOT NULL AND truck_no != ''
      ''');

      int truckRecords = truckCountResult.first['truck_count'] as int;
      debugPrint(
          '**** _loadTruckNumbers: عدد السجلات التي تحتوي على truck_no: $truckRecords');

      // استعلام للحصول على أكبر رقم شاحنة في قاعدة البيانات
      final latestTruckResult = await db.rawQuery('''
        SELECT truck_no
        FROM ${DatabaseHelper.tableCodeData}
        WHERE truck_no IS NOT NULL AND truck_no != ''
        ORDER BY CAST(truck_no AS INTEGER) DESC
        LIMIT 1
      ''');

      debugPrint(
          '**** _loadTruckNumbers: نتيجة أحدث شاحنة: $latestTruckResult');

      // استعلام لجلب جميع أرقام الشاحنات الفريدة مرتبة حسب الرقم (من الأكبر للأصغر)
      final results = await db.rawQuery('''
        SELECT DISTINCT truck_no
        FROM ${DatabaseHelper.tableCodeData}
        WHERE truck_no IS NOT NULL AND truck_no != ''
        ORDER BY CAST(truck_no AS INTEGER) DESC
      ''');

      debugPrint(
          '**** _loadTruckNumbers: نتائج جميع الشاحنات: ${results.length} شاحنة');
      debugPrint(
          '**** _loadTruckNumbers: النتائج الأولى: ${results.take(5).toList()}');

      // تحويل النتائج إلى قائمة من النصوص
      List<String> truckNumbers = [];
      for (var row in results) {
        if (row['truck_no'] != null && row['truck_no'].toString().isNotEmpty) {
          truckNumbers.add(row['truck_no'].toString());
        }
      }

      debugPrint(
          '**** _loadTruckNumbers: قائمة أرقام الشاحنات النهائية: $truckNumbers');

      // تحديد أحدث رقم شاحنة
      String? latestTruckNumber;
      if (latestTruckResult.isNotEmpty &&
          latestTruckResult.first['truck_no'] != null) {
        latestTruckNumber = latestTruckResult.first['truck_no'].toString();
        debugPrint(
            '**** _loadTruckNumbers: أكبر رقم شاحنة من الاستعلام: $latestTruckNumber');
      } else if (truckNumbers.isNotEmpty) {
        latestTruckNumber = truckNumbers.first;
        debugPrint(
            '**** _loadTruckNumbers: أكبر رقم شاحنة من القائمة: $latestTruckNumber');
      }

      // حفظ الأرقام القديمة للمقارنة
      String? oldTruckNumber = _truckNumber;
      String? oldExportTruckNumber = _exportTruckNumber;

      setState(() {
        _truckNumbers = truckNumbers;
        _isLoadingTruckNumbers = false;

        // التحقق من وجود الشاحنة المحددة حالياً في تبويب Statistics
        if (_truckNumber != null && !truckNumbers.contains(_truckNumber)) {
          debugPrint(
              '**** _loadTruckNumbers: الشاحنة المحددة حالياً في Statistics ($_truckNumber) لم تعد موجودة');
          _truckNumber = null; // إزالة التحديد الحالي
        }

        // التحقق من وجود الشاحنة المحددة حالياً في تبويب Export Reports
        if (_exportTruckNumber != null &&
            !truckNumbers.contains(_exportTruckNumber)) {
          debugPrint(
              '**** _loadTruckNumbers: الشاحنة المحددة حالياً في Export Reports ($_exportTruckNumber) لم تعد موجودة');
          _exportTruckNumber = null; // إزالة التحديد الحالي
        }

        // تعيين أكبر رقم شاحنة كقيمة افتراضية لتبويب Statistics
        if ((_truckNumber == null || !truckNumbers.contains(_truckNumber)) &&
            latestTruckNumber != null) {
          _truckNumber = latestTruckNumber;
          debugPrint(
              '**** _loadTruckNumbers: تم تعيين أكبر رقم شاحنة لتبويب Statistics إلى: $latestTruckNumber');
        }

        // تعيين أكبر رقم شاحنة كقيمة افتراضية لتبويب Export Reports
        if ((_exportTruckNumber == null ||
                !truckNumbers.contains(_exportTruckNumber)) &&
            latestTruckNumber != null) {
          _exportTruckNumber = latestTruckNumber;
          debugPrint(
              '**** _loadTruckNumbers: تم تعيين أكبر رقم شاحنة لتبويب Export Reports إلى: $latestTruckNumber');
        }
      });

      // إذا تغير رقم الشاحنة المحدد وكان autoRefreshData مفعلاً، نحدث البيانات
      if ((oldTruckNumber != _truckNumber ||
              oldExportTruckNumber != _exportTruckNumber) &&
          autoRefreshData) {
        debugPrint(
            '**** _loadTruckNumbers: أرقام الشاحنات تغيرت - Statistics: $oldTruckNumber -> $_truckNumber, Export: $oldExportTruckNumber -> $_exportTruckNumber، سيتم تحديث البيانات');
        _loadReportData();
      }

      debugPrint('**** _loadTruckNumbers: انتهاء التحميل بنجاح');
    } catch (e) {
      setState(() {
        _isLoadingTruckNumbers = false;
      });

      debugPrint('**** _loadTruckNumbers: خطأ في تحميل أرقام الشاحنات: $e');
      debugPrint('**** _loadTruckNumbers: Stack trace: ${StackTrace.current}');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        UiHelper.showSnackBar(
          context,
          'خطأ في تحميل أرقام الشاحنات: $e',
          isError: true,
        );
      }
    }
  }

  // استرجاع قائمة البلدان المتوفرة
  Future<void> _loadCountries() async {
    setState(() {
      _isLoadingCountries = true;
    });

    try {
      final db = await _databaseHelper.database;

      // استعلام لجلب البلدان الفريدة
      final results = await db.rawQuery('''
        SELECT DISTINCT country
        FROM ${DatabaseHelper.tableCodeData}
        WHERE country IS NOT NULL AND country != ''
        ORDER BY country
      ''');

      // تحويل النتائج إلى قائمة من النصوص
      List<String> countries = [];
      for (var row in results) {
        if (row['country'] != null && row['country'].toString().isNotEmpty) {
          countries.add(row['country'].toString());
        }
      }

      setState(() {
        _countries = countries;
        _isLoadingCountries = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCountries = false;
      });
      debugPrint('Error loading countries list: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        UiHelper.showSnackBar(
          context,
          'Error loading countries list: $e',
          isError: true,
        );
      }
    }
  }

  // استرجاع قائمة المدن المتوفرة حسب البلد المحدد
  Future<void> _loadCities({String? country}) async {
    setState(() {
      _isLoadingCities = true;
    });

    try {
      final db = await _databaseHelper.database;

      // استعلام لجلب المدن الفريدة حسب البلد
      final String whereClause = country != null ? 'WHERE country = ?' : '';
      final List<dynamic> whereArgs = country != null ? [country] : [];

      final results = await db.rawQuery('''
        SELECT DISTINCT city
        FROM ${DatabaseHelper.tableCodeData}
        $whereClause
        ORDER BY city
      ''', whereArgs);

      // تحويل النتائج إلى قائمة من النصوص
      List<String> cities = [];
      for (var row in results) {
        if (row['city'] != null && row['city'].toString().isNotEmpty) {
          cities.add(row['city'].toString());
        }
      }

      setState(() {
        _cities = cities;
        _isLoadingCities = false;
        // إعادة تعيين المدينة المحددة عند تغيير البلد
        if (country != null) {
          _selectedCity = null;
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingCities = false;
      });
      debugPrint('Error loading cities list: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        UiHelper.showSnackBar(
          context,
          'Error loading cities list: $e',
          isError: true,
        );
      }
    }
  }

  // تصدير البيانات إلى ملف إكسل
  Future<void> _exportToExcel() async {
    if (mounted) {
      UiHelper.showSnackBar(
        context,
        'Export functionality will be implemented soon',
      );
    }
  }

  // بناء تبويب تصدير التقارير
  Widget _buildExportReportTab() {
    return Card(
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Export Data to Excel File',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // حقل تاريخ البداية
            InkWell(
              onTap: () => _selectExportStartDate(context),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'From Date',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _exportStartDate == null
                      ? 'Select Start Date'
                      : DateFormat('yyyy-MM-dd').format(_exportStartDate!),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // حقل تاريخ النهاية
            InkWell(
              onTap: () => _selectExportEndDate(context),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'To Date',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _exportEndDate == null
                      ? 'Select End Date'
                      : DateFormat('yyyy-MM-dd').format(_exportEndDate!),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // قائمة رقم الشاحنة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Truck Number',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      // زر تحديث قائمة أرقام الشاحنات
                      if (!_isLoadingTruckNumbers)
                        InkWell(
                          onTap: _loadTruckNumbers,
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Icon(
                              Icons.refresh,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(Icons.local_shipping,
                          color: Colors.grey, size: 22),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _isLoadingTruckNumbers
                            ? Row(
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.blue[300],
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Loading truck numbers...',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              )
                            : _truckNumbers.isEmpty
                                ? const Text(
                                    'No truck numbers found',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                : DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                      isExpanded: true,
                                      isDense: true,
                                      hint: const Text('Select Truck Number'),
                                      value: _exportTruckNumber,
                                      items: [
                                        const DropdownMenuItem<String>(
                                          value: '',
                                          child: Text('All Trucks'),
                                        ),
                                        ..._truckNumbers.map((String number) {
                                          return DropdownMenuItem<String>(
                                            value: number,
                                            child: Text(number),
                                          );
                                        }),
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          _exportTruckNumber =
                                              value == '' ? null : value;
                                        });
                                      },
                                    ),
                                  ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // قائمة البلدان
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Country',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(Icons.flag, color: Colors.grey, size: 22),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _isLoadingCountries
                            ? Row(
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.blue[300],
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Loading countries...',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              )
                            : _countries.isEmpty
                                ? const Text(
                                    'No countries found',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                : DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                      isExpanded: true,
                                      isDense: true,
                                      hint: const Text('Select Country'),
                                      value: _selectedCountry,
                                      items: [
                                        const DropdownMenuItem<String>(
                                          value: '',
                                          child: Text('All Countries'),
                                        ),
                                        ..._countries.map((String country) {
                                          return DropdownMenuItem<String>(
                                            value: country,
                                            child: Text(country),
                                          );
                                        }),
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedCountry =
                                              value == '' ? null : value;
                                        });
                                        // تحميل المدن المتعلقة بالبلد المحدد
                                        _loadCities(country: _selectedCountry);
                                      },
                                    ),
                                  ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // قائمة المدن
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'City',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(Icons.location_city,
                          color: Colors.grey, size: 22),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _isLoadingCities
                            ? Row(
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.blue[300],
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Loading cities...',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              )
                            : _cities.isEmpty
                                ? const Text(
                                    'No cities found',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                : DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                      isExpanded: true,
                                      isDense: true,
                                      hint: const Text('Select City'),
                                      value: _selectedCity,
                                      items: [
                                        const DropdownMenuItem<String>(
                                          value: '',
                                          child: Text('All Cities'),
                                        ),
                                        ..._cities.map((String city) {
                                          return DropdownMenuItem<String>(
                                            value: city,
                                            child: Text(city),
                                          );
                                        }),
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedCity =
                                              value == '' ? null : value;
                                        });
                                      },
                                    ),
                                  ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // زر تصدير إلى إكسل
            Center(
              child: ElevatedButton.icon(
                onPressed: _exportToExcel,
                icon: const Icon(Icons.file_download),
                label: const Text('Export to Excel'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  backgroundColor: Colors.green.shade600,
                  foregroundColor: Colors.white,
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // زر إعادة تعيين الفلاتر
            Center(
              child: TextButton.icon(
                onPressed: () {
                  setState(() {
                    _exportStartDate = null;
                    _exportEndDate = null;
                    _exportTruckNumber = null;
                    _selectedCountry = null;
                    _selectedCity = null;
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('Reset All Filters'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // اختيار تاريخ البداية للتصدير
  Future<void> _selectExportStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _exportStartDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _exportStartDate) {
      if (!mounted) return;
      setState(() {
        _exportStartDate = picked;
      });
    }
  }

  // اختيار تاريخ النهاية للتصدير
  Future<void> _selectExportEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _exportEndDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _exportEndDate) {
      if (!mounted) return;
      setState(() {
        _exportEndDate = picked;
      });
    }
  }
}
