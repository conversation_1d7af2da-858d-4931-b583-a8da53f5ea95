import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/print_settings_model.dart';
import '../services/print_settings_database_service.dart';
import '../services/printer_service.dart';

/// نافذة إعدادات الطباعة المنبثقة
class PrintSettingsDialog extends StatefulWidget {
  final String documentType;
  final PrintSettingsModel? initialSettings;
  final Function(PrintSettingsModel)? onSettingsSaved;

  const PrintSettingsDialog({
    super.key,
    required this.documentType,
    this.initialSettings,
    this.onSettingsSaved,
  });

  @override
  State<PrintSettingsDialog> createState() => _PrintSettingsDialogState();
}

class _PrintSettingsDialogState extends State<PrintSettingsDialog> {
  final _formKey = GlobalKey<FormState>();
  final _databaseService = PrintSettingsDatabaseService();

  // متحكمات النصوص
  final _marginTopController = TextEditingController();
  final _marginBottomController = TextEditingController();
  final _marginLeftController = TextEditingController();
  final _marginRightController = TextEditingController();
  final _copiesController = TextEditingController();

  // المتغيرات
  List<String> _availablePrinters = [];
  String? _selectedPrinter;
  String _selectedPaperSize = 'A4';
  String _selectedOrientation = 'Portrait';
  int _selectedDpi = 300;
  bool _isLoading = false;
  bool _isLoadingPrinters = true;

  // متغيرات الرسائل الداخلية
  String? _inDialogMessage;
  Color? _inDialogMessageColor;
  bool _showInDialogMessageFlag = false;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
    _loadPrinters();
  }

  @override
  void dispose() {
    _marginTopController.dispose();
    _marginBottomController.dispose();
    _marginLeftController.dispose();
    _marginRightController.dispose();
    _copiesController.dispose();
    super.dispose();
  }

  /// تهيئة الإعدادات
  void _initializeSettings() {
    final settings = widget.initialSettings ??
        DefaultPrintSettings.getDefaultForDocumentType(widget.documentType);

    _selectedPrinter = settings.printerName;
    _selectedPaperSize = settings.paperSize;
    _selectedOrientation = settings.orientation;
    _selectedDpi = settings.dpi;

    _marginTopController.text = settings.marginTop.toString();
    _marginBottomController.text = settings.marginBottom.toString();
    _marginLeftController.text = settings.marginLeft.toString();
    _marginRightController.text = settings.marginRight.toString();
    _copiesController.text = settings.copies.toString();
  }

  /// تحميل قائمة الطابعات
  Future<void> _loadPrinters() async {
    setState(() {
      _isLoadingPrinters = true;
    });

    try {
      final printers = await PrinterService.getInstalledPrinters();
      final defaultPrinter = await PrinterService.getDefaultPrinter();

      setState(() {
        _availablePrinters = printers;
        if (_selectedPrinter == null && defaultPrinter != null) {
          _selectedPrinter = defaultPrinter;
        }
        _isLoadingPrinters = false;
      });
    } catch (e) {
      setState(() {
        _availablePrinters = ['الطابعة الافتراضية'];
        _selectedPrinter = 'الطابعة الافتراضية';
        _isLoadingPrinters = false;
      });

      // سيتم عرض رسالة الخطأ في واجهة المستخدم بدلاً من SnackBar
      debugPrint('خطأ في تحميل قائمة الطابعات: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final settings = PrintSettingsModel(
        documentType: widget.documentType,
        printerName: _selectedPrinter,
        paperSize: _selectedPaperSize,
        orientation: _selectedOrientation,
        marginTop: double.parse(_marginTopController.text),
        marginBottom: double.parse(_marginBottomController.text),
        marginLeft: double.parse(_marginLeftController.text),
        marginRight: double.parse(_marginRightController.text),
        dpi: _selectedDpi,
        copies: int.parse(_copiesController.text),
        isDefault: widget.documentType == 'invoice', // الفاتورة افتراضية
      );

      await _databaseService.savePrintSettings(settings);

      if (widget.onSettingsSaved != null) {
        widget.onSettingsSaved!(settings);
      }

      if (mounted) {
        // عرض رسالة نجاح داخل النافذة المنبثقة
        _showInDialogMessage('تم حفظ إعدادات الطباعة بنجاح', Colors.green);
        // تأخير قصير قبل الإغلاق لإظهار الرسالة
        await Future.delayed(const Duration(milliseconds: 1500));
        if (mounted) {
          Navigator.of(context).pop(settings);
        }
      }
    } catch (e) {
      if (mounted) {
        // عرض رسالة خطأ داخل النافذة المنبثقة
        _showInDialogMessage('خطأ في حفظ الإعدادات: $e', Colors.red);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إعادة تعيين الإعدادات إلى القيم الافتراضية
  void _resetToDefaults() {
    final defaultSettings =
        DefaultPrintSettings.getDefaultForDocumentType(widget.documentType);

    setState(() {
      _selectedPaperSize = defaultSettings.paperSize;
      _selectedOrientation = defaultSettings.orientation;
      _selectedDpi = defaultSettings.dpi;

      _marginTopController.text = defaultSettings.marginTop.toString();
      _marginBottomController.text = defaultSettings.marginBottom.toString();
      _marginLeftController.text = defaultSettings.marginLeft.toString();
      _marginRightController.text = defaultSettings.marginRight.toString();
      _copiesController.text = defaultSettings.copies.toString();
    });
  }

  /// تحديث قائمة الطابعات
  Future<void> _refreshPrinters() async {
    await _loadPrinters();
  }

  /// عرض رسالة داخل النافذة المنبثقة
  void _showInDialogMessage(String message, Color color) {
    setState(() {
      _inDialogMessage = message;
      _inDialogMessageColor = color;
      _showInDialogMessageFlag = true;
    });

    // إخفاء الرسالة بعد 3 ثوانٍ
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showInDialogMessageFlag = false;
        });
      }
    });
  }

  /// الحصول على اسم نوع المستند للعرض
  String _getDocumentTypeDisplayName() {
    switch (widget.documentType) {
      case 'invoice':
        return 'الفاتورة';
      case 'office_label':
        return 'ملصق المكتب';
      case 'post_label':
        return 'ملصق البريد';
      default:
        return widget.documentType;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // شريط العنوان
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.blue[700],
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'إعدادات الطباعة - ${_getDocumentTypeDisplayName()}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            const Divider(height: 24),

            // المحتوى
            Expanded(
              child: _isLoadingPrinters
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('جاري تحميل قائمة الطابعات...'),
                        ],
                      ),
                    )
                  : Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            _buildPrinterSection(),
                            const SizedBox(height: 20),
                            _buildPaperSection(),
                            const SizedBox(height: 20),
                            _buildMarginsSection(),
                            const SizedBox(height: 20),
                            _buildAdvancedSection(),
                          ],
                        ),
                      ),
                    ),
            ),

            const Divider(height: 24),

            // عرض الرسائل الداخلية
            if (_showInDialogMessageFlag && _inDialogMessage != null)
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _inDialogMessageColor?.withAlpha(
                      25), // Using withAlpha for 10% opacity (255 * 0.1 ≈ 25)
                  border: Border.all(
                    color: _inDialogMessageColor ?? Colors.grey,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _inDialogMessageColor == Colors.green
                          ? Icons.check_circle
                          : Icons.error,
                      color: _inDialogMessageColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _inDialogMessage!,
                        style: TextStyle(
                          color: _inDialogMessageColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // أزرار الإجراءات
            Row(
              children: [
                TextButton.icon(
                  onPressed: _resetToDefaults,
                  icon: const Icon(Icons.restore),
                  label: const Text('إعادة تعيين'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.orange,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _saveSettings,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.save),
                  label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[700],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم اختيار الطابعة
  Widget _buildPrinterSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.print, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات الطابعة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh, size: 20),
                  onPressed: _refreshPrinters,
                  tooltip: 'تحديث',
                ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _selectedPrinter,
              decoration: const InputDecoration(
                labelText: 'اختيار الطابعة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.print),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: _availablePrinters.map((printer) {
                return DropdownMenuItem(
                  value: printer,
                  child: Text(printer),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPrinter = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار طابعة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  /// قسم إعدادات الورقة
  Widget _buildPaperSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.description, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'إعدادات الورقة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPaperSize,
                    decoration: const InputDecoration(
                      labelText: 'حجم الورقة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.crop_din),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items:
                        PrintSettingsModel.getSupportedPaperSizes().map((size) {
                      final displayNames =
                          PrintSettingsModel.getPaperSizeDisplayNames();
                      return DropdownMenuItem(
                        value: size,
                        child: Text(displayNames[size] ?? size),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedPaperSize = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedOrientation,
                    decoration: const InputDecoration(
                      labelText: 'اتجاه الورقة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.rotate_90_degrees_ccw),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: PrintSettingsModel.getSupportedOrientations()
                        .map((orientation) {
                      final displayNames =
                          PrintSettingsModel.getOrientationDisplayNames();
                      return DropdownMenuItem(
                        value: orientation,
                        child: Text(displayNames[orientation] ?? orientation),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedOrientation = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الهوامش
  Widget _buildMarginsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.border_outer, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'الهوامش (مم)',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _marginTopController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش العلوي',
                      border: OutlineInputBorder(),
                      suffixText: 'مم',
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0 || margin > 50) {
                        return '0-50 مم';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _marginBottomController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش السفلي',
                      border: OutlineInputBorder(),
                      suffixText: 'مم',
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0 || margin > 50) {
                        return '0-50 مم';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _marginLeftController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش الأيسر',
                      border: OutlineInputBorder(),
                      suffixText: 'مم',
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0 || margin > 50) {
                        return '0-50 مم';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _marginRightController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش الأيمن',
                      border: OutlineInputBorder(),
                      suffixText: 'مم',
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0 || margin > 50) {
                        return '0-50 مم';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الإعدادات المتقدمة
  Widget _buildAdvancedSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.tune, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'إعدادات متقدمة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedDpi,
                    decoration: const InputDecoration(
                      labelText: 'دقة الطباعة (DPI)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.high_quality),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 150, child: Text('150 DPI - عادية')),
                      DropdownMenuItem(
                          value: 300, child: Text('300 DPI - عالية')),
                      DropdownMenuItem(
                          value: 600, child: Text('600 DPI - عالية جداً')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedDpi = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _copiesController,
                    decoration: const InputDecoration(
                      labelText: 'عدد النسخ',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.content_copy),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final copies = int.tryParse(value);
                      if (copies == null || copies < 1 || copies > 100) {
                        return '1-100';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
