import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/database_helper.dart';

class BranchesTab extends StatefulWidget {
  final DatabaseHelper databaseHelper;

  const BranchesTab({super.key, required this.databaseHelper});

  @override
  State<BranchesTab> createState() => _BranchesTabState();
}

class _BranchesTabState extends State<BranchesTab>
    with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> _branches = [];
  List<Map<String, dynamic>> _filteredBranches = [];
  Map<int, List<Map<String, dynamic>>> _branchUsers = {};

  bool _isLoading = true;
  late TabController _tabController;

  // Controllers for search
  final TextEditingController _branchSearchController = TextEditingController();
  final TextEditingController _userSearchController = TextEditingController();

  // Controllers for branch fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _codePrefixController = TextEditingController();
  final TextEditingController _codeDigitsController = TextEditingController();

  // Controllers for user fields
  final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _userEmailController = TextEditingController();
  final TextEditingController _userPasswordController = TextEditingController();
  String _selectedRole = 'user';

  final List<String> _roles = ['user', 'admin', 'manager'];

  // دالة لعرض اسم الدور بحرف كبير أول
  String _getRoleDisplayName(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Admin';
      case 'manager':
        return 'Manager';
      case 'user':
        return 'User';
      default:
        return 'User';
    }
  }

  // التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    if (email.trim().isEmpty) return true; // البريد الإلكتروني اختياري
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email.trim());
  }

  // التحقق من الحروف الإنجليزية فقط (مع السماح بالمسافات والأرقام)
  bool _isValidEnglishText(String text) {
    return RegExp(r'^[a-zA-Z0-9\s._-]+$').hasMatch(text.trim());
  }

  // التحقق من اسم المستخدم (حروف إنجليزية وأرقام فقط بدون مسافات)
  bool _isValidUsername(String username) {
    return RegExp(r'^[a-zA-Z0-9._-]+$').hasMatch(username.trim());
  }

  // إنشاء بريد إلكتروني افتراضي بناء على اسم المستخدم
  Future<String> _generateUniqueDefaultEmail(String username) async {
    // تنظيف اسم المستخدم: إزالة المسافات وتحويل إلى أحرف صغيرة
    String cleanUsername = username.trim().toLowerCase().replaceAll(' ', '');

    // التأكد من أن اسم المستخدم يحتوي على حروف إنجليزية فقط
    cleanUsername = cleanUsername.replaceAll(RegExp(r'[^a-z0-9._-]'), '');

    // التأكد من وجود محتوى صالح
    if (cleanUsername.isEmpty) {
      cleanUsername = 'user${DateTime.now().millisecondsSinceEpoch}';
    }

    // البحث عن إيميل فريد
    String baseEmail = '$<EMAIL>';
    String finalEmail = baseEmail;
    int counter = 1;

    final existingUsers = await widget.databaseHelper.getUsers();

    while (existingUsers.any((user) =>
        user['email']?.toString().toLowerCase() == finalEmail.toLowerCase())) {
      finalEmail = '$cleanUsername$<EMAIL>';
      counter++;
    }

    return finalEmail;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
    _loadBranches();
    _loadUsers();
  }

  @override
  void dispose() {
    _branchSearchController.dispose();
    _userSearchController.dispose();
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _codePrefixController.dispose();
    _codeDigitsController.dispose();
    _userNameController.dispose();
    _userEmailController.dispose();
    _userPasswordController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBranches() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على جميع الفروع
      final branches = await widget.databaseHelper.getBranches();

      setState(() {
        _branches = branches;
        _filteredBranches = branches;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('error in loading branches: $e');
    }
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على جميع المستخدمين
      final users = await widget.databaseHelper.getUsers();

      // تنظيم المستخدمين حسب الفرع
      Map<int, List<Map<String, dynamic>>> branchUsers = {};

      for (var user in users) {
        int branchId = user['branch_id'] ?? -1;
        if (!branchUsers.containsKey(branchId)) {
          branchUsers[branchId] = [];
        }
        branchUsers[branchId]!.add(user);
      }

      setState(() {
        _branchUsers = branchUsers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('error in loading users: $e');
    }
  }

  void _filterBranches(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredBranches = _branches;
      } else {
        _filteredBranches = _branches
            .where((branch) =>
                branch['name']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                branch['address']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                branch['phone']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                branch['email']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  Future<void> _addBranch() async {
    _nameController.clear();
    _addressController.clear();
    _phoneController.clear();
    _emailController.clear();
    _codePrefixController.clear();
    _codeDigitsController.text = '25'; // قيمة افتراضية
    _userNameController.clear();
    _userEmailController.clear();
    _userPasswordController.clear();
    _selectedRole = 'user';

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: const BoxConstraints(maxWidth: 800),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان النافذة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Add New Branch',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(thickness: 1),
              const SizedBox(height: 16),

              // محتوى النافذة
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات الفرع - القسم الأول
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.shade100),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(Icons.business, color: Colors.blue),
                                SizedBox(width: 8),
                                Text(
                                  'Branch Information',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // رقمين في صف واحد
                            Row(
                              children: [
                                // اسم الفرع
                                Expanded(
                                  flex: 2,
                                  child: TextField(
                                    controller: _nameController,
                                    decoration: InputDecoration(
                                      labelText: 'Branch Name',
                                      hintText: 'Enter branch name',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.store),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                        RegExp(r'[a-zA-Z0-9\s._-]'),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // رقم الهاتف
                                Expanded(
                                  flex: 1,
                                  child: TextField(
                                    controller: _phoneController,
                                    decoration: InputDecoration(
                                      labelText: 'Phone',
                                      hintText: 'Enter phone number',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.phone),
                                    ),
                                    keyboardType: TextInputType.phone,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // العنوان
                            TextField(
                              controller: _addressController,
                              decoration: InputDecoration(
                                labelText: 'Address',
                                hintText: 'Enter branch address',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                prefixIcon: const Icon(Icons.location_on),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // تنسيق الكود في صف واحد
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _codePrefixController,
                                    decoration: InputDecoration(
                                      labelText: 'Code Prefix',
                                      hintText: 'e.g. XXXX',
                                      helperText: 'First 1-4 letters in code',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.code),
                                    ),
                                    maxLength: 4,
                                    textCapitalization:
                                        TextCapitalization.characters,
                                    buildCounter: (BuildContext context,
                                            {int? currentLength,
                                            int? maxLength,
                                            bool? isFocused}) =>
                                        null,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextField(
                                    controller: _codeDigitsController,
                                    decoration: InputDecoration(
                                      labelText: 'Code Digits',
                                      hintText: 'e.g. 25',
                                      helperText: 'Middle 2 digits in code',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.numbers),
                                    ),
                                    maxLength: 2,
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    buildCounter: (BuildContext context,
                                            {int? currentLength,
                                            int? maxLength,
                                            bool? isFocused}) =>
                                        null,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // معلومات المستخدم - القسم الثاني
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.green.shade100),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(Icons.person, color: Colors.green),
                                SizedBox(width: 8),
                                Text(
                                  'Basic User Information',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                    color: Colors.green,
                                  ),
                                ),
                                Spacer(),
                                Text(
                                  '(Optional)',
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // اسم المستخدم
                            TextField(
                              controller: _userNameController,
                              decoration: InputDecoration(
                                labelText: 'User Name',
                                hintText: 'Enter user name',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                prefixIcon: const Icon(Icons.person),
                              ),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'[a-zA-Z0-9._-]'),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // البريد الإلكتروني وكلمة المرور
                            Row(
                              children: [
                                // البريد الإلكتروني
                                Expanded(
                                  child: TextField(
                                    controller: _userEmailController,
                                    decoration: InputDecoration(
                                      labelText: 'Email',
                                      hintText: 'Enter email',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.email),
                                    ),
                                    keyboardType: TextInputType.emailAddress,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                        RegExp(r'[a-zA-Z0-9@._-]'),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // كلمة المرور
                                Expanded(
                                  child: TextField(
                                    controller: _userPasswordController,
                                    decoration: InputDecoration(
                                      labelText: 'Password',
                                      hintText: 'Enter password',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.lock),
                                    ),
                                    obscureText: true,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // الدور
                            DropdownButtonFormField<String>(
                              value: _selectedRole,
                              decoration: InputDecoration(
                                labelText: 'Role',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                prefixIcon:
                                    const Icon(Icons.admin_panel_settings),
                              ),
                              items: _roles.map((role) {
                                return DropdownMenuItem<String>(
                                  value: role,
                                  child: Text(_getRoleDisplayName(role)),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedRole = value!;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveBranch(context),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Save Branch'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _editBranch(Map<String, dynamic> branch) async {
    // حفظ القيم الأصلية للتحقق من التغييرات لاحقًا

    _nameController.text = branch['name'] ?? '';
    _addressController.text = branch['address'] ?? '';
    _phoneController.text = branch['phone'] ?? '';
    _emailController.text = branch['email'] ?? '';
    _codePrefixController.text = branch['code_prefix'] ?? '';
    _codeDigitsController.text = branch['code_digits'] ?? '25';

    // البحث عن المستخدم الأساسي للفرع (إذا وجد)
    final branchUsers = _branchUsers[branch['id']] ?? [];
    Map<String, dynamic>? primaryUser;

    if (branchUsers.isNotEmpty) {
      // اختيار أول مستخدم كمستخدم أساسي
      primaryUser = branchUsers.first;
      _userNameController.text = primaryUser['name'] ?? '';
      _userEmailController.text = primaryUser['email'] ?? '';
      _userPasswordController.text = ''; // لا تعرض كلمة المرور
      _selectedRole = primaryUser['role'] ?? 'user';
    } else {
      _userNameController.clear();
      _userEmailController.clear();
      _userPasswordController.clear();
      _selectedRole = 'user';
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: const BoxConstraints(maxWidth: 800),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان النافذة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Edit Branch',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(thickness: 1),
              const SizedBox(height: 16),

              // محتوى النافذة
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات الفرع - القسم الأول
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.shade100),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(Icons.business, color: Colors.blue),
                                SizedBox(width: 8),
                                Text(
                                  'Branch Information',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // رقمين في صف واحد
                            Row(
                              children: [
                                // اسم الفرع
                                Expanded(
                                  flex: 2,
                                  child: TextField(
                                    controller: _nameController,
                                    decoration: InputDecoration(
                                      labelText: 'Branch Name',
                                      hintText: 'Enter branch name',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.store),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                        RegExp(r'[a-zA-Z0-9\s._-]'),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // رقم الهاتف
                                Expanded(
                                  flex: 1,
                                  child: TextField(
                                    controller: _phoneController,
                                    decoration: InputDecoration(
                                      labelText: 'Phone',
                                      hintText: 'Enter phone number',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.phone),
                                    ),
                                    keyboardType: TextInputType.phone,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // العنوان
                            TextField(
                              controller: _addressController,
                              decoration: InputDecoration(
                                labelText: 'Address',
                                hintText: 'Enter branch address',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                prefixIcon: const Icon(Icons.location_on),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // تنسيق الكود في صف واحد
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _codePrefixController,
                                    decoration: InputDecoration(
                                      labelText: 'Code Prefix',
                                      hintText: 'e.g. BASR',
                                      helperText: 'First 1-4 letters in code',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.code),
                                    ),
                                    maxLength: 4,
                                    textCapitalization:
                                        TextCapitalization.characters,
                                    buildCounter: (BuildContext context,
                                            {int? currentLength,
                                            int? maxLength,
                                            bool? isFocused}) =>
                                        null,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextField(
                                    controller: _codeDigitsController,
                                    decoration: InputDecoration(
                                      labelText: 'Code Digits',
                                      hintText: 'e.g. 25',
                                      helperText: 'Middle 2 digits in code',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      prefixIcon: const Icon(Icons.numbers),
                                    ),
                                    maxLength: 2,
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    buildCounter: (BuildContext context,
                                            {int? currentLength,
                                            int? maxLength,
                                            bool? isFocused}) =>
                                        null,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // معلومات المستخدم - القسم الثاني
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.green.shade100),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.person, color: Colors.green),
                                const SizedBox(width: 8),
                                const Text(
                                  'Basic User Information',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                    color: Colors.green,
                                  ),
                                ),
                                const Spacer(),
                                if (primaryUser != null)
                                  Text(
                                    'User ID: ${primaryUser['id']}',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // اسم المستخدم
                            TextField(
                              controller: _userNameController,
                              decoration: InputDecoration(
                                labelText: 'User Name',
                                hintText: 'Enter user name',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                prefixIcon: const Icon(Icons.person),
                              ),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'[a-zA-Z0-9._-]'),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // الدور
                            DropdownButtonFormField<String>(
                              value: _selectedRole,
                              decoration: InputDecoration(
                                labelText: 'Role',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                prefixIcon:
                                    const Icon(Icons.admin_panel_settings),
                              ),
                              items: _roles.map((role) {
                                return DropdownMenuItem<String>(
                                  value: role,
                                  child: Text(_getRoleDisplayName(role)),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedRole = value!;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () =>
                        _updateBranch(context, branch, primaryUser),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Update Branch'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveBranch(BuildContext context) async {
    // التحقق من الحقول المطلوبة
    if (_nameController.text.trim().isEmpty) {
      _showErrorDialog('Branch name is required');
      return;
    }

    // التحقق من أن اسم الفرع يحتوي على حروف إنجليزية فقط
    if (!_isValidEnglishText(_nameController.text.trim())) {
      _showErrorDialog(
          'Branch name must contain only English letters, numbers, and spaces');
      return;
    }

    if (_codePrefixController.text.trim().isEmpty) {
      _showErrorDialog('Code prefix is required');
      return;
    }

    if (_codeDigitsController.text.trim().isEmpty) {
      _showErrorDialog('Code digits is required');
      return;
    }

    // التحقق من وجود مستخدم (البريد الإلكتروني اختياري)
    bool hasUserName = _userNameController.text.trim().isNotEmpty;
    bool hasPassword = _userPasswordController.text.trim().isNotEmpty;
    bool hasEmail = _userEmailController.text.trim().isNotEmpty;

    // إذا تم إدخال اسم مستخدم أو كلمة مرور، يجب إدخال كليهما
    if ((hasUserName || hasPassword) && !(hasUserName && hasPassword)) {
      _showErrorDialog('User name and password are required together');
      return;
    }

    // التحقق من أن اسم المستخدم يحتوي على حروف إنجليزية فقط
    if (hasUserName && !_isValidUsername(_userNameController.text.trim())) {
      _showErrorDialog(
          'Username must contain only English letters, numbers, and symbols (._-)');
      return;
    }

    bool hasUser = hasUserName && hasPassword;

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (hasEmail && !_isValidEmail(_userEmailController.text)) {
      _showErrorDialog('Please enter a valid email address');
      return;
    }

    try {
      // التحقق من وجود فرع بنفس الاسم أو رمز الفرع (جلب أحدث البيانات من قاعدة البيانات)
      final existingBranches = await widget.databaseHelper.getBranches();
      for (var branch in existingBranches) {
        if (branch['name']?.toString().toLowerCase() ==
            _nameController.text.trim().toLowerCase()) {
          _showErrorDialog(
              'Branch name already exists: "${_nameController.text.trim()}"');
          return;
        }
        if (branch['code_prefix']?.toString().toUpperCase() ==
            _codePrefixController.text.trim().toUpperCase()) {
          _showErrorDialog(
              'Code prefix already exists: "${_codePrefixController.text.trim().toUpperCase()}"');
          return;
        }
      }

      // التحقق من وجود مستخدم بنفس البريد الإلكتروني (فقط إذا تم إدخال بريد إلكتروني)
      if (hasEmail) {
        final existingUsers = await widget.databaseHelper.getUsers();
        for (var user in existingUsers) {
          if (user['email']?.toString().toLowerCase() ==
              _userEmailController.text.trim().toLowerCase()) {
            _showErrorDialog(
                'Email address already exists: "${_userEmailController.text.trim()}"');
            return;
          }
        }
      }

      // إضافة الفرع
      final branchId = await widget.databaseHelper.insertBranch({
        'name': _nameController.text.trim(),
        'address': _addressController.text.trim(),
        'phone': _phoneController.text.trim(),
        'email': _emailController.text.trim(),
        'code_prefix': _codePrefixController.text.trim().toUpperCase(),
        'code_digits': _codeDigitsController.text.trim(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // إضافة المستخدم إذا كانت المعلومات مكتملة
      if (hasUser) {
        String userEmail;

        // تحديد البريد الإلكتروني (إما المدخل أو الافتراضي)
        if (hasEmail) {
          userEmail = _userEmailController.text.trim();
        } else {
          userEmail = await _generateUniqueDefaultEmail(
              _userNameController.text.trim());
        }

        // التحقق من عدم وجود هذا الإيميل مسبقاً
        final existingUsers = await widget.databaseHelper.getUsers();
        bool emailExists = existingUsers.any((user) =>
            user['email']?.toString().toLowerCase() == userEmail.toLowerCase());

        if (emailExists) {
          _showErrorDialog('Email address already exists: "$userEmail"');
          return;
        }

        Map<String, dynamic> userData = {
          'name': _userNameController.text.trim(),
          'password': _userPasswordController.text.trim(),
          'role': _selectedRole,
          'branch_id': branchId,
          'created_at': DateTime.now().toIso8601String(),
          'email': userEmail,
        };

        await widget.databaseHelper.insertUser(userData);
      }

      // إغلاق النافذة وتحديث البيانات
      if (context.mounted) {
        Navigator.pop(context);
        await _loadBranches();
        await _loadUsers();
        _showSuccessSnackBar('Branch added successfully');
      }
    } catch (e) {
      String errorMessage = e.toString();
      if (errorMessage.contains('UNIQUE constraint failed: users.email')) {
        _showErrorDialog(
            'Email address already in use. Please use a different email.');
      } else if (errorMessage.contains('UNIQUE constraint failed')) {
        _showErrorDialog('Data already exists. Please check all fields.');
      } else {
        _showErrorDialog('Error adding branch: $errorMessage');
      }
    }
  }

  Future<void> _updateBranch(BuildContext context, Map<String, dynamic> branch,
      Map<String, dynamic>? primaryUser) async {
    // التحقق من الحقول المطلوبة
    if (_nameController.text.trim().isEmpty) {
      _showErrorDialog('Branch name is required');
      return;
    }

    // التحقق من أن اسم الفرع يحتوي على حروف إنجليزية فقط
    if (!_isValidEnglishText(_nameController.text.trim())) {
      _showErrorDialog(
          'Branch name must contain only English letters, numbers, and spaces');
      return;
    }

    if (_codePrefixController.text.trim().isEmpty) {
      _showErrorDialog('Code prefix is required');
      return;
    }

    if (_codeDigitsController.text.trim().isEmpty) {
      _showErrorDialog('Code digits is required');
      return;
    }

    try {
      // تحديث الفرع
      await widget.databaseHelper.updateBranch({
        'id': branch['id'],
        'name': _nameController.text.trim(),
        'address': _addressController.text.trim(),
        'phone': _phoneController.text.trim(),
        'email': _emailController.text.trim(),
        'code_prefix': _codePrefixController.text.trim().toUpperCase(),
        'code_digits': _codeDigitsController.text.trim(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // تحديث المستخدم إذا كان موجودًا
      if (primaryUser != null) {
        final userUpdate = {
          'id': primaryUser['id'],
          'name': _userNameController.text.trim(),
          'email': _userEmailController.text.trim(),
          'role': _selectedRole,
          'branch_id': branch['id'],
        };

        // إضافة كلمة المرور فقط إذا تم تغييرها
        if (_userPasswordController.text.trim().isNotEmpty) {
          userUpdate['password'] = _userPasswordController.text.trim();
        }

        await widget.databaseHelper.updateUser(userUpdate);
      } else if (_userNameController.text.trim().isNotEmpty &&
          _userEmailController.text.trim().isNotEmpty &&
          _userPasswordController.text.trim().isNotEmpty) {
        // إنشاء مستخدم جديد إذا لم يكن موجودًا وتم إدخال البيانات
        await widget.databaseHelper.insertUser({
          'name': _userNameController.text.trim(),
          'email': _userEmailController.text.trim(),
          'password': _userPasswordController.text.trim(),
          'role': _selectedRole,
          'branch_id': branch['id'],
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      // إغلاق النافذة وتحديث البيانات
      if (context.mounted) {
        Navigator.pop(context);
        await _loadBranches();
        await _loadUsers();
        _showSuccessSnackBar('Branch updated successfully');
      }
    } catch (e) {
      _showErrorDialog('Error updating branch: $e');
    }
  }

  Future<void> _deleteBranch(Map<String, dynamic> branch) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text(
            'Are you sure you want to delete the branch "${branch['name']}"? This will also delete all associated users.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        // حذف الفرع
        await widget.databaseHelper.deleteBranch(branch['id']);

        // تحديث البيانات مع انتظار اكتمال التحديث
        await _loadBranches();
        await _loadUsers();
        _showSuccessSnackBar('Branch deleted successfully');
      } catch (e) {
        _showErrorDialog('Error deleting branch: $e');
      }
    }
  }

  Future<void> _addUserToBranch(Map<String, dynamic> branch) async {
    // تهيئة حقول المستخدم
    _userNameController.clear();
    _userEmailController.clear();
    _userPasswordController.clear();
    _selectedRole = 'user';

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.6,
          constraints: const BoxConstraints(maxWidth: 600),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان النافذة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Add User to ${branch['name']}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(thickness: 1),
              const SizedBox(height: 16),

              // محتوى النافذة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade100),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.person, color: Colors.green),
                        SizedBox(width: 8),
                        Text(
                          'User Information',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // اسم المستخدم
                    TextField(
                      controller: _userNameController,
                      decoration: InputDecoration(
                        labelText: 'User Name',
                        hintText: 'Enter user name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.person),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // البريد الإلكتروني
                    TextField(
                      controller: _userEmailController,
                      decoration: InputDecoration(
                        labelText: 'Email',
                        hintText: 'Enter email',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),

                    // كلمة المرور
                    TextField(
                      controller: _userPasswordController,
                      decoration: InputDecoration(
                        labelText: 'Password',
                        hintText: 'Enter password',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.lock),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),

                    // الدور
                    DropdownButtonFormField<String>(
                      value: _selectedRole,
                      decoration: InputDecoration(
                        labelText: 'Role',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.admin_panel_settings),
                      ),
                      items: _roles.map((role) {
                        return DropdownMenuItem<String>(
                          value: role,
                          child: Text(_getRoleDisplayName(role)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedRole = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _saveUserToBranch(context, branch),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Add User'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveUserToBranch(
      BuildContext context, Map<String, dynamic> branch) async {
    // التحقق من الحقول المطلوبة
    if (_userNameController.text.trim().isEmpty) {
      _showErrorDialog('User name is required');
      return;
    }

    // التحقق من أن اسم المستخدم يحتوي على حروف إنجليزية فقط
    if (!_isValidUsername(_userNameController.text.trim())) {
      _showErrorDialog(
          'Username must contain only English letters, numbers, and symbols (._-)');
      return;
    }

    if (_userPasswordController.text.trim().isEmpty) {
      _showErrorDialog('Password is required');
      return;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (_userEmailController.text.trim().isNotEmpty &&
        !_isValidEmail(_userEmailController.text)) {
      _showErrorDialog('Please enter a valid email address');
      return;
    }

    try {
      String userEmail;

      // تحديد البريد الإلكتروني (إما المدخل أو الافتراضي)
      if (_userEmailController.text.trim().isNotEmpty) {
        userEmail = _userEmailController.text.trim();
      } else {
        userEmail =
            await _generateUniqueDefaultEmail(_userNameController.text.trim());
      }

      // التحقق من عدم وجود هذا الإيميل مسبقاً
      final existingUsers = await widget.databaseHelper.getUsers();
      bool emailExists = existingUsers.any((user) =>
          user['email']?.toString().toLowerCase() == userEmail.toLowerCase());

      if (emailExists) {
        _showErrorDialog('Email address already exists: "$userEmail"');
        return;
      }

      // إعداد بيانات المستخدم الجديد
      Map<String, dynamic> userData = {
        'name': _userNameController.text.trim(),
        'password': _userPasswordController.text.trim(),
        'role': _selectedRole,
        'branch_id': branch['id'],
        'created_at': DateTime.now().toIso8601String(),
        'email': userEmail,
      };

      // إضافة المستخدم الجديد
      await widget.databaseHelper.insertUser(userData);

      // إغلاق النافذة وتحديث البيانات
      if (context.mounted) {
        Navigator.pop(context);
        await _loadUsers();
        _showSuccessSnackBar('User added successfully');
      }
    } catch (e) {
      _showErrorDialog('Error adding user: $e');
    }
  }

  // وظيفة تعديل بيانات المستخدم
  Future<void> _editUser(Map<String, dynamic> user) async {
    // تعيين القيم الحالية للمستخدم
    _userNameController.text = user['name'] ?? '';
    _userEmailController.text = user['email'] ?? '';
    _userPasswordController.clear(); // لا نعرض كلمة المرور الحالية
    _selectedRole = user['role'] ?? 'user';

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.6,
          constraints: const BoxConstraints(maxWidth: 600),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان النافذة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Edit User',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(thickness: 1),
              const SizedBox(height: 16),

              // محتوى النافذة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade100),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.person, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'User Information',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // اسم المستخدم
                    TextField(
                      controller: _userNameController,
                      decoration: InputDecoration(
                        labelText: 'User Name',
                        hintText: 'Enter user name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.person),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // البريد الإلكتروني
                    TextField(
                      controller: _userEmailController,
                      decoration: InputDecoration(
                        labelText: 'Email',
                        hintText: 'Enter email',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),

                    // كلمة المرور (اختيارية عند التعديل)
                    TextField(
                      controller: _userPasswordController,
                      decoration: InputDecoration(
                        labelText: 'Password (leave empty to keep current)',
                        hintText: 'Enter new password',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.lock),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),

                    // الدور
                    DropdownButtonFormField<String>(
                      value: _selectedRole,
                      decoration: InputDecoration(
                        labelText: 'Role',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.admin_panel_settings),
                      ),
                      items: _roles.map((role) {
                        return DropdownMenuItem<String>(
                          value: role,
                          child: Text(_getRoleDisplayName(role)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedRole = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => _updateUser(context, user),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Update User'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // وظيفة حفظ تعديلات المستخدم
  Future<void> _updateUser(
      BuildContext context, Map<String, dynamic> user) async {
    // التحقق من الحقول المطلوبة
    if (_userNameController.text.trim().isEmpty) {
      _showErrorDialog('User name is required');
      return;
    }

    // التحقق من أن اسم المستخدم يحتوي على حروف إنجليزية فقط
    if (!_isValidUsername(_userNameController.text.trim())) {
      _showErrorDialog(
          'Username must contain only English letters, numbers, and symbols (._-)');
      return;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (_userEmailController.text.trim().isNotEmpty &&
        !_isValidEmail(_userEmailController.text)) {
      _showErrorDialog('Please enter a valid email address');
      return;
    }

    try {
      // إعداد بيانات التحديث
      final userUpdate = {
        'id': user['id'],
        'name': _userNameController.text.trim(),
        'role': _selectedRole,
        'branch_id': user['branch_id'],
      };

      // إضافة البريد الإلكتروني إذا تم إدخاله أو الاحتفاظ بالقديم
      if (_userEmailController.text.trim().isNotEmpty) {
        userUpdate['email'] = _userEmailController.text.trim();
      } else if (user['email'] != null && user['email'].toString().isNotEmpty) {
        userUpdate['email'] =
            user['email']; // الاحتفاظ بالبريد الإلكتروني القديم
      } else {
        // إنشاء بريد إلكتروني افتراضي إذا لم يكن موجود
        userUpdate['email'] =
            '${_userNameController.text.trim().toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}@default.local';
      }

      // إضافة كلمة المرور فقط إذا تم تغييرها
      if (_userPasswordController.text.trim().isNotEmpty) {
        userUpdate['password'] = _userPasswordController.text.trim();
      }

      // تحديث بيانات المستخدم
      await widget.databaseHelper.updateUser(userUpdate);

      // إغلاق النافذة وتحديث البيانات
      if (context.mounted) {
        Navigator.pop(context);
        await _loadUsers();
        _showSuccessSnackBar('User updated successfully');
      }
    } catch (e) {
      _showErrorDialog('Error updating user: $e');
    }
  }

  // وظيفة حذف المستخدم
  Future<void> _deleteUser(Map<String, dynamic> user) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content:
            Text('Are you sure you want to delete the user "${user['name']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        // حذف المستخدم
        await widget.databaseHelper.deleteUser(user['id']);

        // تحديث البيانات
        await _loadUsers();
        _showSuccessSnackBar('User deleted successfully');
      } catch (e) {
        _showErrorDialog('Error deleting user: $e');
      }
    }
  }

  // دالة للتحقق من وجود إيميل معين وحذفه

  // دالة لفحص قاعدة البيانات والبحث عن الإيميل المكرر

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red.shade600,
              size: 28,
            ),
            const SizedBox(width: 12),
            Text(
              'Error',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
                color: Colors.red.shade700,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: const TextStyle(
            fontSize: 16,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          Center(
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'OK',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
        actionsAlignment: MainAxisAlignment.center,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل بدلاً من الأعلى
        left: 0,
        right: 0,
        child: Center(
          // وسط الشاشة أفقياً
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: Colors.green.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min, // العرض حسب المحتوى فقط
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    // بدلاً من Expanded لتجنب أخذ العرض الكامل
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد 3 ثواني مع أنيميشن
    Future.delayed(const Duration(seconds: 3), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // تجاهل الخطأ إذا كان العنصر تم حذفه بالفعل
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _branchSearchController,
                    decoration: InputDecoration(
                      hintText: 'Search branches...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onChanged: _filterBranches,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _addBranch,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Branch'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    backgroundColor: Colors.blue,
                  ),
                ),
              ],
            ),
          ),

          // قائمة الفروع
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredBranches.isEmpty
                    ? const Center(child: Text('No branches found'))
                    : ListView.builder(
                        itemCount: _filteredBranches.length,
                        itemBuilder: (context, index) {
                          final branch = _filteredBranches[index];
                          final branchUsers = _branchUsers[branch['id']] ?? [];

                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ExpansionTile(
                              title: Text(
                                branch['name'] ?? 'Unnamed Branch',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              subtitle: Row(
                                children: [
                                  Icon(Icons.phone,
                                      size: 14, color: Colors.grey.shade600),
                                  const SizedBox(width: 4),
                                  Text(
                                    branch['phone'] ?? 'No phone',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Icon(Icons.location_on,
                                      size: 14, color: Colors.grey.shade600),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      branch['address'] ?? 'No address',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 14,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              leading: CircleAvatar(
                                backgroundColor: Colors.blue.shade100,
                                child: const Icon(
                                  Icons.business,
                                  color: Colors.blue,
                                ),
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Code: ${branch['code_prefix'] ?? ''}${branch['code_digits'] ?? ''}',
                                    style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    icon: const Icon(Icons.edit,
                                        color: Colors.blue, size: 20),
                                    onPressed: () => _editBranch(branch),
                                    tooltip: 'Edit Branch',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red, size: 20),
                                    onPressed: () => _deleteBranch(branch),
                                    tooltip: 'Delete Branch',
                                  ),
                                ],
                              ),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // المستخدمون المرتبطون
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          // زر إضافة مستخدم جديد في الجهة اليسرى
                                          ElevatedButton.icon(
                                            onPressed: () =>
                                                _addUserToBranch(branch),
                                            icon: const Icon(
                                              Icons.person_add,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                            label: const Text('Add User'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.blue,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 12,
                                                      vertical: 8),
                                            ),
                                          ),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              const Text(
                                                'Associated Users',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 14,
                                                ),
                                              ),
                                              Text(
                                                '${branchUsers.length} users',
                                                style: TextStyle(
                                                  color: Colors.grey.shade600,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      branchUsers.isEmpty
                                          ? const Text(
                                              'No users associated with this branch',
                                              style: TextStyle(
                                                fontStyle: FontStyle.italic,
                                                color: Colors.grey,
                                              ),
                                            )
                                          : Column(
                                              children: branchUsers
                                                  .map((user) => ListTile(
                                                        dense: true,
                                                        contentPadding:
                                                            EdgeInsets.zero,
                                                        leading: CircleAvatar(
                                                          backgroundColor:
                                                              Colors.green
                                                                  .shade100,
                                                          radius: 16,
                                                          child: const Icon(
                                                            Icons.person,
                                                            size: 16,
                                                            color: Colors.green,
                                                          ),
                                                        ),
                                                        title: Text(
                                                          user['name'] ??
                                                              'Unnamed User',
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 14),
                                                        ),
                                                        subtitle: Text(
                                                          user['email'] ??
                                                              'N/A',
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 12),
                                                        ),
                                                        trailing: Row(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            // عرض الدور
                                                            Container(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          8,
                                                                      vertical:
                                                                          4),
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: _getRoleColor(
                                                                    user[
                                                                        'role']),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                              child: Text(
                                                                _getRoleDisplayName(
                                                                    user['role'] ??
                                                                        'user'),
                                                                style:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .white,
                                                                  fontSize: 12,
                                                                ),
                                                              ),
                                                            ),
                                                            // زر تعديل المستخدم
                                                            IconButton(
                                                              icon: const Icon(
                                                                Icons.edit,
                                                                color:
                                                                    Colors.blue,
                                                                size: 18,
                                                              ),
                                                              onPressed: () =>
                                                                  _editUser(
                                                                      user),
                                                              tooltip:
                                                                  'Edit User',
                                                              constraints:
                                                                  const BoxConstraints(
                                                                minWidth: 36,
                                                                minHeight: 36,
                                                              ),
                                                              padding:
                                                                  EdgeInsets
                                                                      .zero,
                                                            ),
                                                            // زر حذف المستخدم
                                                            IconButton(
                                                              icon: const Icon(
                                                                Icons.delete,
                                                                color:
                                                                    Colors.red,
                                                                size: 18,
                                                              ),
                                                              onPressed: () =>
                                                                  _deleteUser(
                                                                      user),
                                                              tooltip:
                                                                  'Delete User',
                                                              constraints:
                                                                  const BoxConstraints(
                                                                minWidth: 36,
                                                                minHeight: 36,
                                                              ),
                                                              padding:
                                                                  EdgeInsets
                                                                      .zero,
                                                            ),
                                                          ],
                                                        ),
                                                      ))
                                                  .toList(),
                                            ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(String? role) {
    switch (role) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.orange;
      case 'user':
      default:
        return Colors.green;
    }
  }
}
