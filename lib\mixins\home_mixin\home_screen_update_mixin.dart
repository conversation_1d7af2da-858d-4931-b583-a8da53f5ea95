import 'package:flutter/material.dart';
import '../../services/code_data_service.dart';
import 'package:logging/logging.dart';
import '../../utils/ui_helper.dart';
import '../../services/event_bus_service.dart';

/// مزيج يحتوي على دوال التحديث للشاشة الرئيسية
mixin HomeScreenUpdateMixin<T extends StatefulWidget> on State<T> {
  // إنشاء مسجل خاص بهذا المزيج
  final Logger _logger = Logger('HomeScreenUpdateMixin');

  // الحصول على خدمة البيانات
  CodeDataService get codeDataService;

  // الحصول على مراجع المكونات
  GlobalKey get basicInfoKey;
  GlobalKey get senderInfoKey;
  GlobalKey get receiverInfoKey;

  // متغيرات التحكم في حالة الأزرار
  bool get isSaveEnabled;
  set isSaveEnabled(bool value);

  bool get isUpdateEnabled;
  set isUpdateEnabled(bool value);

  // متغير لتخزين الكود الحالي
  String get currentCode;
  set currentCode(String value);

  // دالة جمع البيانات من جميع المكونات
  Map<String, dynamic> collectAllData({bool forLabel = false});

  // دالة التحقق من صحة البيانات
  Future<bool> validateData();

  // دالة تحميل سجل بواسطة الكود - يجب تنفيذها في الكلاس الذي يستخدم هذا المزيج
  Future<void> loadRecordByCode(String code, {bool viewOnly = false});

  // دالة تحديث البيانات
  void updateData() async {
    try {
      _logger.info('بدأت عملية التحديث من خلال الضغط على زر Update');

      // التحقق من صحة البيانات
      bool isValid = await validateData();
      _logger.info('نتيجة التحقق من صحة البيانات: $isValid');

      if (!mounted) return;

      if (isValid) {
        // حفظ القيم الحالية قبل جمع البيانات
        String? currentCountry;
        String? currentCity;
        Map<String, String> currentAddressInfo = {};

        try {
          final receiverInfoState = receiverInfoKey.currentState;
          if (receiverInfoState != null) {
            // حفظ الدولة والمدينة
            currentCountry =
                (receiverInfoState as dynamic).getSelectedCountry();
            currentCity = (receiverInfoState as dynamic).getSelectedCity();

            // حفظ قيم حقول العنوان
            final addressInfoState =
                (receiverInfoState as dynamic).getAddressInfoState();
            if (addressInfoState != null) {
              currentAddressInfo = {
                'street': (addressInfoState as dynamic).getStreet() ?? '',
                'postal_code':
                    (addressInfoState as dynamic).getPostalCode() ?? '',
                'city_name': (addressInfoState as dynamic).getCityName() ?? '',
                'email': (addressInfoState as dynamic).getEmail() ?? '',
              };
            }

            _logger.info('تم حفظ القيم الحالية:');
            _logger.info('الدولة: $currentCountry');
            _logger.info('المدينة: $currentCity');
            _logger.info('معلومات العنوان: $currentAddressInfo');
          }
        } catch (e) {
          _logger.warning('خطأ في حفظ القيم الحالية: $e');
        }

        // جمع البيانات من جميع المكونات
        Map<String, dynamic> data = collectAllData();
        _logger.info('تم جمع البيانات: ${data.keys.length} حقل');

        // التحقق من وجود الكود في البيانات
        if (!data.containsKey('code_no') ||
            data['code_no'] == null ||
            data['code_no'].isEmpty) {
          _logger.warning('لم يتم العثور على الكود في البيانات المجمعة');
          data['code_no'] = currentCode;
          _logger.info('تم إضافة الكود من المتغير currentCode: $currentCode');
        }

        if (data.isEmpty) {
          if (!mounted) return;
          UiHelper.showNotification(
            context,
            messageEn: "Error collecting data",
            isError: true,
            durationSeconds: 4,
          );
          return;
        }

        // تحديث البيانات في قاعدة البيانات
        await updateInDatabase(data);

        // تحديث حالة الأزرار فقط دون إعادة تعيين القيم
        if (mounted) {
          setState(() {
            isSaveEnabled = false;
            isUpdateEnabled = true;
          });
        }
      } else {
        _logger.warning('فشل في التحقق من صحة البيانات');
      }
    } catch (e) {
      if (!mounted) return;
      _logger.severe('خطأ في عملية التحديث: $e');
      UiHelper.showNotification(
        context,
        messageEn: "Error updating data: $e",
        isError: true,
        durationSeconds: 4,
      );
    }
  }

  // دالة تحديث البيانات في قاعدة البيانات
  Future<void> updateInDatabase(Map<String, dynamic> data,
      {Map<String, String>? preserveAddressInfo}) async {
    try {
      _logger.info('بدأت عملية التحديث في قاعدة البيانات');

      // تحويل البيانات إلى الصيغة المناسبة لقاعدة البيانات
      Map<String, dynamic> dbData = Map.from(data);

      // استخدام الكود المخزن في currentCode بدلاً من قراءته من data
      final String codeNo = currentCode;

      // التحقق من وجود كود صالح
      if (codeNo.isEmpty) {
        _logger.severe('الكود الحالي فارغ، لا يمكن إجراء التحديث');

        if (!mounted) return;

        UiHelper.showNotification(
          context,
          messageEn: "Error: Missing code, cannot update data",
          isError: true,
          durationSeconds: 4,
        );
        return;
      }

      dbData['code_no'] = codeNo;

      // تحديث البيانات في قاعدة البيانات
      int result = await codeDataService.updateCodeData(dbData);
      _logger.info('نتيجة التحديث: $result');

      if (!mounted) return;

      if (result >= 0) {
        _logger.info('تم تحديث البيانات بنجاح أو لم تتغير');

        // تحديث العناصر المحددة في قاعدة البيانات
        try {
          final senderInfoState = senderInfoKey.currentState;
          if (senderInfoState != null) {
            final selectedItems =
                (senderInfoState as dynamic).getSelectedItems();
            if (selectedItems != null && selectedItems.isNotEmpty) {
              _logger
                  .info('عدد العناصر المحددة للتحديث: ${selectedItems.length}');

              final itemsToSave = selectedItems
                  .map((item) => {
                        'id': item.id,
                        'quantity': item.quantity,
                        'weight': item.weight,
                      })
                  .toList();

              await codeDataService.saveSelectedItems(codeNo, itemsToSave);
              _logger.info('تم حفظ العناصر المحددة بنجاح');
            }
          }
        } catch (e) {
          _logger.warning('خطأ في تحديث العناصر المحددة: $e');
        }

        if (codeNo.isNotEmpty) {
          _logger.info('تحديث حالة الأزرار بعد التحديث الناجح');
          setState(() {
            isSaveEnabled = false;
            isUpdateEnabled = true;
          });
        }

        if (!mounted) return;

        // عرض رسالة نجاح
        UiHelper.showNotification(
          context,
          messageEn: 'Data updated successfully',
          isError: false,
          durationSeconds: 4,
        );

        // إرسال إشعار للشاشات الأخرى بأن البيانات تم تحديثها
        EventBusService().fireEvent(AppEvent(EventType.dataRefreshNeeded));
        _logger.info('تم إرسال إشعار تحديث البيانات بعد التحديث');
      } else {
        _logger.warning('فشل في تحديث البيانات، النتيجة: $result');

        if (!mounted) return;

        UiHelper.showNotification(
          context,
          messageEn: "Failed to update data",
          isError: true,
          durationSeconds: 4,
        );
      }
    } catch (e) {
      _logger.severe('خطأ في تحديث البيانات: $e');

      if (!mounted) return;

      UiHelper.showNotification(
        context,
        messageEn: "Error updating data: $e",
        isError: true,
        durationSeconds: 4,
      );
    }
  }

  // دالة للحصول على البيانات الحالية
  Map<String, dynamic> getCurrentData() {
    // هذه الدالة تستخدم للحصول على البيانات الحالية في النموذج
    // يمكن استخدامها لعرض البيانات أو مقارنتها
    Map<String, dynamic> data = collectAllData();
    // إضافة الكود الحالي إلى البيانات
    data['current_code'] = currentCode;
    return data;
  }
}
