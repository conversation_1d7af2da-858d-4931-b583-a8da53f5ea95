import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/germany_address.dart';
import '../../models/netherlands_address.dart';

// مزود لعناوين ألمانيا
final germanyAddressesProvider =
    StateNotifierProvider<GermanyAddressesNotifier, List<GermanyAddress>>(
        (ref) {
  return GermanyAddressesNotifier();
});

class GermanyAddressesNotifier extends StateNotifier<List<GermanyAddress>> {
  GermanyAddressesNotifier() : super([]);

  void setAddresses(List<GermanyAddress> addresses) {
    state = addresses;
  }

  void addAddress(GermanyAddress address) {
    state = [...state, address];
  }

  void removeAddress(int index) {
    if (index >= 0 && index < state.length) {
      final newList = List<GermanyAddress>.from(state);
      newList.removeAt(index);
      state = newList;
    }
  }

  void clearAddresses() {
    state = [];
  }
}

// مزود لعناوين هولندا
final netherlandsAddressesProvider = StateNotifierProvider<
    NetherlandsAddressesNotifier, List<NetherlandsAddress>>((ref) {
  return NetherlandsAddressesNotifier();
});

class NetherlandsAddressesNotifier
    extends StateNotifier<List<NetherlandsAddress>> {
  NetherlandsAddressesNotifier() : super([]);

  void setAddresses(List<NetherlandsAddress> addresses) {
    state = addresses;
  }

  void addAddress(NetherlandsAddress address) {
    state = [...state, address];
  }

  void removeAddress(int index) {
    if (index >= 0 && index < state.length) {
      final newList = List<NetherlandsAddress>.from(state);
      newList.removeAt(index);
      state = newList;
    }
  }

  void clearAddresses() {
    state = [];
  }
}
