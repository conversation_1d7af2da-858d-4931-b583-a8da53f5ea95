import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/settings_parts/import_progress_dialog.dart';
import '../../widgets/settings_parts/import_stats_dialog.dart';
import '../../utils/ui_helper.dart';
import 'settings_variables_mixin.dart';

/// Mixin يحتوي على الوظائف المشتركة لاستيراد وتصدير العناوين
mixin AddressImportExportMixin<T extends ConsumerStatefulWidget>
    on ConsumerState<T>, SettingsVariablesMixin<T> {
  // عرض مربع حوار تقدم الاستيراد
  void showImportProgressDialog(String title) {
    if (!mounted) return;

    // إغلاق مربع الحوار السابق إذا كان مفتوحًا
    Navigator.of(context).popUntil((route) => route.isFirst);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ImportProgressDialog(
        title: title,
        progress: importProgress,
        processedItems: processedRows,
        totalItems: totalRows,
        stage: importStage,
      ),
    );
  }

  // عرض مربع حوار إحصائيات الاستيراد
  void showImportStatsDialog(
      String title, int totalImported, int newItems, int skippedItems) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => ImportStatsDialog(
        title: title,
        totalImported: totalImported,
        newItems: newItems,
        skippedItems: skippedItems,
      ),
    );
  }

  // عرض مربع حوار التصدير
  void showExportDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Exporting data...'),
          ],
        ),
      ),
    );
  }

  // إظهار رسالة خطأ
  void showErrorMessage(String message, {bool forceShow = false}) {
    if (!mounted) return;

    UiHelper.safelyShowSnackBar(
      context,
      message,
      isError: true,
      forceShow: forceShow,
    );
  }

  // إظهار رسالة نجاح
  void showSuccessMessage(String message, {bool forceShow = false}) {
    if (!mounted) return;

    UiHelper.safelyShowSnackBar(
      context,
      message,
      isError: false,
      forceShow: forceShow,
    );
  }

  // تحديث حالة تقدم الاستيراد وعرض مربع الحوار
  void updateProgressAndShowDialog({
    required String title,
    required double progress,
    required int processedRows,
    required int totalRows,
    required String stage,
  }) {
    if (!mounted) return;

    // تحديث المتغيرات
    updateImportProgress(
      progress: progress,
      processedRows: processedRows,
      totalRows: totalRows,
      stage: stage,
    );

    // إغلاق مربع الحوار السابق وعرض مربع حوار جديد
    Navigator.of(context).pop();
    showImportProgressDialog(title);
  }
}
