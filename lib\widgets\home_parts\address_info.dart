import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/riverpod/address_database_provider.dart';
import '../../models/netherlands_address.dart';
import '../../utils/ui_helper.dart';

class AddressInfo extends ConsumerStatefulWidget {
  final bool isVisible;
  final String? selectedCountry;

  const AddressInfo({
    super.key,
    this.isVisible = false,
    this.selectedCountry,
  });

  @override
  ConsumerState<AddressInfo> createState() => _AddressInfoState();
}

class _AddressInfoState extends ConsumerState<AddressInfo> {
  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _streetController = TextEditingController();
  final TextEditingController _postalCodeController = TextEditingController();
  final TextEditingController _cityNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  // متغيرات للتحقق من صحة الإدخال
  bool _streetValidated = true;

  // التحقق من صحة الشارع (يجب أن يحتوي على نص + رقم واحد على الأقل)
  bool _isValidStreet(String value) {
    // التحقق من وجود حرف واحد على الأقل ورقم واحد على الأقل
    return RegExp(r'[a-zA-Z]').hasMatch(value) &&
        RegExp(r'[0-9]').hasMatch(value);
  }

  // عرض نافذة البحث عن الرمز البريدي
  void _showPostalCodeSearchDialog() async {
    final String postalCode = _postalCodeController.text;
    if (postalCode.isEmpty) {
      UiHelper.showSnackBar(
        context,
        'Please enter postal code first',
        isError: true,
      );
      return;
    }

    // التحقق من الدولة المحددة
    if (widget.selectedCountry != 'Germany' &&
        widget.selectedCountry != 'Netherlands') {
      // لا نعرض أي رسالة عندما لا تكون الدولة ألمانيا أو هولندا
      return;
    }

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Searching...'),
          ],
        ),
      ),
    );

    try {
      // الحصول على مرجع لخدمة قاعدة بيانات العناوين
      final addressDbService = ref.read(addressDatabaseServiceProvider);

      // البحث عن العنوان بناءً على الرمز البريدي والدولة المحددة
      if (widget.selectedCountry == 'Germany') {
        // البحث في عناوين ألمانيا
        final address =
            await addressDbService.findGermanyAddressByPostalCode(postalCode);

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        if (!mounted) return;

        if (address != null) {
          // تحديث حقل اسم المدينة مباشرة بعد العثور على النتيجة وتحويله إلى حروف كبيرة
          setState(() {
            _cityNameController.text = address.cityName.toUpperCase();
          });

          // لا نعرض رسالة نجاح عند إيجاد اسم المدينة
        } else {
          // لم يتم العثور على العنوان
          _showCustomErrorDialog(context, postalCode);
        }
      } else if (widget.selectedCountry == 'Netherlands') {
        // البحث في عناوين هولندا
        final addresses = await addressDbService
            .findNetherlandsAddressesByPostalCode(postalCode);

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        if (!mounted) return;

        if (addresses.isNotEmpty) {
          // استخدام أول عنوان في القائمة لتحديث حقل اسم المدينة
          final address = addresses.first;
          setState(() {
            _cityNameController.text = address.cityName.toUpperCase();
          });

          // عرض نافذة لإدخال الرقم
          _showStreetNumberDialog(address);
        } else {
          // لم يتم العثور على العنوان
          _showCustomErrorDialog(context, postalCode);
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة حدوث خطأ
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (!mounted) return;

      // عرض رسالة الخطأ
      UiHelper.showSnackBar(
        context,
        'Error during search: $e',
        isError: true,
      );
    }
  }

  // عرض نافذة لإدخال رقم الشارع
  void _showStreetNumberDialog(NetherlandsAddress address) {
    final TextEditingController streetNumberController =
        TextEditingController();
    final int minNumber = int.tryParse(address.minNummer) ?? 0;
    final int maxNumber = int.tryParse(address.maxNummer) ?? 999;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Enter Street Number'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
                'Please enter street number (between $minNumber and $maxNumber)'),
            const SizedBox(height: 16),
            TextField(
              controller: streetNumberController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Street Number',
                border: OutlineInputBorder(),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final String streetNumber = streetNumberController.text;
              if (streetNumber.isEmpty) {
                UiHelper.showSnackBar(
                  context,
                  'Please enter street number',
                  isError: true,
                );
                return;
              }

              final int number = int.tryParse(streetNumber) ?? 0;
              if (number < minNumber || number > maxNumber) {
                UiHelper.showSnackBar(
                  context,
                  'Please enter a number between $minNumber and $maxNumber',
                  isError: true,
                );
                return;
              }

              // إغلاق النافذة
              Navigator.pop(context);

              // تحديث حقل الشارع
              setState(() {
                _streetController.text = '${address.street} $streetNumber';
              });
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  // التحقق من صحة الإدخال
  void _validateFields() {
    // لا نقوم بتحديث حالة الواجهة هنا، فقط نقوم بالتحقق من صحة القيم
    _streetValidated = _streetController.text.isNotEmpty &&
        _isValidStreet(_streetController.text);

    // التحقق من صحة الإيميل (إذا كان غير فارغ)
    // استخدام نتيجة التحقق مباشرة بدلاً من تخزينها في متغير غير مستخدم
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    // إضافة مستمعين للتحقق من صحة الإدخال
    _streetController.addListener(_validateFields);
    _postalCodeController.addListener(_validateFields);
    _cityNameController.addListener(_validateFields);
    _emailController.addListener(_validateFields);
  }

  // دالة لمسح جميع الحقول
  void clearAllFields() {
    setState(() {
      _streetController.clear();
      _postalCodeController.clear();
      _cityNameController.clear();
      _emailController.clear();
      _streetValidated = true;
    });
  }

  // دالة لمسح حقول العنوان المطلوبة بعد حذف السجل
  void clearAddressFields() {
    setState(() {
      _streetController.clear();
      _postalCodeController.clear();
      _cityNameController.clear();
      _emailController.clear();
      _streetValidated = true;
    });
  }

  @override
  void didUpdateWidget(AddressInfo oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إذا تم تفعيل القسم (كان غير مرئي وأصبح مرئي)، نقوم بتفريغ الحقول
    if (!oldWidget.isVisible && widget.isVisible) {
      clearAllFields();
    }
  }

  @override
  void dispose() {
    _streetController.dispose();
    _postalCodeController.dispose();
    _cityNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // إذا كان القسم غير مرئي، نعيد مساحة فارغة
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50, // إضافة لون خلفية أزرق فاتح
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
            color: Colors.blue.shade200), // إضافة حدود بلون أزرق فاتح
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              const Icon(Icons.location_on, color: Colors.blue, size: 18),
              const SizedBox(width: 4),
              const Text(
                'Address Information',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const Spacer(),
              // أيقونة للبحث في قاعدة بيانات عناوين هولندا
              if (widget.selectedCountry == 'Netherlands')
                IconButton(
                  icon: const Icon(Icons.search, size: 18),
                  tooltip: 'Search in Netherlands Address Database',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: _showPostalCodeSearchDialog,
                ),
            ],
          ),
          const SizedBox(height: 12),

          // حقل اسم الشارع والرقم
          TextField(
            controller: _streetController,
            decoration: const InputDecoration(
              labelText: 'Street Name & No *',
              labelStyle: TextStyle(fontSize: 13),
              border: OutlineInputBorder(),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              isDense: true,
              // لا نعرض رسائل الخطأ مباشرة
              errorText: null,
            ),
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
            onChanged: (value) {
              _validateFields();
              // تحويل النص إلى حروف كبيرة
              if (value != value.toUpperCase()) {
                _streetController.value = TextEditingValue(
                  text: value.toUpperCase(),
                  selection: _streetController.selection,
                );
              }
            },
            // تم إلغاء التحديد التلقائي للنص
            // onTap: () {
            //   _streetController.selection = TextSelection(
            //     baseOffset: 0,
            //     extentOffset: _streetController.text.length,
            //   );
            // },
          ),
          const SizedBox(height: 8),

          // حقل الرمز البريدي
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _postalCodeController,
                  decoration: const InputDecoration(
                    labelText: 'Postal Code *',
                    labelStyle: TextStyle(fontSize: 13),
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    isDense: true,
                    // لا نعرض رسائل الخطأ مباشرة
                    errorText: null,
                  ),
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                  // السماح بالأرقام والحروف الإنجليزية فقط
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
                  ],
                  onChanged: (value) {
                    _validateFields();
                    // تحويل النص إلى حروف كبيرة
                    if (value != value.toUpperCase()) {
                      _postalCodeController.value = TextEditingValue(
                        text: value.toUpperCase(),
                        selection: _postalCodeController.selection,
                      );
                    }
                  },
                  // تم إلغاء التحديد التلقائي للنص
                  // onTap: () {
                  //   _postalCodeController.selection = TextSelection(
                  //     baseOffset: 0,
                  //     extentOffset: _postalCodeController.text.length,
                  //   );
                  // },
                ),
              ),
              IconButton(
                icon: const Icon(Icons.search, size: 20),
                onPressed: _showPostalCodeSearchDialog,
                tooltip: 'Search postal code',
              ),
            ],
          ),
          const SizedBox(height: 8),

          // حقل اسم المدينة
          TextField(
            controller: _cityNameController,
            decoration: const InputDecoration(
              labelText: 'City Name *',
              labelStyle: TextStyle(fontSize: 13),
              border: OutlineInputBorder(),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              isDense: true,
              // لا نعرض رسائل الخطأ مباشرة
              errorText: null,
            ),
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
            onChanged: (value) {
              _validateFields();
              // تحويل النص إلى حروف كبيرة
              if (value != value.toUpperCase()) {
                _cityNameController.value = TextEditingValue(
                  text: value.toUpperCase(),
                  selection: _cityNameController.selection,
                );
              }
            },
            // تم إلغاء التحديد التلقائي للنص
            // onTap: () {
            //   _cityNameController.selection = TextSelection(
            //     baseOffset: 0,
            //     extentOffset: _cityNameController.text.length,
            //   );
            // },
          ),
          const SizedBox(height: 8),

          // حقل البريد الإلكتروني للمستلم
          TextField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'Receiver Email (Optional)',
              labelStyle: TextStyle(fontSize: 13),
              border: OutlineInputBorder(),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              isDense: true,
              // لا نعرض رسائل الخطأ مباشرة
              errorText: null,
            ),
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              // تغيير لون النص إلى أحمر إذا كان النص غير فارغ ولا يحتوي على علامة @
              color: _emailController.text.isNotEmpty &&
                      !_emailController.text.contains('@')
                  ? Colors.red
                  : null,
            ),
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) {
              _validateFields();
              // تحويل النص إلى حروف كبيرة
              if (value != value.toUpperCase()) {
                _emailController.value = TextEditingValue(
                  text: value.toUpperCase(),
                  selection: _emailController.selection,
                );
              }
              // إعادة بناء الواجهة لتحديث لون النص
              setState(() {});
            },
            // تم إلغاء التحديد التلقائي للنص
            // onTap: () {
            //   _emailController.selection = TextSelection(
            //     baseOffset: 0,
            //     extentOffset: _emailController.text.length,
            //   );
            // },
          ),
        ],
      ),
    );
  }

  // دالة للحصول على اسم الشارع
  String getStreet() {
    return _streetController.text.trim();
  }

  // دالة للحصول على الرمز البريدي
  String getPostalCode() {
    return _postalCodeController.text.trim();
  }

  // دالة للحصول على اسم المدينة
  String getCityName() {
    return _cityNameController.text.trim();
  }

  // دالة للحصول على البريد الإلكتروني
  String getEmail() {
    return _emailController.text.trim();
  }

  // دالة للتحقق من صحة الشارع وإرجاع قيمة منطقية
  bool isStreetValid() {
    return _streetValidated && _isValidStreet(_streetController.text);
  }

  // دالة لتعيين قيمة حقل الشارع
  void setStreet(String value) {
    _streetController.text = value;
  }

  // دالة لتعيين قيمة حقل الرمز البريدي
  void setPostalCode(String value) {
    _postalCodeController.text = value;
  }

  // دالة لتعيين قيمة حقل اسم المدينة مع ضمان تحويلها إلى حروف كبيرة
  void setCityName(String value) {
    _cityNameController.text = value.toUpperCase();
  }

  // دالة لتعيين قيمة حقل البريد الإلكتروني
  void setEmail(String value) {
    _emailController.text = value;
  }

  // دالة لمسح جميع حقول العنوان دفعة واحدة
  void resetAllFields() {
    setState(() {
      _streetController.text = '';
      _postalCodeController.text = '';
      _cityNameController.text = '';
      _emailController.text = '';
      _validateFields();
    });
  }

  /// عرض نافذة خطأ مخصصة بسيطة وأنيقة
  void _showCustomErrorDialog(BuildContext context, String postalCode) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 10),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الإيقونة والعنوان في صف واحد
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.search_off,
                    size: 24,
                    color: Colors.blue.shade600,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'No Address Found',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // النص التوضيحي
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  height: 1.4,
                ),
                children: [
                  const TextSpan(text: 'No address found for postal code '),
                  TextSpan(
                    text: '"$postalCode"',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  const TextSpan(
                      text: '.\n\nPlease verify the code and try again.'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue.shade600,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'OK',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
