import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../mixins/settings_mixins/settings_variables_mixin.dart';
import '../mixins/settings_mixins/agent_info_mixin.dart';
import '../mixins/settings_mixins/germany_addresses_mixin.dart';
import '../mixins/settings_mixins/netherlands_addresses_mixin.dart';
import '../mixins/settings_mixins/address_import_export_mixin.dart';
import '../mixins/settings_mixins/about_mixin.dart';
import '../mixins/settings_mixins/general_settings_mixin.dart';

import '../providers/riverpod/screen_provider.dart';
import '../providers/riverpod/user_provider.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with
        SingleTickerProviderStateMixin,
        SettingsVariablesMixin<SettingsScreen>,
        AddressImportExportMixin<SettingsScreen>,
        AgentInfoMixin<SettingsScreen>,
        GermanyAddressesMixin<SettingsScreen>,
        NetherlandsAddressesMixin<SettingsScreen>,
        AboutMixin<SettingsScreen>,
        GeneralSettingsMixin<SettingsScreen> {
  late TabController _tabController;

  // تهيئة الحالة
  @override
  void initState() {
    super.initState();

    // تحميل المدن لمعلومات الوكيل
    loadAgentCities();

    // تحميل عناوين ألمانيا وهولندا
    loadGermanyAddresses();
    loadNetherlandsAddresses();

    // تحميل إعدادات لون حد الجدول
    loadTableBorderColorSettings();

    // إعداد متحكم التبويبات
    _tabController = TabController(
      length: 5,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // بناء واجهة الإعدادات
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    ref.watch(userProvider);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        ref.read(screenProvider.notifier).setSelectedIndex(0);
      },
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: theme.primaryColor,
          elevation: 0,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            isScrollable: true,
            tabs: const [
              Tab(
                icon: Icon(Icons.people),
                text: 'Agent Info',
              ),
              Tab(
                icon: Icon(Icons.location_on),
                text: 'Germany Addresses',
              ),
              Tab(
                icon: Icon(Icons.location_city),
                text: 'Netherlands Addresses',
              ),
              Tab(
                icon: Icon(Icons.settings),
                text: 'General Settings',
              ),
              Tab(
                icon: Icon(Icons.info),
                text: 'About',
              ),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            buildAgentInfoTab(),
            buildGermanyAddressesTab(),
            buildNetherlandsAddressesTab(),
            buildGeneralSettingsTab(),
            buildAboutTab(),
          ],
        ),
      ),
    );
  }
}
