import 'package:flutter/material.dart';

class SimpleSearchDialog extends StatefulWidget {
  const SimpleSearchDialog({super.key});

  // ... (existing code)
  @override
  SimpleSearchDialogState createState() => SimpleSearchDialogState();
}

class SimpleSearchDialogState extends State<SimpleSearchDialog> {
  final List<Map<String, dynamic>> _searchResults = [];
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Search for code number...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              // هنا يمكنك إضافة منطق البحث وتحديث _searchResults
            },
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _searchResults.length,
            itemBuilder: (context, index) =>
                _buildSearchResultItem(_searchResults[index]),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultItem(Map<String, dynamic> record) {
    final bool hasSenderName =
        record['sender_name']?.toString().isNotEmpty ?? false;
    final bool hasReceiverName =
        record['receiver_name']?.toString().isNotEmpty ?? false;
    final bool hasMissingNames = !hasSenderName || !hasReceiverName;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  record['code_no'] ?? '',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: hasMissingNames ? Colors.white : null,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${record['sender_name'] ?? 'بدون اسم'} → ${record['receiver_name'] ?? 'بدون اسم'}',
                  style: TextStyle(
                    color: hasMissingNames ? Colors.white : null,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${record['sender_city'] ?? ''} → ${record['receiver_city'] ?? ''}',
                  style: TextStyle(
                    color: hasMissingNames ? Colors.white : null,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.pop(context, record);
            },
          ),
        ],
      ),
    );
  }
}
