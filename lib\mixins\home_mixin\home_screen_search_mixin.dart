import 'package:flutter/material.dart';
import '../../services/code_data_service.dart';
import 'package:logging/logging.dart';
import '../../utils/ui_helper.dart';

/// Mixin containing search functions for the home screen
mixin HomeScreenSearchMixin<T extends StatefulWidget> on State<T> {
  // Create a logger for this mixin
  final Logger _logger = Logger('HomeScreenSearchMixin');

  // Get data service
  CodeDataService get codeDataService;

  // Get list of search columns
  List<Map<String, String>> get searchColumns;

  // Function to load record by code
  void loadRecordByCode(String code, {bool viewOnly = false}) {
    try {
      // Log attempt to load record
      _logger.info('Attempting to load record with code: $code');

      // طباعة رسالة للتتبع لمعرفة ما إذا كانت البيانات تحتوي على عنوان
      _logger.info('التحقق من وجود بيانات العنوان للسجل: $code');

      // Call function to load data from database
      _loadRecordFromDatabase(code, viewOnly: viewOnly);
    } catch (e) {
      // Log error
      _logger.severe('Error loading record with code $code: $e');
      _logger.severe('Error details: ${e.toString()}');
      _logger.severe('Stack trace: ${StackTrace.current}');
    }
  }

  // Private function to load data from database
  Future<void> _loadRecordFromDatabase(String code,
      {bool viewOnly = false}) async {
    try {
      // Log attempt to load data
      _logger.info('Loading data from database for code: $code');

      // إضافة تسجيل إضافي للتتبع
      final codeData = await codeDataService.getCodeDataByCode(code);
      if (codeData != null) {
        final hasStreetData = codeData['street_name_no'] != null &&
            codeData['street_name_no'].toString().isNotEmpty;
        final hasPostalCodeData = codeData['postal_code'] != null &&
            codeData['postal_code'].toString().isNotEmpty;
        final hasCityNameData = codeData['city_name'] != null &&
            codeData['city_name'].toString().isNotEmpty;
        final hasEmailData = codeData['receiver_email'] != null &&
            codeData['receiver_email'].toString().isNotEmpty;

        _logger.info(
            'البيانات تحتوي على عنوان: ${hasStreetData || hasPostalCodeData || hasCityNameData || hasEmailData}');
        _logger.info('street_name_no: ${codeData['street_name_no']}');
        _logger.info('postal_code: ${codeData['postal_code']}');
        _logger.info('city_name: ${codeData['city_name']}');
        _logger.info('receiver_email: ${codeData['receiver_email']}');
      } else {
        _logger.info('لم يتم العثور على بيانات للسجل: $code');
      }

      // Get HomeScreen state
      final homeScreenState = this;

      // Call loadRecordByCode from HomeScreenDataMixin
      try {
        // Use dynamic to access loadRecordByCode from HomeScreenDataMixin
        await (homeScreenState as dynamic)
            .loadRecordByCode(code, viewOnly: viewOnly);

        // Log success
        _logger.info('Successfully called loadRecordByCode for code: $code');
      } catch (e) {
        // Log error
        _logger.severe('Error calling loadRecordByCode: $e');
        _logger.severe('Error details: ${e.toString()}');
        _logger.severe('Stack trace: ${StackTrace.current}');

        // Rethrow exception to be caught in original function
        rethrow;
      }
    } catch (e) {
      // Log error
      _logger.severe('Error in _loadRecordFromDatabase for code $code: $e');
      _logger.severe('Error details: ${e.toString()}');

      // Rethrow exception to be caught in original function
      rethrow;
    }
  }

  // List of columns to display in search list
  final List<String> columnsToDisplay = [
    'code_no',
    'truck_no',
    'sender_name',
    'sender_phone',
    'receiver_name',
    'receiver_phone',
    'country',
    'city',
    'street_name_no',
    'postal_code',
    'city_name',
  ];

  // Function to show simple search dialog when pressing Ctrl+F
  void showSearchColumnsDialog() {
    try {
      // Log start of search dialog display
      _logger.info('Starting simple search dialog display');

      // Load all data and display in simple dialog
      _loadAndShowSimpleSearchDialog();
    } catch (e) {
      // Log error
      _logger.severe('Error displaying search dialog: $e');
      _logger.severe('Error details: ${e.toString()}');
      _logger.severe('Stack trace: ${StackTrace.current}');
    }
  }

  // دالة تحميل وعرض نافذة بحث بسيطة
  Future<void> _loadAndShowSimpleSearchDialog() async {
    try {
      _logger.info('Loading simple search dialog...');

      // استعلام لاسترداد بيانات البحث من قاعدة البيانات مع اختيار أعمدة محددة فقط
      List<Map<String, dynamic>> searchResults =
          await codeDataService.getCodeDataWithSelectedColumns([
        'id',
        'code_no',
        'date',
        'truck_no',
        'sender_name',
        'sender_phone',
        'sender_id',
        'sender_id_type',
        'sender_id_image_path',
        'goods_description',
        'receiver_name',
        'receiver_phone',
        'country',
        'city',
        'street_name_no',
        'postal_code',
        'city_name',
        'receiver_email',
        'created_at'
      ]);

      _logger
          .info('Search results retrieved: ${searchResults.length} records.');

      // عرض نافذة البحث بالنتائج
      if (mounted) {
        showSearchDialog(searchResults);
      }
    } catch (e) {
      _logger.severe('Error loading search data: $e');
      if (mounted) {
        UiHelper.showNotification(
          context,
          messageEn: "Error loading search data: $e",
          isError: true,
          durationSeconds: 4,
        );
      }
    }
  }

  // دالة عرض نافذة البحث
  void showSearchDialog(List<Map<String, dynamic>> records) {
    TextEditingController searchController = TextEditingController();

    // فلترة السجلات لإظهار فقط التي تحتوي على اسم المرسل واسم المستلم
    List<Map<String, dynamic>> filteredRecords = records.where((record) {
      final senderName = record['sender_name']?.toString().trim() ?? '';
      final receiverName = record['receiver_name']?.toString().trim() ?? '';
      return senderName.isNotEmpty && receiverName.isNotEmpty;
    }).toList();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            // دالة فلترة السجلات بناءً على نص البحث
            void filterRecords(String query) {
              setState(() {
                if (query.isEmpty) {
                  // إذا كان نص البحث فارغًا، عرض السجلات التي تحتوي على اسم المرسل واسم المستلم فقط
                  filteredRecords = records.where((record) {
                    final senderName =
                        record['sender_name']?.toString().trim() ?? '';
                    final receiverName =
                        record['receiver_name']?.toString().trim() ?? '';
                    return senderName.isNotEmpty && receiverName.isNotEmpty;
                  }).toList();
                } else {
                  // البحث في سجلات تحتوي على اسم المرسل واسم المستلم فقط
                  List<Map<String, dynamic>> baseRecords =
                      records.where((record) {
                    final senderName =
                        record['sender_name']?.toString().trim() ?? '';
                    final receiverName =
                        record['receiver_name']?.toString().trim() ?? '';
                    return senderName.isNotEmpty && receiverName.isNotEmpty;
                  }).toList();

                  filteredRecords = baseRecords.where((record) {
                    // البحث في عدة حقول
                    final codeNo =
                        record['code_no']?.toString().toLowerCase() ?? '';
                    final senderName =
                        record['sender_name']?.toString().toLowerCase() ?? '';
                    final receiverName =
                        record['receiver_name']?.toString().toLowerCase() ?? '';
                    final truckNo =
                        record['truck_no']?.toString().toLowerCase() ?? '';
                    final country =
                        record['country']?.toString().toLowerCase() ?? '';
                    final city = record['city']?.toString().toLowerCase() ?? '';
                    final date = record['date']?.toString() ?? '';
                    final searchLower = query.toLowerCase();

                    return codeNo.contains(searchLower) ||
                        senderName.contains(searchLower) ||
                        receiverName.contains(searchLower) ||
                        truckNo.contains(searchLower) ||
                        country.contains(searchLower) ||
                        city.contains(searchLower) ||
                        date.contains(searchLower);
                  }).toList();
                }
              });
            }

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 10,
              insetPadding: const EdgeInsets.all(20),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.729,
                height: MediaQuery.of(context).size.height * 0.8,
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Dialog title and close button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Search Results (${filteredRecords.length})',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Search field
                    TextField(
                      controller: searchController,
                      decoration: const InputDecoration(
                        hintText: 'Search here...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: filterRecords,
                    ),
                    const SizedBox(height: 8),

                    // User hint message
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline,
                              size: 16, color: Colors.blue),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Tip: Double-click on any row to load data. Only showing records with both sender and receiver information.',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Results list
                    Expanded(
                      child: _buildHorizontalListView(filteredRecords),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Function to build horizontal list view in English
  Widget _buildHorizontalListView(List<Map<String, dynamic>> data) {
    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 5),
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            // Add double-tap property to show options dialog
            onDoubleTap: () {
              try {
                // Log double-tap
                _logger
                    .info('Double-tap on item with code: ${item['code_no']}');

                // Close search dialog
                Navigator.of(context).pop();

                // Show options dialog
                _showOptionsDialog(item);
              } catch (e) {
                // Log error
                _logger.severe('Error handling double-tap: $e');
                _logger.severe('Error details: ${e.toString()}');

                // Close search dialog in case of error
                if (Navigator.canPop(context)) {
                  Navigator.of(context).pop();
                }
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Code and truck information
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.qr_code,
                                color: Colors.blue, size: 20),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Code: ${item['code_no'] ?? ''}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.local_shipping,
                                color: Colors.blue, size: 20),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Truck: ${item['truck_no'] ?? ''}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Sender information
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.person,
                                color: Colors.green, size: 18),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Sender: ${item['sender_name'] ?? ''}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.phone,
                                color: Colors.green, size: 18),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Phone: ${item['sender_phone'] ?? ''}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Receiver information
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.person_outline,
                                color: Colors.orange, size: 18),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Receiver: ${item['receiver_name'] ?? ''}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.phone_outlined,
                                color: Colors.orange, size: 18),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Phone: ${item['receiver_phone'] ?? ''}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Address information
                  Expanded(
                    flex: 4,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.location_on,
                                color: Colors.red, size: 18),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SelectableText(
                                'Address: ${item['country'] ?? ''}, ${item['city'] ?? ''}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const SizedBox(width: 22),
                            Expanded(
                              child: SelectableText(
                                '${item['street_name_no'] ?? ''}, ${item['postal_code'] ?? ''}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Load button with tooltip
                  Tooltip(
                    message: 'Double-click on any row to load data',
                    child: IconButton(
                      icon: const Icon(Icons.arrow_forward_ios, size: 16),
                      onPressed: () {
                        try {
                          // Log click on load button
                          _logger.info(
                              'Clicked on load button for item with code: ${item['code_no']}');

                          // Close search dialog
                          Navigator.of(context).pop();

                          // Show options dialog
                          _showOptionsDialog(item);
                        } catch (e) {
                          // Log error
                          _logger.severe(
                              'Error handling click on load button: $e');
                          _logger.severe('Error details: ${e.toString()}');

                          // Close search dialog in case of error
                          if (Navigator.canPop(context)) {
                            Navigator.of(context).pop();
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Function to show options dialog
  void _showOptionsDialog(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Select Action for Code: ${item['code_no']}'),
          content: SizedBox(
            width: 500, // Increase width for horizontal buttons
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Row of three buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Add new button
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(vertical: 15),
                          ),
                          onPressed: () {
                            // Close options dialog
                            Navigator.of(context).pop();

                            // Execute add new function
                            _addNewBasedOnExistingRecord(item);
                          },
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.add_circle,
                                  color: Colors.white, size: 24),
                              SizedBox(height: 5),
                              Text(
                                'Add New',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Edit button
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            padding: const EdgeInsets.symmetric(vertical: 15),
                          ),
                          onPressed: () {
                            // Close options dialog
                            Navigator.of(context).pop();

                            // Log attempt to load record for editing
                            _logger.info(
                                'تحميل السجل للتعديل: ${item['code_no']}');

                            // Clear all fields first (without loading default truck no)
                            try {
                              _logger.info(
                                  'Clearing all fields before loading data for editing');
                              final homeScreenState = this;
                              (homeScreenState as dynamic)
                                  .clearFields(loadDefaultTruckNo: false);
                              _logger.info('Successfully cleared all fields');
                            } catch (e) {
                              _logger.severe('Error clearing fields: $e');
                              _logger.severe('Error details: ${e.toString()}');
                            }

                            // Load data for editing
                            loadRecordByCode(item['code_no'], viewOnly: false);

                            // تفعيل زر التحديث
                            final homeScreenState = this;
                            (homeScreenState as dynamic).setState(() {
                              (homeScreenState as dynamic).isUpdateEnabled =
                                  true;
                            });

                            // عرض رسالة نجاح
                            UiHelper.showNotification(
                              context,
                              messageEn: "Record loaded successfully",
                              isError: false,
                              durationSeconds: 2,
                            );
                          },
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.edit, color: Colors.white, size: 24),
                              SizedBox(height: 5),
                              Text(
                                'Edit',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // View button
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            padding: const EdgeInsets.symmetric(vertical: 15),
                          ),
                          onPressed: () {
                            // Close options dialog
                            Navigator.of(context).pop();

                            // Log attempt to load record for viewing
                            _logger
                                .info('تحميل السجل للعرض: ${item['code_no']}');

                            // Clear all fields first (without loading default truck no)
                            try {
                              _logger.info(
                                  'Clearing all fields before loading data for viewing');
                              final homeScreenState = this;
                              (homeScreenState as dynamic)
                                  .clearFields(loadDefaultTruckNo: false);
                              _logger.info('Successfully cleared all fields');
                            } catch (e) {
                              _logger.severe('Error clearing fields: $e');
                              _logger.severe('Error details: ${e.toString()}');
                            }

                            // Load data for view only
                            loadRecordByCode(item['code_no'], viewOnly: true);

                            // تعطيل زر التحديث
                            final homeScreenState = this;
                            (homeScreenState as dynamic).setState(() {
                              (homeScreenState as dynamic).isUpdateEnabled =
                                  false;
                              (homeScreenState as dynamic).isSaveEnabled =
                                  false;
                            });

                            // عرض رسالة نجاح
                            UiHelper.showNotification(
                              context,
                              messageEn: "Record loaded successfully",
                              isError: false,
                              durationSeconds: 2,
                            );
                          },
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.visibility,
                                  color: Colors.white, size: 24),
                              SizedBox(height: 5),
                              Text(
                                'View',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  // دالة إضافة سجل جديد بناءً على سجل موجود
  Future<void> _addNewBasedOnExistingRecord(
      Map<String, dynamic> existingRecord) async {
    try {
      _logger.info(
          'بدء إضافة سجل جديد بناءً على السجل الموجود: ${existingRecord['code_no']}');

      // الحصول على حالة الشاشة الرئيسية
      final homeScreenState = this;

      // مسح جميع الحقول أولاً (وتحميل آخر رقم شاحنة لأن هذا Add New)
      try {
        _logger.info('مسح جميع الحقول قبل تحميل البيانات');
        (homeScreenState as dynamic).clearFields(loadDefaultTruckNo: true);
        _logger.info('تم مسح جميع الحقول بنجاح');
      } catch (e) {
        _logger.severe('خطأ في مسح الحقول: $e');
        _logger.severe('تفاصيل الخطأ: ${e.toString()}');
      }

      // تعبئة حقول BasicInfo
      try {
        final basicInfoState =
            (homeScreenState as dynamic).basicInfoKey.currentState;
        if (basicInfoState != null) {
          // توليد كود جديد
          await (homeScreenState as dynamic).generateNewCode();
          _logger.info('تم توليد كود جديد بنجاح');

          // تم تحميل آخر رقم شاحنة بالفعل من clearFields
        } else {
          _logger.warning('basicInfoState هي قيمة فارغة');
        }
      } catch (e) {
        _logger.severe('خطأ في تعبئة حقول BasicInfo: $e');
        _logger.severe('تفاصيل الخطأ: ${e.toString()}');
      }

      // تعبئة حقول SenderInfo بالكامل
      try {
        final senderInfoState =
            (homeScreenState as dynamic).senderInfoKey.currentState;
        if (senderInfoState != null) {
          // تعيين اسم المرسل ورقم الهاتف ومعرف الهوية
          (senderInfoState as dynamic)
              .setSenderName(existingRecord['sender_name'] ?? '');
          (senderInfoState as dynamic)
              .setSenderPhone(existingRecord['sender_phone'] ?? '');
          (senderInfoState as dynamic)
              .setSenderId(existingRecord['sender_id'] ?? '');
          (senderInfoState as dynamic)
              .setSenderIdType(existingRecord['sender_id_type'] ?? '');

          // نسخ صورة الهوية (إذا كانت موجودة)
          final idImagePath = existingRecord['sender_id_image_path'] ?? '';
          if (idImagePath.isNotEmpty) {
            (senderInfoState as dynamic).setSenderIdImagePath(idImagePath);
            _logger.info('تم تعيين مسار صورة الهوية بنجاح: $idImagePath');
          }

          _logger.info('تم تعبئة حقول المرسل بنجاح');
        } else {
          _logger.warning('senderInfoState هي قيمة فارغة');
        }
      } catch (e) {
        _logger.severe('خطأ في تعبئة حقول المرسل: $e');
        _logger.severe('تفاصيل الخطأ: ${e.toString()}');
      }

      // تعبئة حقول ReceiverInfo
      try {
        final receiverInfoState =
            (homeScreenState as dynamic).receiverInfoKey.currentState;
        if (receiverInfoState != null) {
          // تعيين اسم المستلم ورقم الهاتف
          try {
            (receiverInfoState as dynamic)
                .setReceiverName(existingRecord['receiver_name'] ?? '');
            (receiverInfoState as dynamic)
                .setReceiverPhone(existingRecord['receiver_phone'] ?? '');
            _logger.info('تم تعيين اسم المستلم ورقم الهاتف بنجاح');
          } catch (e) {
            _logger.severe('خطأ في تعيين اسم المستلم ورقم الهاتف: $e');
          }

          // تعيين الدولة والمدينة
          try {
            final country = existingRecord['country'] ?? 'Germany';
            final city = existingRecord['city'] ?? 'Duisburg';
            _logger.info('محاولة تعيين الدولة: $country والمدينة: $city');
            (receiverInfoState as dynamic).setCountryAndCity(country, city);
            _logger.info('تم تعيين الدولة والمدينة بنجاح');
          } catch (e) {
            _logger.severe('خطأ في تعيين الدولة والمدينة: $e');
            _logger.severe('تفاصيل الخطأ: ${e.toString()}');
          }

          // التحقق مما إذا كان هناك بيانات عنوان في أي حقل
          final hasStreetData = existingRecord['street_name_no'] != null &&
              existingRecord['street_name_no'].toString().isNotEmpty;
          final hasPostalCodeData = existingRecord['postal_code'] != null &&
              existingRecord['postal_code'].toString().isNotEmpty;
          final hasCityNameData = existingRecord['city_name'] != null &&
              existingRecord['city_name'].toString().isNotEmpty;
          final hasEmailData = existingRecord['receiver_email'] != null &&
              existingRecord['receiver_email'].toString().isNotEmpty;

          // التحقق مما إذا كان يجب تفعيل قسم العنوان
          final hasAddressData = hasStreetData ||
              hasPostalCodeData ||
              hasCityNameData ||
              hasEmailData;

          if (hasAddressData) {
            try {
              _logger
                  .info('تم العثور على بيانات عنوان، جاري تفعيل قسم العنوان');

              // تفعيل قسم العنوان بطريقة أكثر موثوقية
              (receiverInfoState as dynamic).setState(() {
                (receiverInfoState as dynamic).setAddressInfoVisible(true);
              });

              // إضافة تأخير قصير لضمان اكتمال تحديث واجهة المستخدم
              Future.delayed(const Duration(milliseconds: 300), () {
                if (receiverInfoState.mounted) {
                  try {
                    // التحقق من قسم العنوان مرة أخرى بعد التأخير
                    final addressInfoState =
                        (receiverInfoState as dynamic).getAddressInfoState();

                    if (addressInfoState != null && addressInfoState.mounted) {
                      // تعيين قيم حقول العنوان
                      (addressInfoState as dynamic)
                          .setStreet(existingRecord['street_name_no'] ?? '');
                      (addressInfoState as dynamic)
                          .setPostalCode(existingRecord['postal_code'] ?? '');
                      (addressInfoState as dynamic)
                          .setCityName(existingRecord['city_name'] ?? '');
                      (addressInfoState as dynamic)
                          .setEmail(existingRecord['receiver_email'] ?? '');

                      // تحديث واجهة المستخدم مرة أخرى بعد تعيين القيم
                      (addressInfoState as dynamic).setState(() {});

                      _logger.info('تم تعيين قيم حقول العنوان بنجاح:');
                      _logger
                          .info('الشارع: ${existingRecord['street_name_no']}');
                      _logger.info(
                          'الرمز البريدي: ${existingRecord['postal_code']}');
                      _logger
                          .info('اسم المدينة: ${existingRecord['city_name']}');
                      _logger.info(
                          'البريد الإلكتروني: ${existingRecord['receiver_email']}');

                      // تحديث Door to Door Cost بعد تعيين قيم حقول العنوان
                      try {
                        // الحصول على الوزن الكلي
                        final weightInfoState = (homeScreenState as dynamic)
                            .weightInfoKey
                            .currentState;
                        if (weightInfoState != null &&
                            (weightInfoState as dynamic).mounted) {
                          final totalWeight =
                              (weightInfoState as dynamic).getTotalWeight();

                          // استدعاء دالة تحديث Door to Door Cost
                          (homeScreenState as dynamic)
                              .updateDoorToDoorCost(totalWeight);
                          _logger.info(
                              'تم تحديث Door to Door Cost بعد تفعيل قسم العنوان');
                        }
                      } catch (e) {
                        _logger.severe(
                            'خطأ في تحديث Door to Door Cost بعد تفعيل قسم العنوان: $e');
                      }
                    }
                  } catch (e) {
                    _logger.severe(
                        'خطأ في تعيين قيم حقول العنوان بعد تحديث واجهة المستخدم: $e');
                  }
                }
              });
            } catch (e) {
              _logger.severe(
                  'خطأ في تعيين حالة قسم العنوان أو تعبئة حقول العنوان: $e');
              _logger.severe('تفاصيل الخطأ: ${e.toString()}');
            }
          }

          _logger.info('تم تعبئة حقول المستلم بنجاح');
        } else {
          _logger.warning('receiverInfoState هي قيمة فارغة');
        }
      } catch (e) {
        _logger.severe('خطأ في تعبئة حقول المستلم: $e');
        _logger.severe('تفاصيل الخطأ: ${e.toString()}');
      }

      // تحديث حالات الأزرار
      try {
        (homeScreenState as dynamic).setState(() {
          (homeScreenState as dynamic).isSaveEnabled = true;
          (homeScreenState as dynamic).isUpdateEnabled = false;
        });
        _logger.info(
            'تم تحديث حالات الأزرار: isSaveEnabled = true, isUpdateEnabled = false');
      } catch (e) {
        _logger.severe('خطأ في تحديث حالات الأزرار: $e');
        _logger.severe('تفاصيل الخطأ: ${e.toString()}');
      }

      _logger.info('تمت إضافة سجل جديد بناءً على السجل الموجود بنجاح');

      // عرض رسالة نجاح للمستخدم
      if (homeScreenState.mounted) {
        UiHelper.showNotification(
          (homeScreenState as dynamic).context,
          messageEn: 'New record created successfully based on existing record',
          isError: false,
          durationSeconds: 2,
        );
      }
    } catch (e) {
      _logger.severe('خطأ في إضافة سجل جديد بناءً على السجل الموجود: $e');
      _logger.severe('تفاصيل الخطأ: ${e.toString()}');
    }
  }

  // Function to handle search result selection
}
