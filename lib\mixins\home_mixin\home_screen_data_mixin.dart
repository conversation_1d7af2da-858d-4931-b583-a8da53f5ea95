import 'package:flutter/material.dart';
import '../../services/code_data_service.dart';
import '../../utils/ui_helper.dart';
import '../../models/item_data.dart';

/// مزيج يحتوي على دوال التعامل مع البيانات للشاشة الرئيسية
mixin HomeScreenDataMixin<T extends StatefulWidget> on State<T> {
  // الحصول على خدمة البيانات
  CodeDataService get codeDataService;

  // الحصول على مراجع المكونات
  GlobalKey get basicInfoKey;
  GlobalKey get senderInfoKey;
  GlobalKey get receiverInfoKey;
  GlobalKey get weightInfoKey;
  GlobalKey get costInfoKey;
  GlobalKey get notesKey;

  // متغيرات التحكم في حالة الأزرار
  bool get isSaveEnabled;
  set isSaveEnabled(bool value);

  bool get isUpdateEnabled;
  set isUpdateEnabled(bool value);

  // متغير لتخزين الكود الحالي
  String get currentCode;
  set currentCode(String value);

  // دالة توليد كود جديد
  Future<void> generateNewCode() async {
    try {
      final basicInfoState = basicInfoKey.currentState;
      if (basicInfoState != null) {
        // استخدام الدالة الموجودة في BasicInfo لتوليد كود جديد
        // ملاحظة: سيتم استخدام فقط السجلات التي تحتوي على اسم المرسل واسم المستلم لتوليد الكود

        // التحقق ما إذا كان هذا هو التوليد الأولي عند بدء التطبيق
        bool isInitialLoad = true;
        try {
          // محاولة الوصول إلى سياق التطبيق والتحقق من حالة الطريق
          isInitialLoad = ModalRoute.of(context)?.isCurrent != true;
        } catch (e) {
          // في حالة حدوث خطأ، نفترض أن هذا هو التوليد الأولي
          debugPrint('خطأ في التحقق من حالة الطريق: $e');
        }

        // تمرير معلومات ما إذا كان هذا هو التوليد الأولي إلى دالة توليد الكود
        await (basicInfoState as dynamic)
            .generateNewCode(isInitialLoad: isInitialLoad);

        // طباعة الكود الجديد للتأكد من توليده بنجاح
        final newCode = (basicInfoState as dynamic).getCodeNo();
        debugPrint('تم توليد كود جديد بنجاح: $newCode');
      }
    } catch (e) {
      debugPrint('خطأ في توليد كود جديد: $e');
      rethrow; // إعادة رمي الخطأ ليتم التقاطه في catchError
    }
  }

  // دالة لتحميل بيانات السجل بواسطة الكود
  Future<void> loadRecordByCode(String code, {bool viewOnly = false}) async {
    try {
      // إعادة تعيين جميع الحقول قبل تحميل البيانات الجديدة
      _resetAllFields();

      // الحصول على بيانات السجل من قاعدة البيانات
      final data = await codeDataService.getCodeDataByCode(code);

      if (data == null) {
        if (mounted) {
          UiHelper.showNotification(
            context,
            messageEn: "Record not found",
            isError: true,
            durationSeconds: 3,
          );
        }
        return;
      }

      // تحميل البيانات في الحقول
      _fillFieldsWithData(data);

      // تحميل العناصر المحددة
      try {
        final selectedItemsData = await codeDataService.getSelectedItems(code);
        if (selectedItemsData.isNotEmpty) {
          final senderInfoState = senderInfoKey.currentState;
          if (senderInfoState != null) {
            // الحصول على خدمة بيانات البضائع
            final goodsDataService =
                (senderInfoState as dynamic).getGoodsDataService();
            if (goodsDataService != null) {
              // تحميل جميع العناصر
              final allItems = await goodsDataService.getAllGoodsAsItems();

              // تحويل البيانات المحملة إلى كائنات ItemData
              final selectedItems = selectedItemsData.map((item) {
                // البحث عن العنصر في قائمة جميع العناصر
                final foundItem = allItems.firstWhere(
                  (element) => element.id == item['item_id'],
                  orElse: () => ItemData(
                    id: item['item_id'],
                    nameAr: item['name_ar'],
                    nameEn: item['name_en'],
                    isSelected: true,
                    quantity: item['quantity'],
                    weight: item['weight'],
                  ),
                );

                // إنشاء نسخة من العنصر مع تحديث البيانات
                return foundItem.copyWith(
                  isSelected: true,
                  quantity: item['quantity'],
                  weight: item['weight'],
                );
              }).toList();

              // تحديث العناصر المحددة في SenderInfo
              (senderInfoState as dynamic).clearSelectedItems();
              (senderInfoState as dynamic).addSelectedItems(selectedItems);

              // إعادة بناء وصف البضائع
              final goodsDescription = selectedItems.map((item) {
                return '${item.nameAr} (${item.quantity})';
              }).join(' - ');

              // تحديث حقل وصف البضائع
              (senderInfoState as dynamic)
                  .setGoodsDescription(goodsDescription);
            }
          }
        }
      } catch (e) {
        debugPrint('خطأ في تحميل العناصر المحددة: $e');
      }

      // تخزين الكود الحالي وتحديث حالة الأزرار
      // نستخدم الكود من البيانات المستردة بدلاً من الكود المدخل
      // هذا يضمن استخدام الكود بالصيغة الدقيقة كما هو مخزن في قاعدة البيانات
      currentCode = data['code_no'];

      // تحديث حقل Code No في BasicInfo للتأكد من عرض الصيغة الصحيحة للكود
      final basicInfoState = basicInfoKey.currentState;
      if (basicInfoState != null) {
        try {
          (basicInfoState as dynamic).setCodeNo(data['code_no']);
        } catch (e) {
          debugPrint('خطأ في تحديث حقل Code No: $e');
        }
      }

      setState(() {
        isSaveEnabled = false;
        isUpdateEnabled = !viewOnly; // تعطيل زر التحديث إذا كان العرض فقط
      });

      // إعادة حساب جميع القيم بعد تحميل البيانات
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          // تحديث قيم WeightInfo أولاً
          final weightInfoState = weightInfoKey.currentState;
          if (weightInfoState != null && (weightInfoState as dynamic).mounted) {
            // التحقق مما إذا كان خيار الأبعاد مفعلاً
            final useDimensions =
                (weightInfoState as dynamic).getUseDimensions();

            if (useDimensions) {
              // إعادة حساب الوزن الحجمي إذا كان خيار الأبعاد مفعلاً
              (weightInfoState as dynamic).calculateVolumeWeight();
            }

            // إعادة حساب إجمالي الوزن
            (weightInfoState as dynamic).calculateTotalWeight();

            // الحصول على الوزن الكلي لاستخدامه في حساب Door to Door Cost
            final totalWeight = (weightInfoState as dynamic).getTotalWeight();

            // استدعاء دالة تحديث Door to Door Cost بعد تحديث الوزن الكلي
            // هذا سيضمن تحديث Door to Door Cost بناءً على حالة قسم Address Info
            try {
              // استدعاء دالة updateDoorToDoorCost من HomeScreenCalculationsMixin
              final homeScreenState = this;
              (homeScreenState as dynamic).updateDoorToDoorCost(totalWeight);
            } catch (e) {
              debugPrint('خطأ في استدعاء updateDoorToDoorCost: $e');
            }
          }

          // تحديث قيم CostInfo بعد تحديث الوزن
          final costInfoState = costInfoKey.currentState;
          if (costInfoState != null && (costInfoState as dynamic).mounted) {
            (costInfoState as dynamic).calculateInsuranceAmount();
            (costInfoState as dynamic).calculateTotals();
            (costInfoState as dynamic).calculateUnpaid();
          }
        } catch (e) {
          debugPrint('خطأ في إعادة حساب القيم بعد تحميل البيانات: $e');
        }
      });
    } catch (e) {
      if (mounted) {
        UiHelper.showNotification(
          context,
          messageEn: "Error loading data: $e",
          isError: true,
          durationSeconds: 4,
        );
      }
    }
  }

  // دالة إعادة تعيين جميع الحقول
  void _resetAllFields() {
    try {
      // إعادة تعيين حقول BasicInfo
      final basicInfoState = basicInfoKey.currentState;
      if (basicInfoState != null) {
        try {
          (basicInfoState as dynamic).resetFields();
        } catch (e) {
          debugPrint('خطأ في إعادة تعيين حقول BasicInfo: $e');
        }
      }

      // إعادة تعيين حقول SenderInfo
      final senderInfoState = senderInfoKey.currentState;
      if (senderInfoState != null) {
        try {
          (senderInfoState as dynamic).resetFields();
        } catch (e) {
          debugPrint('خطأ في إعادة تعيين حقول SenderInfo: $e');
        }
      }

      // إعادة تعيين حقول ReceiverInfo
      final receiverInfoState = receiverInfoKey.currentState;
      if (receiverInfoState != null) {
        try {
          (receiverInfoState as dynamic).resetFields();
        } catch (e) {
          debugPrint('خطأ في إعادة تعيين حقول ReceiverInfo: $e');
        }
      }

      // إعادة تعيين حقول WeightInfo
      final weightInfoState = weightInfoKey.currentState;
      if (weightInfoState != null) {
        try {
          (weightInfoState as dynamic).resetFields();
        } catch (e) {
          debugPrint('خطأ في إعادة تعيين حقول WeightInfo: $e');
        }
      }

      // إعادة تعيين حقول CostInfo
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        try {
          (costInfoState as dynamic).resetFields();
        } catch (e) {
          debugPrint('خطأ في إعادة تعيين حقول CostInfo: $e');
        }
      }

      // إعادة تعيين حقول Notes
      final notesState = notesKey.currentState;
      if (notesState != null) {
        try {
          (notesState as dynamic).resetFields();
        } catch (e) {
          debugPrint('خطأ في إعادة تعيين حقول Notes: $e');
        }
      }
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين جميع الحقول: $e');
    }
  }

  // دالة تعبئة الحقول بالبيانات المسترجعة
  void _fillFieldsWithData(Map<String, dynamic> data) {
    try {
      // تعبئة حقول BasicInfo
      final basicInfoState = basicInfoKey.currentState;
      if (basicInfoState != null) {
        try {
          (basicInfoState as dynamic).setCodeNo(data['code_no'] ?? '');
          (basicInfoState as dynamic).setTruckNo(data['truck_no'] ?? '');
          // استخدام created_at بدلاً من date لعرض تاريخ إضافة الكود
          String createdDate = data['created_at'] ?? '';
          debugPrint('تاريخ الإنشاء من قاعدة البيانات: $createdDate');

          if (createdDate.isNotEmpty) {
            // تحويل التاريخ من ISO format إلى yyyy-MM-dd format إذا لزم الأمر
            try {
              DateTime dateTime = DateTime.parse(createdDate);
              createdDate =
                  '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
              debugPrint('تاريخ الإنشاء بعد التحويل: $createdDate');
            } catch (e) {
              // إذا فشل التحويل، استخدم القيمة كما هي
              debugPrint('خطأ في تحويل تاريخ الإنشاء: $e');
            }
          } else {
            // إذا لم يكن هناك تاريخ إنشاء، استخدم تاريخ date العادي كبديل
            createdDate = data['date'] ?? '';
            debugPrint('تاريخ الإنشاء فارغ، استخدام date كبديل: $createdDate');
          }
          (basicInfoState as dynamic).setDate(createdDate);
        } catch (e) {
          debugPrint('خطأ في تعبئة حقول BasicInfo: $e');
        }
      }

      // تعبئة حقول SenderInfo
      final senderInfoState = senderInfoKey.currentState;
      if (senderInfoState != null) {
        try {
          (senderInfoState as dynamic).setSenderName(data['sender_name'] ?? '');
          (senderInfoState as dynamic)
              .setSenderPhone(data['sender_phone'] ?? '');
          (senderInfoState as dynamic).setSenderId(data['sender_id'] ?? '');
          (senderInfoState as dynamic)
              .setSenderIdType(data['sender_id_type'] ?? '');
          (senderInfoState as dynamic)
              .setSenderIdImagePath(data['sender_id_image_path'] ?? '');
          (senderInfoState as dynamic)
              .setGoodsDescription(data['goods_description'] ?? '');

          // تحديث لون أيقونة صورة الهوية بناءً على وجود مسار الصورة
          WidgetsBinding.instance.addPostFrameCallback((_) {
            try {
              // تحديث واجهة المستخدم لإظهار حالة صورة الهوية
              (senderInfoState as dynamic).setState(() {});
            } catch (e) {
              debugPrint('خطأ في تحديث حالة صورة الهوية: $e');
            }
          });
        } catch (e) {
          debugPrint('خطأ في تعبئة حقول SenderInfo: $e');
        }
      }

      // تعبئة حقول ReceiverInfo
      final receiverInfoState = receiverInfoKey.currentState;
      if (receiverInfoState != null) {
        try {
          (receiverInfoState as dynamic)
              .setReceiverName(data['receiver_name'] ?? '');
          (receiverInfoState as dynamic)
              .setReceiverPhone(data['receiver_phone'] ?? '');

          // تعيين الدولة والمدينة
          try {
            final country = data['country'] ?? 'Germany';
            final city = data['city'] ?? 'Duisburg';
            (receiverInfoState as dynamic).setCountryAndCity(country, city);
          } catch (e) {
            debugPrint('خطأ في تعيين الدولة والمدينة: $e');
          }

          // تعبئة حقول العنوان بغض النظر عن حالة التفعيل
          try {
            // تحديد ما إذا كان هناك بيانات عنوان في أي حقل
            final hasStreetData = data['street_name_no'] != null &&
                data['street_name_no'].toString().isNotEmpty;
            final hasPostalCodeData = data['postal_code'] != null &&
                data['postal_code'].toString().isNotEmpty;
            final hasCityNameData = data['city_name'] != null &&
                data['city_name'].toString().isNotEmpty;
            final hasEmailData = data['receiver_email'] != null &&
                data['receiver_email'].toString().isNotEmpty;

            // تحديد ما إذا كان يجب تفعيل قسم العنوان
            final hasAddressData = hasStreetData ||
                hasPostalCodeData ||
                hasCityNameData ||
                hasEmailData;

            debugPrint('فحص بيانات العنوان:');
            debugPrint(
                'hasStreetData: $hasStreetData (${data['street_name_no']})');
            debugPrint(
                'hasPostalCodeData: $hasPostalCodeData (${data['postal_code']})');
            debugPrint(
                'hasCityNameData: $hasCityNameData (${data['city_name']})');
            debugPrint(
                'hasEmailData: $hasEmailData (${data['receiver_email']})');
            debugPrint('hasAddressData (النتيجة النهائية): $hasAddressData');

            // إجراء آمن للتأكد من إظهار قسم العنوان أولاً
            if (hasAddressData) {
              debugPrint('تم العثور على بيانات عنوان، تفعيل قسم العنوان');

              // تأكد من تفعيل قسم العنوان أولاً
              (receiverInfoState as dynamic).setAddressInfoVisible(true);

              // تأكد من أن القسم أصبح مرئياً
              final isVisible =
                  (receiverInfoState as dynamic).isAddressInfoVisible();
              debugPrint('هل قسم العنوان مرئي بعد التفعيل المباشر؟ $isVisible');

              // ننتظر لضمان تحديث واجهة المستخدم
              Future.delayed(const Duration(milliseconds: 300), () {
                if (!receiverInfoState.mounted) return;

                // الحصول على حالة قسم العنوان بعد تفعيله
                final addressInfoState =
                    (receiverInfoState as dynamic).getAddressInfoState();
                if (addressInfoState != null && addressInfoState.mounted) {
                  debugPrint(
                      'تم الوصول إلى قسم العنوان بنجاح، تعيين البيانات...');

                  // تعيين قيم حقول العنوان بالتسلسل مع تأخير بسيط بين كل عملية
                  Future.microtask(() {
                    try {
                      (addressInfoState as dynamic)
                          .setStreet(data['street_name_no'] ?? '');
                      debugPrint('تم تعيين الشارع: ${data['street_name_no']}');
                    } catch (e) {
                      debugPrint('خطأ في تعيين الشارع: $e');
                    }

                    Future.microtask(() {
                      try {
                        (addressInfoState as dynamic)
                            .setPostalCode(data['postal_code'] ?? '');
                        debugPrint(
                            'تم تعيين الرمز البريدي: ${data['postal_code']}');
                      } catch (e) {
                        debugPrint('خطأ في تعيين الرمز البريدي: $e');
                      }

                      Future.microtask(() {
                        try {
                          (addressInfoState as dynamic)
                              .setCityName(data['city_name'] ?? '');
                          debugPrint(
                              'تم تعيين اسم المدينة: ${data['city_name']}');
                        } catch (e) {
                          debugPrint('خطأ في تعيين اسم المدينة: $e');
                        }

                        Future.microtask(() {
                          try {
                            (addressInfoState as dynamic)
                                .setEmail(data['receiver_email'] ?? '');
                            debugPrint(
                                'تم تعيين البريد الإلكتروني: ${data['receiver_email']}');
                          } catch (e) {
                            debugPrint('خطأ في تعيين البريد الإلكتروني: $e');
                          }

                          // تحديث واجهة المستخدم بعد تعيين جميع القيم
                          if (addressInfoState.mounted) {
                            (addressInfoState as dynamic).setState(() {});
                            debugPrint(
                                'تم تحديث واجهة المستخدم بعد تعيين جميع قيم العنوان');
                          }
                        });
                      });
                    });
                  });
                } else {
                  debugPrint(
                      'فشل الوصول إلى قسم العنوان بعد تفعيله: ${addressInfoState == null ? "قسم العنوان غير موجود" : "قسم العنوان غير مثبت"}');
                }
              });
            } else {
              debugPrint(
                  'لم يتم العثور على بيانات عنوان، قسم العنوان غير مفعل');
            }
          } catch (e) {
            debugPrint('خطأ في تعبئة حقول العنوان: $e');
            debugPrint('تفاصيل الخطأ: ${e.toString()}');
            debugPrint('تتبع الاستدعاء: ${StackTrace.current}');
          }

          // تعيين قيمة Exchange Rate في PriceInfo
          try {
            final priceInfoState =
                (receiverInfoState as dynamic).getPriceInfoState();
            if (priceInfoState != null) {
              // استخدام القيمة من قاعدة البيانات أو القيمة الافتراضية 1309
              double exchangeRate = data['exchange_rate'] ?? 1309.0;
              (priceInfoState as dynamic).setExchangeRate(exchangeRate);
            }
          } catch (e) {
            debugPrint('خطأ في تعيين سعر الصرف: $e');
          }
        } catch (e) {
          debugPrint('خطأ في تعبئة حقول ReceiverInfo: $e');
        }
      }

      // تعبئة حقول WeightInfo
      final weightInfoState = weightInfoKey.currentState;
      if (weightInfoState != null) {
        try {
          // تعيين القيم الأساسية أولاً
          (weightInfoState as dynamic).setBoxNo(data['box_no'] ?? 0);
          (weightInfoState as dynamic).setPalletNo(data['pallet_no'] ?? 0);
          (weightInfoState as dynamic)
              .setRealWeight(data['real_weight_kg'] ?? 0.0);

          // تخزين قيمة Additional KG للاستخدام لاحقاً
          final additionalKg = data['additional_kg'] ?? 0.0;

          // تعيين حالة قسم الأبعاد - نعطل الأبعاد أولاً
          (weightInfoState as dynamic).setUseDimensions(false);

          // إذا كانت هناك بيانات أبعاد، نقوم بتفعيل خيار الأبعاد وتعيين القيم
          final hasValidDimensions = data['height'] != null &&
              data['length'] != null &&
              data['width'] != null &&
              data['height'].toString() != '0.0' &&
              data['length'].toString() != '0.0' &&
              data['width'].toString() != '0.0';

          if (hasValidDimensions) {
            // تفعيل خيار الأبعاد
            (weightInfoState as dynamic).setUseDimensions(true);

            // تعيين قيم الأبعاد
            WidgetsBinding.instance.addPostFrameCallback((_) {
              try {
                // تعيين قيم الأبعاد بعد تفعيل الخيار
                (weightInfoState as dynamic).setHeight(data['height'] ?? 0.0);
                (weightInfoState as dynamic).setLength(data['length'] ?? 0.0);
                (weightInfoState as dynamic).setWidth(data['width'] ?? 0.0);
                (weightInfoState as dynamic)
                    .setSelectedFactor(data['factor'] ?? 5000);

                // إعادة حساب الوزن الحجمي
                (weightInfoState as dynamic).calculateVolumeWeight();
              } catch (e) {
                debugPrint('خطأ في تعيين قيم الأبعاد: $e');
              }
            });
          }

          // تعيين قيمة Additional KG بعد تعيين حالة الأبعاد
          // نستخدم addPostFrameCallback لضمان تعيين القيمة بعد تحديث واجهة المستخدم
          WidgetsBinding.instance.addPostFrameCallback((_) {
            try {
              (weightInfoState as dynamic).setAdditionalKg(additionalKg);

              // إعادة حساب إجمالي الوزن بعد تعيين قيمة Additional KG
              if (weightInfoState.mounted) {
                (weightInfoState as dynamic).calculateTotalWeight();
              }
            } catch (e) {
              debugPrint('خطأ في تعيين قيمة Additional KG: $e');
            }
          });
        } catch (e) {
          debugPrint('خطأ في تعبئة حقول WeightInfo: $e');
        }
      }

      // تعبئة حقول CostInfo
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        try {
          // تعيين حالة قسم التأمين
          final useInsurance = data['use_insurance'] == 1 ||
              (data['insurance_percent'] != null &&
                  data['insurance_percent'].toString() != '0.0');
          (costInfoState as dynamic).setUseInsurance(useInsurance);

          // تعبئة حقول التأمين إذا كانت مفعلة
          if (useInsurance) {
            (costInfoState as dynamic)
                .setInsurancePercent(data['insurance_percent'] ?? 6.0);
            (costInfoState as dynamic)
                .setGoodsValue(data['goods_value'] ?? 0.0);
            (costInfoState as dynamic)
                .setInsuranceAmount(data['insurance_amount'] ?? 0.0);
          } else {
            // تعبئة قيمة البضاعة حتى لو كان التأمين غير مفعل
            (costInfoState as dynamic)
                .setGoodsValue(data['goods_value'] ?? 0.0);
          }

          // تعبئة حقول التكلفة
          (costInfoState as dynamic).setExportDoc(data['export_doc'] ?? 0.0);
          (costInfoState as dynamic)
              .setBoxPackingCost(data['box_packing_cost'] ?? 0.0);
          (costInfoState as dynamic)
              .setPostSubCost(data['post_sub_cost'] ?? 0.0);
          (costInfoState as dynamic)
              .setDoorToDoorCost(data['door_to_door_cost'] ?? 0.0);
          (costInfoState as dynamic)
              .setDiscountAmount(data['discount_amount'] ?? 0.0);
          (costInfoState as dynamic).setTotalPaid(data['total_paid'] ?? 0.0);

          // تعيين إجمالي تكلفة البريد
          (costInfoState as dynamic)
              .setTotalPostCost(data['total_post_cost'] ?? 0.0);

          // تعيين المبلغ غير المدفوع
          (costInfoState as dynamic)
              .setUnpaidAmount(data['unpaid_amount'] ?? 0.0);

          // تعيين إجمالي التكلفة باليورو
          (costInfoState as dynamic)
              .setTotalCostEUR(data['total_cost_eur'] ?? 0.0);

          // تعيين المبلغ غير المدفوع باليورو
          (costInfoState as dynamic).setUnpaidEUR(data['unpaid_eur'] ?? 0.0);

          // تعيين حالة خيار تحويل المبلغ غير المدفوع إلى المدفوع
          // إذا كان المبلغ غير المدفوع يساوي صفر، فهذا يعني أن المبلغ بالكامل مدفوع
          final bool isPaidInFull;

          // التحقق من وجود حقل transfer_unpaid_to_paid في البيانات
          if (data.containsKey('transfer_unpaid_to_paid')) {
            isPaidInFull = data['transfer_unpaid_to_paid'] == 1;
          } else {
            final unpaidAmount = data['unpaid_amount'] ?? 0.0;
            final totalPostCost = data['total_post_cost'] ?? 0.0;
            final totalPaid = data['total_paid'] ?? 0.0;

            // تحقق مما إذا كان المبلغ المدفوع يساوي إجمالي التكلفة
            isPaidInFull = (unpaidAmount == 0.0 || totalPaid >= totalPostCost);
          }

          // تعيين حالة الخيار
          (costInfoState as dynamic).setTransferUnpaidToPaid(isPaidInFull);

          // إعادة حساب القيم بعد تعبئة جميع الحقول
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (costInfoState.mounted) {
              (costInfoState as dynamic).calculateInsuranceAmount();
              (costInfoState as dynamic).calculateTotals();
              (costInfoState as dynamic).calculateUnpaid();
            }
          });
        } catch (e) {
          debugPrint('خطأ في تعبئة حقول CostInfo: $e');
        }
      }

      // تعبئة حقل الملاحظات
      final notesState = notesKey.currentState;
      if (notesState != null) {
        (notesState as dynamic).setNotes(data['notes'] ?? '');
      }
    } catch (e) {
      debugPrint('خطأ في تعبئة الحقول: $e');
    }
  }
}
