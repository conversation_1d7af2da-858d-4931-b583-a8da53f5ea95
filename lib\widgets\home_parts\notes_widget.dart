import 'package:flutter/material.dart';

class NotesWidget extends StatefulWidget {
  const NotesWidget({super.key});

  @override
  State<NotesWidget> createState() => _NotesWidgetState();
}

class _NotesWidgetState extends State<NotesWidget> {
  // Text controller for notes
  final TextEditingController _notesController = TextEditingController();
  // Always use LTR text direction for English
  final TextDirection _textDirection = TextDirection.ltr;

  @override
  void initState() {
    super.initState();
    // No need for listener to detect language anymore
  }

  // Function to get notes text
  String getNotes() {
    return _notesController.text.trim();
  }

  // Function to set notes text
  void setNotes(String value) {
    _notesController.text = value.toUpperCase();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(0),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notes field
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                labelStyle: TextStyle(fontSize: 13),
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                isDense: true,
              ),
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 3, // Increase number of lines to 3
              textDirection: _textDirection, // Always LTR for English
              textAlign: TextAlign.start, // Align text from the beginning
              onChanged: (value) {
                // Convert English text to uppercase
                if (value != value.toUpperCase()) {
                  _notesController.value = TextEditingValue(
                    text: value.toUpperCase(),
                    selection: _notesController.selection,
                  );
                }
              },
              onTap: () {
                // Select all text in the field when clicked
                _notesController.selection = TextSelection(
                  baseOffset: 0,
                  extentOffset: _notesController.text.length,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
