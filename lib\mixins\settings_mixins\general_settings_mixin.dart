import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../screens/settings_screen.dart';
import '../../pdf_module/office_label.dart';
import '../../utils/ui_helper.dart';
import '../../utils/invoice_language_helper.dart';

/// General settings mixin for settings screen
mixin GeneralSettingsMixin<T extends StatefulWidget> on State<SettingsScreen> {
  // Table border color settings state variables
  Color _selectedTableBorderColor = Colors.blue.shade800;

  // Invoice language settings state variables
  String _selectedInvoiceLanguage = InvoiceLanguageHelper.arabic;
  final List<Color> _availableColors = [
    // Primary Colors
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
    Colors.blueGrey,
    Colors.black,

    // Bright Primary Shades
    Colors.red.shade600,
    Colors.pink.shade600,
    Colors.purple.shade600,
    Colors.deepPurple.shade600,
    Colors.indigo.shade600,
    Colors.blue.shade600,
    Colors.lightBlue.shade600,
    Colors.cyan.shade600,
    Colors.teal.shade600,
    Colors.green.shade600,
    Colors.lightGreen.shade600,
    Colors.lime.shade600,
    Colors.yellow.shade600,
    Colors.amber.shade600,
    Colors.orange.shade600,
    Colors.deepOrange.shade600,
    Colors.brown.shade600,
    Colors.grey.shade600,
    Colors.blueGrey.shade600,

    // Dark Shades
    Colors.red.shade800,
    Colors.pink.shade800,
    Colors.purple.shade800,
    Colors.deepPurple.shade800,
    Colors.indigo.shade800,
    Colors.blue.shade800,
    Colors.lightBlue.shade800,
    Colors.cyan.shade800,
    Colors.teal.shade800,
    Colors.green.shade800,
    Colors.lightGreen.shade800,
    Colors.lime.shade800,
    Colors.yellow.shade800,
    Colors.amber.shade800,
    Colors.orange.shade800,
    Colors.deepOrange.shade800,
    Colors.brown.shade800,
    Colors.grey.shade800,
    Colors.blueGrey.shade800,

    // Very Dark Shades
    Colors.red.shade900,
    Colors.pink.shade900,
    Colors.purple.shade900,
    Colors.deepPurple.shade900,
    Colors.indigo.shade900,
    Colors.blue.shade900,
    Colors.lightBlue.shade900,
    Colors.cyan.shade900,
    Colors.teal.shade900,
    Colors.green.shade900,
    Colors.lightGreen.shade900,
    Colors.lime.shade900,
    Colors.yellow.shade900,
    Colors.amber.shade900,
    Colors.orange.shade900,
    Colors.deepOrange.shade900,
    Colors.brown.shade900,
    Colors.grey.shade900,
    Colors.blueGrey.shade900,

    // Custom Professional Colors
    const Color(0xFF2E2E2E), // Dark Grey
    const Color(0xFF1A1A1A), // Very Dark Grey
    const Color(0xFF4A4A4A), // Medium Grey
    const Color(0xFF8B4513), // Saddle Brown
    const Color(0xFF800080), // Purple
    const Color(0xFF008080), // Teal
    const Color(0xFF4B0082), // Indigo
    const Color(0xFF800000), // Maroon
    const Color(0xFF556B2F), // Dark Olive Green
    const Color(0xFF2F4F4F), // Dark Slate Grey
    const Color(0xFF191970), // Midnight Blue
    const Color(0xFF8B0000), // Dark Red
    const Color(0xFF006400), // Dark Green
    const Color(0xFF483D8B), // Dark Slate Blue
    const Color(0xFF2E8B57), // Sea Green
    const Color(0xFF8B008B), // Dark Magenta
  ];

  /// Load table border color settings - must be called from initState in settings_screen
  Future<void> loadTableBorderColorSettings() async {
    await _loadTableBorderColorSetting();
    await _loadInvoiceLanguageSetting();
  }

  /// Load table border color setting
  Future<void> _loadTableBorderColorSetting() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colorValue = prefs.getInt('table_border_color');
      if (colorValue != null) {
        setState(() {
          _selectedTableBorderColor = Color(colorValue);
        });
      }
    } catch (e) {
      debugPrint('Error loading table border color setting: $e');
    }
  }

  /// Load invoice language setting
  Future<void> _loadInvoiceLanguageSetting() async {
    try {
      final language = await InvoiceLanguageHelper.getSavedLanguage();
      setState(() {
        _selectedInvoiceLanguage = language;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل إعداد لغة الفاتورة: $e');
    }
  }

  /// Save table border color setting
  Future<void> _saveTableBorderColorSetting(Color color) async {
    try {
      await OfficeLabel.setTableBorderColor(color.toARGB32());
      setState(() {
        _selectedTableBorderColor = color;
      });

      if (mounted) {
        UiHelper.showSnackBar(
          context,
          'Table border color saved successfully',
        );
      }
    } catch (e) {
      debugPrint('Error saving table border color: $e');
    }
  }

  /// Save invoice language setting
  Future<void> _saveInvoiceLanguageSetting(String language) async {
    try {
      await InvoiceLanguageHelper.saveLanguage(language);
      setState(() {
        _selectedInvoiceLanguage = language;
      });

      if (mounted) {
        UiHelper.showSnackBar(
          context,
          'Invoice language saved successfully',
        );
      }
    } catch (e) {
      debugPrint('خطأ في حفظ لغة الفاتورة: $e');
    }
  }

  /// Build general settings tab
  Widget buildGeneralSettingsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'General Settings',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Invoice language settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Invoice Language Settings',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('Invoice Language'),
                    subtitle: const Text(
                        'Choose the language for invoice text display'),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .primaryColor
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(color: Theme.of(context).primaryColor),
                      ),
                      child: Text(
                        InvoiceLanguageHelper.getLanguageDisplayName(
                            _selectedInvoiceLanguage),
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    onTap: () {
                      _showLanguagePickerDialog();
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // PDF appearance settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'PDF Appearance Settings',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('Table Border Color'),
                    subtitle: const Text(
                        'Choose border color for shipment information table in office label'),
                    trailing: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _selectedTableBorderColor,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onTap: () {
                      _showColorPickerDialog();
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// Show color picker dialog
  void _showColorPickerDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Border Color'),
        content: SizedBox(
          width: 500,
          height: 400,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 8,
              crossAxisSpacing: 4,
              mainAxisSpacing: 4,
            ),
            itemCount: _availableColors.length,
            itemBuilder: (context, index) {
              final color = _availableColors[index];
              final isSelected =
                  color.toARGB32() == _selectedTableBorderColor.toARGB32();

              return GestureDetector(
                onTap: () {
                  _saveTableBorderColorSetting(color);
                  Navigator.pop(context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.grey.shade300,
                      width: isSelected ? 3 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20,
                        )
                      : null,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show language picker dialog
  void _showLanguagePickerDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Invoice Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children:
              InvoiceLanguageHelper.getAvailableLanguages().map((language) {
            final isSelected = language['code'] == _selectedInvoiceLanguage;
            return ListTile(
              title: Text(language['name']!),
              subtitle: Text(_getLanguageDescription(language['code']!)),
              leading: Radio<String>(
                value: language['code']!,
                groupValue: _selectedInvoiceLanguage,
                onChanged: (value) {
                  if (value != null) {
                    _saveInvoiceLanguageSetting(value);
                    Navigator.pop(context);
                  }
                },
              ),
              selected: isSelected,
              onTap: () {
                _saveInvoiceLanguageSetting(language['code']!);
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Get language description
  String _getLanguageDescription(String languageCode) {
    switch (languageCode) {
      case InvoiceLanguageHelper.arabic:
        return 'Arabic';
      case InvoiceLanguageHelper.kurdish:
        return 'Kurdish';
      case InvoiceLanguageHelper.english:
        return 'English';
      default:
        return '';
    }
  }
}
