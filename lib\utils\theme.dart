import 'package:flutter/material.dart';

// ألوان التطبيق الرئيسية
class AppColors {
  static const Color primary = Color(0xFF1E88E5);
  static const Color secondary = Color(0xFF26A69A);
  static const Color accent = Color(0xFFFFA000);
  static const Color background = Color(0xFFF5F5F5);
  static const Color cardBackground = Colors.white;
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
}

// أنماط النصوص
class AppTextStyles {
  static const TextStyle heading = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static const TextStyle subheading = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
  );

  static const TextStyle body = TextStyle(
    fontSize: 14,
    color: AppColors.textPrimary,
  );

  static const TextStyle caption = TextStyle(
    fontSize: 12,
    color: AppColors.textSecondary,
  );
}

// أنماط الحدود والزوايا
class AppBorders {
  static final BorderRadius borderRadius = BorderRadius.circular(12);
  static final BorderRadius buttonRadius = BorderRadius.circular(8);

  static final RoundedRectangleBorder cardShape = RoundedRectangleBorder(
    borderRadius: borderRadius,
  );

  static final RoundedRectangleBorder buttonShape = RoundedRectangleBorder(
    borderRadius: buttonRadius,
  );
}

// سمة التطبيق الفاتحة
final ThemeData lightTheme = ThemeData(
  useMaterial3: true,
  brightness: Brightness.light,
  primaryColor: AppColors.primary,
  scaffoldBackgroundColor: AppColors.background,
  fontFamily: 'NotoSansArabic',

  // مخطط الألوان
  colorScheme: const ColorScheme.light(
    primary: AppColors.primary,
    secondary: AppColors.secondary,
    tertiary: AppColors.accent,
    surface: AppColors.background,
    surfaceTint: AppColors.cardBackground,
  ),

  // نمط شريط التطبيق
  appBarTheme: const AppBarTheme(
    backgroundColor: AppColors.primary,
    foregroundColor: Colors.white,
    elevation: 0,
  ),

  // نمط البطاقات
  cardTheme: CardThemeData(
    elevation: 2,
    shape: AppBorders.cardShape,
    color: AppColors.cardBackground,
  ),

  // نمط أزرار الارتفاع
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      foregroundColor: Colors.white,
      backgroundColor: AppColors.primary,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      shape: AppBorders.buttonShape,
    ),
  ),

  // نمط حقول الإدخال
  inputDecorationTheme: InputDecorationTheme(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
    filled: true,
    fillColor: Colors.white,
  ),

  // نمط القوائم المنسدلة
  dropdownMenuTheme: DropdownMenuThemeData(
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
  ),
);

// أدوات عرض الرسائل المنبثقة
class SnackBarUtils {
  SnackBarUtils._(); // منع إنشاء نسخة من الفئة

  /// عرض رسالة نجاح في أسفل منتصف الشاشة
  static void showSuccessSnackBar(BuildContext context, String message) {
    _showBottomSnackBar(
        context, message, Colors.green, const Duration(seconds: 3));
  }

  /// عرض رسالة خطأ في أسفل منتصف الشاشة
  static void showErrorSnackBar(BuildContext context, String message) {
    _showBottomSnackBar(
        context, message, Colors.red, const Duration(seconds: 4));
  }

  /// عرض رسالة تحذير في أسفل منتصف الشاشة
  static void showWarningSnackBar(BuildContext context, String message) {
    _showBottomSnackBar(
        context, message, Colors.orange, const Duration(seconds: 3));
  }

  /// عرض رسالة معلومات في أسفل منتصف الشاشة
  static void showInfoSnackBar(BuildContext context, String message) {
    _showBottomSnackBar(
        context, message, Colors.blue, const Duration(seconds: 3));
  }

  /// دالة مساعدة لعرض الرسائل في أسفل منتصف الشاشة
  static void _showBottomSnackBar(BuildContext context, String message,
      Color backgroundColor, Duration duration) {
    if (!context.mounted) return;

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getIconForColor(backgroundColor),
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد المدة المحددة
    Future.delayed(duration, () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // تجاهل الخطأ إذا كان العنصر تم حذفه بالفعل
      }
    });
  }

  /// الحصول على الأيقونة المناسبة بناءً على اللون
  static IconData _getIconForColor(Color color) {
    if (color == Colors.green) {
      return Icons.check_circle;
    } else if (color == Colors.red) {
      return Icons.error;
    } else if (color == Colors.orange) {
      return Icons.warning;
    } else if (color == Colors.blue) {
      return Icons.info;
    }
    return Icons.notifications;
  }
}
