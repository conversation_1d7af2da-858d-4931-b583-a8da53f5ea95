// ثوابت التطبيق المختلفة مقسمة حسب الفئة
class AppConstants {
  AppConstants._(); // منع إنشاء نسخة من الفئة

  // معلومات التطبيق
  static const String appName = 'EUKnet Company';
  static const String appVersion = '1.0.0';

  // عناوين الشاشات
  static const String homeTitle = 'Dashboard';
  static const String reportsTitle = 'Reports';
  static const String adminTitle = 'Admin Panel';
  static const String settingsTitle = 'Settings';

  // رسائل
  static const String welcomeMessage = 'Welcome to EUKnet Company Dashboard';
  static const String homeSubtitle =
      'Manage your international transport operations';

  // قيم افتراضية
  static const String defaultUsername = 'admin';
  static const String defaultPassword = '12345';
  static const String defaultBranch = 'Baghdad';
}

// ثوابت تخطيط واجهة المستخدم
class UIConstants {
  UIConstants._(); // منع إنشاء نسخة من الفئة

  // المسافات والأبعاد
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double sidebarWidth = 250.0;

  // أحجام الشاشة
  static const double minWindowWidth = 1200.0;
  static const double minWindowHeight = 800.0;
  static const double defaultWindowWidth = 1280.0;
  static const double defaultWindowHeight = 900.0;

  // مدد زمنية
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(seconds: 1);
}

// ثوابت قاعدة البيانات
class DbConstants {
  DbConstants._(); // منع إنشاء نسخة من الفئة

  // أسماء الجداول
  static const String tableUsers = 'users';
  static const String tableShipments = 'shipments';
  static const String tableCodeData = 'code_data';
  static const String tableGoodsData = 'goods_data';

  // أسماء الأعمدة المشتركة
  static const String columnId = 'id';
  static const String columnCreatedAt = 'created_at';
  static const String columnUpdatedAt = 'updated_at';
}

// ثوابت نصوص واجهة معلومات المرسل
class SenderInfoStrings {
  SenderInfoStrings._(); // منع إنشاء نسخة من الفئة

  // العناوين العامة
  static const String senderInformation = 'Sender Information';
  static const String senderName = 'Sender Name';
  static const String senderPhone = 'Sender Phone';
  static const String senderId = 'Sender ID Number';
  static const String goodsDescription = 'Goods Description';

  // نصوص الهوية
  static const String idType = 'ID Type';
  static const String idImage = 'ID Image';
  static const String selectIdTypeAndImage = 'Select ID Type and Image';
  static const String noImage = 'No Image';
  static const String selectImage = 'Select Image';
  static const String deleteImage = 'Delete Image';
  static const String saveToDevice = 'Save to Device';
  static const String imageSavedTo = 'Image saved to:';
  static const String errorSavingImage = 'Error saving image:';

  // نصوص البحث عن الهاتف
  static const String enterPhoneFirst = 'Please enter phone number first';
  static const String searching = 'Searching...';
  static const String searchResults = 'Search Results';
  static const String searchForPhone = 'Search for phone number:';
  static const String noMatchingRecords = 'No matching records found';
  static const String foundRecords = 'Found %d records for phone number:';
  static const String errorSearchingPhone = 'Error searching for phone number:';
  static const String enterDigits = 'Enter 11 digits';

  // نصوص البضائع
  static const String goodsList = 'Goods List';
  static const String itemArKu = 'Item Arabic/Kurdish';
  static const String itemEn = 'Item English';
  static const String add = 'Add';
  static const String import = 'Import';
  static const String export = 'Export';
  static const String search = 'Search';
  static const String quantity = 'Quantity:';
  static const String weight = 'Weight:';
  static const String addAndExit = 'Add and Exit';
  static const String enterItemNameBoth =
      'Please enter item name in both English and Arabic/Kurdish';
  static const String itemAlreadyExists =
      'This item already exists in the list';
  static const String editItemName = 'Edit Item Name';
  static const String nameAr = 'Arabic/Kurdish Name';
  static const String nameEn = 'English Name';
  static const String fillAllFields = 'Please fill all fields';

  // نصوص استيراد وتصدير البيانات
  static const String exportSuccess =
      'Data exported to Excel file successfully';
  static const String errorExportingData = 'Error exporting data:';
  static const String importSuccess =
      'Successfully imported %d items from sheet %s';
  static const String importSuccessGeneral =
      'Data imported from Excel file successfully';
  static const String duplicatesIgnored = ' and ignored %d duplicate items';
  static const String onlyDuplicatesFound =
      'Ignored %d duplicate items and no new items were imported from sheet %s';
  static const String noItemsImported =
      'No items were imported from sheet %s. Make sure the file format is correct';
  static const String fileHasNoData = 'File contains no data';
  static const String errorImportingData = 'Error importing data:';
  static const String permissionRequired =
      'Storage permission is required to export files';

  // رسالة نجاح التحديث
  static const String updateSuccess = 'Data updated successfully for code: %s';

  // الأزرار العامة
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String close = 'Close';
}
