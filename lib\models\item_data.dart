/// فئة لتمثيل بيانات العنصر
class ItemData {
  final int id;
  final String nameAr;
  final String nameEn;
  bool isSelected;
  int quantity;
  double weight;

  ItemData({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.isSelected = false,
    this.quantity = 1,
    this.weight = 0.0,
  });

  /// إنشاء نسخة جديدة من العنصر مع تحديث بعض الخصائص
  ItemData copyWith({
    int? id,
    String? nameAr,
    String? nameEn,
    bool? isSelected,
    int? quantity,
    double? weight,
  }) {
    return ItemData(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      isSelected: isSelected ?? this.isSelected,
      quantity: quantity ?? this.quantity,
      weight: weight ?? this.weight,
    );
  }

  /// تحويل العنصر إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nameAr': nameAr,
      'nameEn': nameEn,
      'isSelected': isSelected ? 1 : 0,
      'quantity': quantity,
      'weight': weight,
    };
  }

  /// إنشاء عنصر من خريطة
  factory ItemData.fromMap(Map<String, dynamic> map) {
    return ItemData(
      id: map['id'] as int,
      nameAr: map['nameAr'] as String,
      nameEn: map['nameEn'] as String,
      isSelected: map['isSelected'] == 1,
      quantity: map['quantity'] as int,
      weight: map['weight'] as double,
    );
  }

  @override
  String toString() {
    return 'ItemData(id: $id, nameAr: $nameAr, nameEn: $nameEn, isSelected: $isSelected, quantity: $quantity, weight: $weight)';
  }
}
