import 'package:flutter/material.dart';

class ImportProgressDialog extends StatelessWidget {
  final String title;
  final double progress;
  final int processedItems;
  final int totalItems;
  final String stage;

  const ImportProgressDialog({
    super.key,
    required this.title,
    required this.progress,
    required this.processedItems,
    required this.totalItems,
    required this.stage,
  });

  @override
  Widget build(BuildContext context) {
    // حساب النسبة المئوية للتقدم
    final percentage = (progress * 100).toInt();

    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض المرحلة الحالية
          Text('Stage: $stage'),
          const SizedBox(height: 16),

          // عرض عدد العناصر المعالجة
          Text('Processed $processedItems out of $totalItems items'),
          const SizedBox(height: 8),

          // عرض شريط التقدم
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor:
                AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
          ),
          const SizedBox(height: 8),

          // عرض النسبة المئوية للتقدم
          Text('$percentage%', textAlign: TextAlign.center),
        ],
      ),
      actions: const [
        // لا يمكن إلغاء العملية حاليًا، يمكن إضافة زر إلغاء في المستقبل
      ],
    );
  }
}
