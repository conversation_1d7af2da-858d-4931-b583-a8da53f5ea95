class NetherlandsAddress {
  final String postalCode;
  final String postalCodeNumbers;
  final String postalCodeLetters;
  final String street;
  final String minNummer;
  final String maxNummer;
  final String cityName;

  NetherlandsAddress({
    required this.postalCode,
    required this.postalCodeNumbers,
    required this.postalCodeLetters,
    required this.street,
    required this.minNummer,
    required this.maxNummer,
    required this.cityName,
  });

  factory NetherlandsAddress.fromMap(Map<String, dynamic> map) {
    return NetherlandsAddress(
      postalCode: map['postal_code'] ?? '',
      postalCodeNumbers: map['postal_code_numbers'] ?? '',
      postalCodeLetters: map['postal_code_letters'] ?? '',
      street: map['street'] ?? '',
      minNummer: map['min_nummer'] ?? '',
      maxNummer: map['max_nummer'] ?? '',
      cityName: map['city_name'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'postal_code': postalCode,
      'postal_code_numbers': postalCodeNumbers,
      'postal_code_letters': postalCodeLetters,
      'street': street,
      'min_nummer': minNummer,
      'max_nummer': maxNummer,
      'city_name': cityName,
    };
  }

  @override
  String toString() {
    return '$postalCode, $street, $cityName';
  }
}
