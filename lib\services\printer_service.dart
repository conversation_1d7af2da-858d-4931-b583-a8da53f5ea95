import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:win32/win32.dart';

/// خدمة الطابعات للحصول على قائمة الطابعات المثبتة
class PrinterService {
  static final PrinterService _instance = PrinterService._internal();
  factory PrinterService() => _instance;
  PrinterService._internal();

  /// الحصول على قائمة الطابعات المثبتة (Windows فقط)
  static Future<List<String>> getInstalledPrinters() async {
    if (!Platform.isWindows) {
      debugPrint('⚠️ خدمة الطابعات متاحة فقط على نظام Windows');
      return ['الطابعة الافتراضية'];
    }

    try {
      return await _getWindowsPrinters();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على قائمة الطابعات: $e');
      return ['الطابعة الافتراضية'];
    }
  }

  /// الحصول على الطابعة الافتراضية
  static Future<String?> getDefaultPrinter() async {
    if (!Platform.isWindows) {
      return 'الطابعة الافتراضية';
    }

    try {
      return await _getWindowsDefaultPrinter();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الطابعة الافتراضية: $e');
      return null;
    }
  }

  /// الحصول على قائمة الطابعات في Windows
  static Future<List<String>> _getWindowsPrinters() async {
    final printers = <String>[];
    
    try {
      // استخدام WMI للحصول على قائمة الطابعات
      final result = await Process.run(
        'powershell',
        [
          '-Command',
          'Get-WmiObject -Class Win32_Printer | Select-Object -ExpandProperty Name'
        ],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        final output = result.stdout.toString();
        final lines = output.split('\n');
        
        for (final line in lines) {
          final trimmed = line.trim();
          if (trimmed.isNotEmpty && !trimmed.startsWith('Name')) {
            printers.add(trimmed);
          }
        }
      }

      // إذا فشل PowerShell، جرب طريقة أخرى
      if (printers.isEmpty) {
        printers.addAll(await _getWindowsPrintersAlternative());
      }

      debugPrint('🖨️ تم العثور على ${printers.length} طابعة: $printers');
      return printers;
    } catch (e) {
      debugPrint('❌ خطأ في _getWindowsPrinters: $e');
      return await _getWindowsPrintersAlternative();
    }
  }

  /// طريقة بديلة للحصول على قائمة الطابعات باستخدام Win32 API
  static Future<List<String>> _getWindowsPrintersAlternative() async {
    final printers = <String>[];
    
    try {
      // استخدام EnumPrinters API
      final pPrinterEnum = calloc<PRINTER_INFO_2>();
      final pcbNeeded = calloc<DWORD>();
      final pcReturned = calloc<DWORD>();

      // الحصول على حجم البيانات المطلوب
      EnumPrinters(
        PRINTER_ENUM_LOCAL | PRINTER_ENUM_CONNECTIONS,
        nullptr,
        2, // PRINTER_INFO_2
        pPrinterEnum.cast<Uint8>(),
        0,
        pcbNeeded,
        pcReturned,
      );

      final bytesNeeded = pcbNeeded.value;
      if (bytesNeeded > 0) {
        final pPrinterInfo = calloc<Uint8>(bytesNeeded);
        
        final result = EnumPrinters(
          PRINTER_ENUM_LOCAL | PRINTER_ENUM_CONNECTIONS,
          nullptr,
          2,
          pPrinterInfo,
          bytesNeeded,
          pcbNeeded,
          pcReturned,
        );

        if (result != 0) {
          final numPrinters = pcReturned.value;
          final printerInfoSize = sizeOf<PRINTER_INFO_2>();
          
          for (int i = 0; i < numPrinters; i++) {
            final printerInfo = Pointer<PRINTER_INFO_2>.fromAddress(
              pPrinterInfo.address + (i * printerInfoSize)
            );
            
            if (printerInfo.ref.pPrinterName != nullptr) {
              final printerName = printerInfo.ref.pPrinterName.toDartString();
              if (printerName.isNotEmpty) {
                printers.add(printerName);
              }
            }
          }
        }
        
        calloc.free(pPrinterInfo);
      }

      calloc.free(pPrinterEnum);
      calloc.free(pcbNeeded);
      calloc.free(pcReturned);

      debugPrint('🖨️ تم العثور على ${printers.length} طابعة باستخدام Win32 API');
      return printers;
    } catch (e) {
      debugPrint('❌ خطأ في _getWindowsPrintersAlternative: $e');
      return ['الطابعة الافتراضية'];
    }
  }

  /// الحصول على الطابعة الافتراضية في Windows
  static Future<String?> _getWindowsDefaultPrinter() async {
    try {
      // استخدام PowerShell للحصول على الطابعة الافتراضية
      final result = await Process.run(
        'powershell',
        [
          '-Command',
          'Get-WmiObject -Class Win32_Printer | Where-Object {\$_.Default -eq \$true} | Select-Object -ExpandProperty Name'
        ],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        final output = result.stdout.toString().trim();
        if (output.isNotEmpty) {
          debugPrint('🖨️ الطابعة الافتراضية: $output');
          return output;
        }
      }

      // طريقة بديلة باستخدام Win32 API
      return await _getWindowsDefaultPrinterAlternative();
    } catch (e) {
      debugPrint('❌ خطأ في _getWindowsDefaultPrinter: $e');
      return await _getWindowsDefaultPrinterAlternative();
    }
  }

  /// طريقة بديلة للحصول على الطابعة الافتراضية باستخدام Win32 API
  static Future<String?> _getWindowsDefaultPrinterAlternative() async {
    try {
      final pPrinterName = wsalloc(256);
      final pcchBuffer = calloc<DWORD>();
      pcchBuffer.value = 256;

      final result = GetDefaultPrinter(pPrinterName, pcchBuffer);
      
      if (result != 0) {
        final defaultPrinter = pPrinterName.toDartString();
        free(pPrinterName);
        calloc.free(pcchBuffer);
        
        debugPrint('🖨️ الطابعة الافتراضية (Win32): $defaultPrinter');
        return defaultPrinter;
      }
      
      free(pPrinterName);
      calloc.free(pcchBuffer);
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في _getWindowsDefaultPrinterAlternative: $e');
      return null;
    }
  }

  /// التحقق من وجود طابعة معينة
  static Future<bool> isPrinterAvailable(String printerName) async {
    if (printerName.isEmpty) return false;
    
    final printers = await getInstalledPrinters();
    return printers.contains(printerName);
  }

  /// الحصول على معلومات الطابعة
  static Future<Map<String, dynamic>?> getPrinterInfo(String printerName) async {
    if (!Platform.isWindows || printerName.isEmpty) {
      return null;
    }

    try {
      final result = await Process.run(
        'powershell',
        [
          '-Command',
          'Get-WmiObject -Class Win32_Printer | Where-Object {\$_.Name -eq "$printerName"} | Select-Object Name, DriverName, PortName, PrinterStatus, WorkOffline'
        ],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        final output = result.stdout.toString();
        if (output.isNotEmpty) {
          // تحليل المخرجات وإرجاع معلومات الطابعة
          return {
            'name': printerName,
            'available': true,
            'details': output,
          };
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على معلومات الطابعة: $e');
      return null;
    }
  }

  /// تعيين الطابعة الافتراضية
  static Future<bool> setDefaultPrinter(String printerName) async {
    if (!Platform.isWindows || printerName.isEmpty) {
      return false;
    }

    try {
      final result = await Process.run(
        'powershell',
        [
          '-Command',
          '(Get-WmiObject -ComputerName . -Class Win32_Printer -Filter "Name=\'$printerName\'" | ForEach-Object{\$_.SetDefaultPrinter()}).ReturnValue'
        ],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        final output = result.stdout.toString().trim();
        final success = output == '0';
        
        if (success) {
          debugPrint('✅ تم تعيين $printerName كطابعة افتراضية');
        } else {
          debugPrint('❌ فشل في تعيين $printerName كطابعة افتراضية');
        }
        
        return success;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في تعيين الطابعة الافتراضية: $e');
      return false;
    }
  }

  /// تحديث قائمة الطابعات
  static Future<List<String>> refreshPrinters() async {
    debugPrint('🔄 تحديث قائمة الطابعات...');
    return await getInstalledPrinters();
  }
}