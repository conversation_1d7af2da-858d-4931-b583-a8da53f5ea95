import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../utils/constants.dart';
import 'dart:async';
import 'package:flutter/services.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;
  static Database? _branchDatabase;
  static const String _dbName = 'euknet_company.db';
  static const int _dbVersion = 13;
  String? _currentBranch;
  bool _isAdminMode = false; // للتمييز بين الأدمن ومستخدمي الفروع

  // قفل لمنع التداخل في عمليات قاعدة البيانات
  bool _isUpdatingBranch = false;

  // جداول قاعدة البيانات
  static const String tableUsers = 'users';
  static const String tableShipments = 'shipments';
  static const String tableCodeData = 'code_data';
  static const String tableGoodsData = 'goods_data';
  static const String tableGermanyAddresses = 'germany_addresses';
  static const String tableNetherlandsAddresses = 'netherlands_addresses';
  static const String tableSelectedItems = 'selected_items';
  static const String tableBranches = 'branches';
  static const String tableLastLoginInfo = 'last_login_info';

  // تهيئة قاعدة البيانات
  static Future<void> initializeDatabaseForPlatform() async {
    if (!kIsWeb &&
        (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
  }

  // تعيين الفرع الحالي مع البحث عن قاعدة البيانات الموجودة
  void setCurrentBranch(String branchName, {bool isAdmin = false}) {
    // ?? ??? ????? debugPrint
    // ?? ??? ????? debugPrint
    // ?? ??? ????? debugPrint

    // التحقق من وجود قفل تحديث
    if (_isUpdatingBranch) {
      debugPrint(
          '**** setCurrentBranch: تحذير: عملية تحديث قيد التشغيل، قد يؤدي إلى تداخل');
    }

    _currentBranch = branchName;
    _isAdminMode = isAdmin;

    // إغلاق قاعدة بيانات الفرع الحالية إذا كانت مفتوحة
    if (_branchDatabase != null && _branchDatabase!.isOpen) {
      // ?? ??? ????? debugPrint
      _branchDatabase!.close();
      _branchDatabase = null;
    }

    // طباعة حالة قواعد البيانات الموجودة
    _printExistingDatabases();
  }

  // طباعة قائمة بجميع قواعد البيانات الموجودة (للتشخيص)
  Future<void> _printExistingDatabases() async {
    try {
      Directory databaseDirectory = await _getDatabaseDirectory();
      databaseDirectory
          .listSync()
          .where((entity) => entity is File && entity.path.endsWith('.db'))
          .toList();
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  // الحصول على مسار قواعد البيانات المخصص
  Future<Directory> _getDatabaseDirectory() async {
    if (kReleaseMode) {
      // في وضع Release: إنشاء مجلد database بجانب ملف التطبيق
      try {
        String executablePath = Platform.resolvedExecutable;
        String appDirectory = dirname(executablePath);
        String databasePath = join(appDirectory, 'database');

        Directory databaseDir = Directory(databasePath);

        // إنشاء المجلد إذا لم يكن موجوداً
        if (!await databaseDir.exists()) {
          await databaseDir.create(recursive: true);
          // ?? ??? ????? debugPrint
        }

        // ?? ??? ????? debugPrint
        return databaseDir;
      } catch (e) {
        // ?? ??? ????? debugPrint
        // ?? ??? ????? debugPrint
        // العودة للمسار الافتراضي في حالة الخطأ
        return await getApplicationDocumentsDirectory();
      }
    } else {
      // في وضع Debug: استخدام مجلد Documents
      // ?? ??? ????? debugPrint
      return await getApplicationDocumentsDirectory();
    }
  }

  // الحصول على قاعدة بيانات الفرع
  Future<Database> get branchDatabase async {
    if (_currentBranch == null) {
      throw Exception('لم يتم تعيين الفرع الحالي');
    }

    // ?? ??? ????? debugPrint
    // ?? ??? ????? debugPrint

    if (_branchDatabase != null && _branchDatabase!.isOpen) {
      try {
        await _branchDatabase!.execute('PRAGMA temp_store = 2');
        // ?? ??? ????? debugPrint
        return _branchDatabase!;
      } catch (e) {
        debugPrint(
            '**** branchDatabase: قاعدة بيانات الفرع في وضع القراءة فقط، إعادة فتحها: $e');
        await _branchDatabase!.close();
        _branchDatabase = null;
      }
    }

    debugPrint(
        '**** branchDatabase: تهيئة قاعدة بيانات جديدة للفرع: $_currentBranch');
    _branchDatabase = await _initBranchDatabase(_currentBranch!);
    return _branchDatabase!;
  }

  // البحث المحسن عن قاعدة بيانات موجودة للفرع
  Future<String?> findExistingBranchDatabaseImproved(String branchName) async {
    try {
      debugPrint(
          '**** findExistingBranchDatabaseImproved: البحث عن قاعدة بيانات للفرع: $branchName');
      Directory documentsDirectory = await _getDatabaseDirectory();

      // الحصول على معلومات الفرع من قاعدة البيانات الرئيسية
      final db = await database;
      final branchData = await db.query(
        tableBranches,
        where: 'name = ?',
        whereArgs: [branchName],
        limit: 1,
      );

      if (branchData.isEmpty) {
        debugPrint(
            '**** findExistingBranchDatabaseImproved: فرع غير موجود: $branchName');
        return null;
      }

      String codePrefix = branchData.first['code_prefix'] as String;
      int branchId = branchData.first['id'] as int;
      debugPrint(
          '**** findExistingBranchDatabaseImproved: فرع "$branchName" - ID: $branchId - رمز: $codePrefix');

      // قائمة بأسماء محتملة لقاعدة البيانات (الاسم الحالي + أسماء قديمة محتملة)
      List<String> possibleNames = [
        branchName.toLowerCase().replaceAll(' ', '_'),
        branchName.toLowerCase().replaceAll(' ', ''),
        branchName.toLowerCase(),
      ];

      debugPrint(
          '**** findExistingBranchDatabaseImproved: أسماء محتملة: $possibleNames');

      // البحث المباشر بالأسماء المحتملة
      for (String name in possibleNames) {
        String possibleDbName = 'euknet_$name.db';
        String possiblePath = join(documentsDirectory.path, possibleDbName);
        File possibleFile = File(possiblePath);

        if (await possibleFile.exists()) {
          debugPrint(
              '**** findExistingBranchDatabaseImproved: وُجد ملف محتمل: $possibleDbName');

          // التحقق من أن هذا الملف يحتوي على بيانات هذا الفرع
          if (await _verifyBranchDatabase(possiblePath, codePrefix)) {
            debugPrint(
                '**** findExistingBranchDatabaseImproved: تم التحقق من صحة قاعدة البيانات: $possibleDbName');
            return possiblePath;
          }
        }
      }

      // البحث في جميع ملفات قواعد البيانات الموجودة
      List<FileSystemEntity> allDbFiles = documentsDirectory
          .listSync()
          .where((entity) =>
              entity is File &&
              entity.path.contains('euknet_') &&
              entity.path.endsWith('.db') &&
              !entity.path.contains('euknet_company.db'))
          .toList();

      debugPrint(
          '**** findExistingBranchDatabaseImproved: فحص ${allDbFiles.length} ملف قاعدة بيانات');

      for (var file in allDbFiles) {
        String fileName = basename(file.path);
        debugPrint(
            '**** findExistingBranchDatabaseImproved: فحص ملف: $fileName');

        if (await _verifyBranchDatabase(file.path, codePrefix)) {
          debugPrint(
              '**** findExistingBranchDatabaseImproved: تم العثور على قاعدة بيانات صحيحة: $fileName');
          return file.path;
        }
      }

      debugPrint(
          '**** findExistingBranchDatabaseImproved: لم يتم العثور على قاعدة بيانات للفرع');
      return null;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return null;
    }
  }

  // فحص أن قاعدة البيانات تخص الفرع الصحيح مع التعامل مع الملفات المُقفلة
  Future<bool> _verifyBranchDatabase(String filePath, String branchName) async {
    Database? tempDb;
    try {
      debugPrint(
          '**** _verifyBranchDatabase: فحص ${basename(filePath)} للفرع: $branchName');

      // أولاً: فحص اسم الملف للتطابق السريع
      String fileName = basename(filePath).toLowerCase();
      String branchPattern = branchName.toLowerCase().replaceAll(' ', '_');

      if (fileName.contains(branchPattern)) {
        debugPrint(
            '**** _verifyBranchDatabase: ${basename(filePath)} - تطابق الاسم، قبول مباشر');
        return true;
      }

      // محاولة فتح قاعدة البيانات في وضع القراءة فقط
      try {
        tempDb = await openDatabase(
          filePath,
          readOnly: true,
          singleInstance: false,
        );
      } catch (e) {
        debugPrint(
            '**** _verifyBranchDatabase: خطأ في فتح ${basename(filePath)}: $e');

        // إذا كانت القاعدة مُقفلة، جرب طريقة أخرى
        if (e.toString().contains('database_closed') ||
            e.toString().contains('being used') ||
            e.toString().contains('locked')) {
          debugPrint(
              '**** _verifyBranchDatabase: ${basename(filePath)} - قاعدة بيانات مُقفلة، سنعتبرها صالحة');

          // التحقق من اسم الملف فقط إذا كانت مُقفلة
          if (fileName.contains(branchPattern)) {
            return true;
          }
        }
        return false;
      }

      // فحص الجداول الأساسية
      List<Map<String, dynamic>> tables = await tempDb.query(
        'sqlite_master',
        where: 'type = ?',
        whereArgs: ['table'],
      );

      bool hasCodeDataTable =
          tables.any((table) => table['name'] == 'code_data');

      if (!hasCodeDataTable) {
        debugPrint(
            '**** _verifyBranchDatabase: ${basename(filePath)} - لا يحتوي على جدول code_data');
        return false;
      }

      // فحص البيانات
      List<Map<String, dynamic>> codeData =
          await tempDb.query('code_data', limit: 1);

      if (codeData.isEmpty) {
        debugPrint(
            '**** _verifyBranchDatabase: ${basename(filePath)} - قاعدة بيانات فارغة (صالحة)');
        return true;
      }

      // التحقق من رمز الفرع في البيانات
      String? codePrefix = codeData.first['code']?.toString().substring(0, 2);
      if (codePrefix != null) {
        // الحصول على رمز الفرع المتوقع
        final db = await database;
        final branchData = await db.query(
          tableBranches,
          where: 'name = ?',
          whereArgs: [branchName],
          limit: 1,
        );

        if (branchData.isNotEmpty) {
          String expectedPrefix = branchData.first['code'] as String;
          if (codePrefix == expectedPrefix) {
            debugPrint(
                '**** _verifyBranchDatabase: ${basename(filePath)} - مطابق للفرع $branchName');
            return true;
          }
        }
      }

      debugPrint(
          '**** _verifyBranchDatabase: ${basename(filePath)} - غير مطابق للفرع $branchName');
      return false;
    } catch (e) {
      debugPrint(
          '**** _verifyBranchDatabase: خطأ في فحص ${basename(filePath)}: $e');

      // في حالة الخطأ، نفترض أنها صالحة إذا كان اسم الملف يحتوي على اسم الفرع
      String fileName = basename(filePath).toLowerCase();
      String branchPattern = branchName.toLowerCase().replaceAll(' ', '_');
      if (fileName.contains(branchPattern)) {
        debugPrint(
            '**** _verifyBranchDatabase: ${basename(filePath)} - تم قبولها بناءً على اسم الملف');
        return true;
      }

      return false;
    } finally {
      if (tempDb != null && tempDb.isOpen) {
        try {
          await tempDb.close();
        } catch (e) {
          debugPrint(
              '**** _verifyBranchDatabase: خطأ في إغلاق قاعدة البيانات المؤقتة: $e');
        }
      }
    }
  }

  // البحث عن قاعدة بيانات موجودة للفرع باستخدام رمز الفرع
  Future<String?> findExistingBranchDatabase(String branchName) async {
    try {
      debugPrint(
          '**** findExistingBranchDatabase: البحث عن قاعدة بيانات موجودة للفرع: $branchName');
      Directory documentsDirectory = await _getDatabaseDirectory();

      // البحث عن بيانات الفرع من قاعدة البيانات الرئيسية
      final db = await database;
      final branchData = await db.query(
        tableBranches,
        where: 'name = ?',
        whereArgs: [branchName],
        limit: 1,
      );

      if (branchData.isEmpty) {
        debugPrint(
            '**** findExistingBranchDatabase: لم يتم العثور على الفرع في قاعدة البيانات: $branchName');
        return null;
      }

      String codePrefix = branchData.first['code_prefix'] as String;
      debugPrint(
          '**** findExistingBranchDatabase: رمز الفرع $branchName: $codePrefix');

      // البحث عن جميع ملفات قواعد البيانات في المجلد
      List<FileSystemEntity> files = documentsDirectory
          .listSync()
          .where((entity) =>
              entity is File &&
              entity.path.contains('euknet_') &&
              entity.path.endsWith('.db') &&
              !entity.path.contains('euknet_company.db'))
          .toList();

      debugPrint(
          '**** findExistingBranchDatabase: ملفات قواعد البيانات الموجودة: ${files.map((f) => basename(f.path)).toList()}');

      // فحص كل ملف للبحث عن بيانات تحتوي على رمز هذا الفرع
      for (var file in files) {
        try {
          debugPrint(
              '**** findExistingBranchDatabase: فحص قاعدة البيانات: ${basename(file.path)}');

          // فتح قاعدة البيانات للفحص
          final testDb = await openDatabase(
            file.path,
            readOnly: true,
          );

          // البحث عن كود يحتوي على رمز الفرع
          final codeData = await testDb.query(
            tableCodeData,
            where: 'code_no LIKE ?',
            whereArgs: ['$codePrefix%'],
            limit: 1,
          );

          await testDb.close();

          if (codeData.isNotEmpty) {
            debugPrint(
                '**** findExistingBranchDatabase: تم العثور على قاعدة بيانات موجودة للفرع "$branchName" برمز "$codePrefix" في: ${basename(file.path)}');
            return file.path;
          } else {
            debugPrint(
                '**** findExistingBranchDatabase: لا توجد بيانات برمز "$codePrefix" في ${basename(file.path)}');
          }
        } catch (e) {
          debugPrint(
              '**** findExistingBranchDatabase: خطأ في فحص قاعدة البيانات ${basename(file.path)}: $e');
        }
      }

      debugPrint(
          '**** findExistingBranchDatabase: لم يتم العثور على قاعدة بيانات موجودة للفرع "$branchName" برمز "$codePrefix"');
      return null;
    } catch (e) {
      debugPrint(
          '**** findExistingBranchDatabase: خطأ في البحث عن قاعدة بيانات الفرع: $e');
      return null;
    }
  }

  // تهيئة قاعدة بيانات الفرع مع منع إنشاء ملفات جديدة غير ضرورية
  Future<Database> _initBranchDatabase(String branchName) async {
    await initializeDatabaseForPlatform();
    Directory documentsDirectory = await _getDatabaseDirectory();

    // إنشاء اسم ملف قاعدة البيانات الخاص بالفرع
    String branchDbName =
        'euknet_${branchName.toLowerCase().replaceAll(' ', '_')}.db';
    String path = join(documentsDirectory.path, branchDbName);

    // ?? ??? ????? debugPrint
    // ?? ??? ????? debugPrint

    // ALWAYS البحث عن قاعدة بيانات موجودة أولاً، حتى لو كان الملف موجود
    debugPrint(
        '**** _initBranchDatabase: البحث الإجباري عن قاعدة بيانات موجودة');
    String? existingDbPath =
        await findExistingBranchDatabaseImproved(branchName);

    String finalPath = path; // المسار الذي سيتم استخدامه فعلياً

    if (existingDbPath != null && existingDbPath != path) {
      debugPrint(
          '**** _initBranchDatabase: وُجدت قاعدة بيانات موجودة بمسار مختلف: ${basename(existingDbPath)}');
      // ?? ??? ????? debugPrint}');

      try {
        File existingFile = File(existingDbPath);
        File targetFile = File(path);

        // حذف الملف المُراد إنشاؤه إذا كان موجوداً
        if (await targetFile.exists()) {
          debugPrint(
              '**** _initBranchDatabase: حذف ملف موجود بالاسم الجديد: ${basename(path)}');
          try {
            await targetFile.delete();
          } catch (e) {
            debugPrint(
                '**** _initBranchDatabase: لا يمكن حذف الملف المُراد: $e');
          }
        }

        // محاولة إعادة تسمية الملف الموجود
        await existingFile.rename(path);
        debugPrint(
            '**** _initBranchDatabase: تم إعادة تسمية قاعدة البيانات من ${basename(existingDbPath)} إلى ${basename(path)}');
        finalPath = path;
      } catch (e) {
        debugPrint(
            '**** _initBranchDatabase: خطأ في إعادة تسمية قاعدة البيانات: $e');

        // إذا فشلت إعادة التسمية، استخدم الملف الموجود مباشرة
        if (e.toString().contains('being used') ||
            e.toString().contains('locked')) {
          debugPrint(
              '**** _initBranchDatabase: سنستخدم الملف الموجود مباشرة: ${basename(existingDbPath)}');
          finalPath = existingDbPath;
        }
      }
    }

    // التحقق من وجود ملف قاعدة البيانات
    File dbFile = File(finalPath);
    if (!await dbFile.exists()) {
      debugPrint(
          '**** _initBranchDatabase: الملف غير موجود بعد البحث، إنشاء ملف جديد');
      debugPrint(
          '**** _initBranchDatabase: التأكد من عدم وجود أي قواعد بيانات أخرى للفرع');

      // فحص نهائي للتأكد من عدم وجود أي ملفات أخرى
      await _printExistingDatabases();

      try {
        await dbFile.create(recursive: true);
        debugPrint(
            '**** _initBranchDatabase: تم إنشاء ملف قاعدة بيانات جديد: ${basename(finalPath)}');

        if (!kIsWeb &&
            (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
          try {
            await Process.run('chmod', ['666', finalPath]);
            // ?? ??? ????? debugPrint
          } catch (e) {
            debugPrint(
                '**** _initBranchDatabase: لا يمكن تعيين صلاحيات الملف: $e');
          }
        }
      } catch (e) {
        debugPrint(
            '**** _initBranchDatabase: خطأ في إنشاء ملف قاعدة بيانات الفرع: $e');
      }
    } else {
      debugPrint(
          '**** _initBranchDatabase: الملف موجود: ${basename(finalPath)}');
    }

    final options = OpenDatabaseOptions(
      version: _dbVersion,
      onCreate: _createBranchDatabase,
      onUpgrade: _upgradeBranchDatabase,
      readOnly: false,
      singleInstance: false,
    );

    try {
      debugPrint(
          '**** _initBranchDatabase: محاولة فتح قاعدة البيانات: ${basename(finalPath)}');
      final db =
          await databaseFactoryFfi.openDatabase(finalPath, options: options);
      debugPrint(
          '**** _initBranchDatabase: تم فتح قاعدة البيانات بنجاح من: ${basename(finalPath)}');
      return db;
    } catch (e) {
      // ?? ??? ????? debugPrint
      try {
        final db = await openDatabase(
          finalPath,
          version: _dbVersion,
          onCreate: _createBranchDatabase,
          onUpgrade: _upgradeBranchDatabase,
          singleInstance: false,
          readOnly: false,
        );
        debugPrint(
            '**** _initBranchDatabase: تم فتح قاعدة البيانات بالطريقة البديلة من: ${basename(finalPath)}');
        return db;
      } catch (e2) {
        debugPrint(
            '**** _initBranchDatabase: فشل نهائي في فتح قاعدة بيانات الفرع: $e2');
        throw Exception('لا يمكن فتح قاعدة بيانات الفرع: $e2');
      }
    }
  }

  // إنشاء قاعدة بيانات الفرع (فقط الجداول الخاصة بالفرع)
  Future<void> _createBranchDatabase(Database db, int version) async {
    // إنشاء جدول بيانات الكود فقط (بدون باقي الجداول المشتركة)
    await _createCodeDataTable(db);

    // إنشاء جدول العناصر المحددة
    await _createSelectedItemsTable(db);

    // ?? ??? ????? debugPrint
  }

  // ترقية قاعدة بيانات الفرع
  Future<void> _upgradeBranchDatabase(
      Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await _createCodeDataTable(db);
    }

    if (oldVersion < 3) {
      await _addColumnsToCodeData(db);
    }

    if (oldVersion < 4) {
      await _addBooleanColumnsToCodeData(db);
    }

    if (oldVersion < 12) {
      // إضافة أعمدة معلومات الوكيل وصورة العلم إلى جدول بيانات الكود
      try {
        var tableInfo = await db.rawQuery("PRAGMA table_info($tableCodeData)");
        var columnNames =
            tableInfo.map((col) => col['name'] as String).toList();

        // إضافة عمود صورة العلم
        if (!columnNames.contains('flag_image')) {
          await db
              .execute('ALTER TABLE $tableCodeData ADD COLUMN flag_image BLOB');
          // ?? ??? ????? debugPrint
        }

        // إضافة أعمدة معلومات الوكيل
        if (!columnNames.contains('agent_name')) {
          await db
              .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_name TEXT');
          // ?? ??? ????? debugPrint
        }

        if (!columnNames.contains('agent_phone1')) {
          await db.execute(
              'ALTER TABLE $tableCodeData ADD COLUMN agent_phone1 TEXT');
          // ?? ??? ????? debugPrint
        }

        if (!columnNames.contains('agent_phone2')) {
          await db.execute(
              'ALTER TABLE $tableCodeData ADD COLUMN agent_phone2 TEXT');
          // ?? ??? ????? debugPrint
        }

        if (!columnNames.contains('agent_address')) {
          await db.execute(
              'ALTER TABLE $tableCodeData ADD COLUMN agent_address TEXT');
          // ?? ??? ????? debugPrint
        }
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    // ?? ??? ????? debugPrint

    // إضافة تحديث للإصدار 13 - عمود تعطيل الدول
    if (oldVersion < 13) {
      await _addCountryEnableColumn(db);
    }
  }

  Future<Database> get database async {
    if (_database != null && _database!.isOpen) {
      // التحقق من أن قاعدة البيانات ليست في وضع القراءة فقط
      // إذا كانت في وضع القراءة فقط، أغلقها وأعد فتحها
      try {
        // محاولة تنفيذ استعلام كتابة بسيط للتحقق مما إذا كانت قاعدة البيانات قابلة للكتابة
        await _database!.execute('PRAGMA temp_store = 2');
        return _database!;
      } catch (e) {
        // من المحتمل أن قاعدة البيانات في وضع القراءة فقط، أغلقها وأعد فتحها
        // ?? ??? ????? debugPrint
        await _database!.close();
        _database = null;
      }
    }

    // إذا لم يكن هناك اتصال مفتوح أو تمت إعادة تعيينه، قم بفتح قاعدة البيانات
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    await initializeDatabaseForPlatform();
    Directory documentsDirectory = await _getDatabaseDirectory();
    String path = join(documentsDirectory.path, _dbName);

    // التحقق مما إذا كان ملف قاعدة البيانات موجوداً، إذا لم يكن موجوداً، نسخ من مجلد الأصول
    await _copyDatabaseFromAssets(path);

    // إنشاء ملف قاعدة البيانات إذا لم يكن موجوداً
    File dbFile = File(path);
    if (!await dbFile.exists()) {
      try {
        await dbFile.create(recursive: true);
        // تعيين صلاحيات الكتابة على الملف الجديد
        if (!kIsWeb &&
            (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
          try {
            await Process.run('chmod', ['666', path]);
            debugPrint(
                'تم تعيين صلاحيات الكتابة على ملف قاعدة البيانات الجديد');
          } catch (e) {
            // ?? ??? ????? debugPrint
          }
        }
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    } else {
      // التحقق من صلاحيات الكتابة على الملف الموجود
      try {
        // محاولة الكتابة على الملف للتأكد من أنه ليس في وضع القراءة فقط
        await dbFile.writeAsString('', mode: FileMode.append);
      } catch (e) {
        // ?? ??? ????? debugPrint

        // محاولة تغيير صلاحيات الملف
        if (!kIsWeb &&
            (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
          try {
            await Process.run('chmod', ['666', path]);
            // ?? ??? ????? debugPrint
          } catch (e2) {
            // ?? ??? ????? debugPrint
          }
        }

        // محاولة إنشاء نسخة جديدة من الملف بصلاحيات كاملة
        try {
          String tempPath = join(documentsDirectory.path, 'temp_$_dbName');
          await dbFile.copy(tempPath);
          File tempFile = File(tempPath);

          // تغيير صلاحيات الملف المؤقت
          if (!kIsWeb &&
              (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
            await Process.run('chmod', ['666', tempPath]);
          }

          // حذف الملف الأصلي إذا أمكن
          try {
            await dbFile.delete();
            // نقل الملف المؤقت ليحل محل الملف الأصلي
            await tempFile.rename(path);
            // ?? ??? ????? debugPrint
          } catch (e4) {
            // ?? ??? ????? debugPrint
          }
        } catch (e3) {
          // ?? ??? ????? debugPrint
        }
      }
    }

    // تهيئة قاعدة البيانات مع الخيارات الصريحة
    final options = OpenDatabaseOptions(
      version: _dbVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      readOnly: false,
      singleInstance: false,
    );

    try {
      debugPrint(
          'محاولة فتح قاعدة البيانات مع: readOnly=false, singleInstance=false');
      return await databaseFactoryFfi.openDatabase(path, options: options);
    } catch (e) {
      // ?? ??? ????? debugPrint

      // محاولة ثانية مع خيارات مختلفة
      try {
        // ?? ??? ????? debugPrint
        return await openDatabase(
          path,
          version: _dbVersion,
          onCreate: _onCreate,
          onUpgrade: _onUpgrade,
          singleInstance: false,
          readOnly: false,
        );
      } catch (e2) {
        // ?? ??? ????? debugPrint
        throw Exception('لا يمكن فتح قاعدة البيانات: $e2');
      }
    }
  }

  // دالة لنسخ قاعدة البيانات من مجلد الأصول إذا لم تكن موجودة
  Future<void> _copyDatabaseFromAssets(String dbPath) async {
    try {
      // التحقق مما إذا كان ملف قاعدة البيانات موجوداً
      if (!await File(dbPath).exists()) {
        // ?? ??? ????? debugPrint

        // التأكد من وجود المجلد الذي سيحتوي على قاعدة البيانات
        await Directory(dirname(dbPath)).create(recursive: true);

        // محاولة الوصول إلى ملف قاعدة البيانات من مجلد الأصول
        try {
          // نسخ قاعدة البيانات من مجلد الأصول إلى المسار المطلوب
          ByteData data =
              await rootBundle.load('assets/database/euknet_company.db');
          List<int> bytes =
              data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
          await File(dbPath).writeAsBytes(bytes, flush: true);
          // ?? ??? ????? debugPrint
        } catch (e) {
          // ?? ??? ????? debugPrint
        }
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // إنشاء جدول المستخدمين
    await db.execute('''
      CREATE TABLE ${DbConstants.tableUsers} (
        ${DbConstants.columnId} INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        role TEXT NOT NULL,
        branch_id INTEGER,
        ${DbConstants.columnCreatedAt} TEXT NOT NULL,
        FOREIGN KEY (branch_id) REFERENCES $tableBranches(id) ON DELETE SET NULL
      )
    ''');

    // إنشاء جدول الشحنات
    await db.execute('''
      CREATE TABLE ${DbConstants.tableShipments} (
        ${DbConstants.columnId} INTEGER PRIMARY KEY AUTOINCREMENT,
        tracking_number TEXT NOT NULL UNIQUE,
        origin TEXT NOT NULL,
        destination TEXT NOT NULL,
        status TEXT NOT NULL,
        ${DbConstants.columnCreatedAt} TEXT NOT NULL,
        ${DbConstants.columnUpdatedAt} TEXT NOT NULL,
        customer_id INTEGER,
        weight REAL,
        price REAL,
        estimated_delivery TEXT
      )
    ''');

    // إنشاء جدول معلومات آخر تسجيل دخول
    await db.execute('''
      CREATE TABLE $tableLastLoginInfo (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        branch_id INTEGER,
        login_time TEXT NOT NULL
      )
    ''');

    // إنشاء جدول بيانات الكود
    await _createCodeDataTable(db);

    // إنشاء جدول بيانات البضائع
    await _createGoodsDataTable(db);

    // إنشاء جداول العناوين
    await _createAddressTables(db);

    // إنشاء جدول العناصر المحددة
    await _createSelectedItemsTable(db);

    // إنشاء جدول الفروع
    await _createBranchesTable(db);

    // إدخال بيانات المستخدم الافتراضي (المسؤول)
    await db.insert(DbConstants.tableUsers, {
      'name': 'Admin User',
      'email': '<EMAIL>',
      'password': AppConstants.defaultPassword,
      'role': 'admin',
      DbConstants.columnCreatedAt: DateTime.now().toIso8601String(),
    });

    // إدخال بيانات البضائع الافتراضية
    await _createInitialGoodsData(db);

    // إدخال بيانات الفرع الافتراضي
    await _createInitialBranch(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await _createCodeDataTable(db);
    }

    if (oldVersion < 3) {
      await _addColumnsToCodeData(db);
    }

    if (oldVersion < 4) {
      await _addBooleanColumnsToCodeData(db);
    }

    if (oldVersion < 5) {
      await _createGoodsDataTable(db);
      await _createInitialGoodsData(db);
    }

    if (oldVersion < 6) {
      await _createAddressTables(db);
    }

    if (oldVersion < 7) {
      await _createBranchesTable(db);
      await _createInitialBranch(db);
    }

    if (oldVersion < 8) {
      // إضافة حقل branch_id إلى جدول المستخدمين
      try {
        var tableInfo =
            await db.rawQuery("PRAGMA table_info(${DbConstants.tableUsers})");
        var columnNames =
            tableInfo.map((col) => col['name'] as String).toList();

        if (!columnNames.contains('branch_id')) {
          await db.execute(
              'ALTER TABLE ${DbConstants.tableUsers} ADD COLUMN branch_id INTEGER REFERENCES $tableBranches(id) ON DELETE SET NULL');
        }
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    if (oldVersion < 9) {
      // إنشاء جدول معلومات آخر تسجيل دخول
      try {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS $tableLastLoginInfo (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            branch_id INTEGER,
            login_time TEXT NOT NULL
          )
        ''');
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    if (oldVersion < 10) {
      // إضافة حقل code_prefix إلى جدول الفروع
      try {
        var tableInfo = await db.rawQuery("PRAGMA table_info($tableBranches)");
        var columnNames =
            tableInfo.map((col) => col['name'] as String).toList();

        if (!columnNames.contains('code_prefix')) {
          await db.execute(
              'ALTER TABLE $tableBranches ADD COLUMN code_prefix TEXT');

          // تعيين قيم افتراضية للفروع الموجودة
          await db.update(tableBranches, {'code_prefix': 'BA'},
              where: 'name = ?', whereArgs: ['Baghdad']);
          await db.update(tableBranches, {'code_prefix': 'HA'},
              where: 'name = ?', whereArgs: ['Erbil']);
          await db.update(tableBranches, {'code_prefix': 'HW'},
              where: 'name = ?', whereArgs: ['Erbil-2']);
          await db.update(tableBranches, {'code_prefix': 'DU'},
              where: 'name = ?', whereArgs: ['Duhuk']);
          await db.update(tableBranches, {'code_prefix': 'SU'},
              where: 'name = ?', whereArgs: ['Sulaymany']);
          await db.update(tableBranches, {'code_prefix': 'MU'},
              where: 'name = ?', whereArgs: ['Bashiqa']);
          await db.update(tableBranches, {'code_prefix': 'KI'},
              where: 'name = ?', whereArgs: ['Kirkuk']);
        }
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    if (oldVersion < 11) {
      // إضافة حقل code_digits إلى جدول الفروع
      try {
        var tableInfo = await db.rawQuery("PRAGMA table_info($tableBranches)");
        var columnNames =
            tableInfo.map((col) => col['name'] as String).toList();

        if (!columnNames.contains('code_digits')) {
          await db.execute(
              'ALTER TABLE $tableBranches ADD COLUMN code_digits TEXT');

          // تعيين قيم افتراضية للفروع الموجودة
          await db.update(tableBranches, {'code_digits': '25'}, where: '1=1');
        }
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    if (oldVersion < 12) {
      // إضافة أعمدة معلومات الوكيل وصورة العلم إلى جدول بيانات الكود
      try {
        var tableInfo = await db.rawQuery("PRAGMA table_info($tableCodeData)");
        var columnNames =
            tableInfo.map((col) => col['name'] as String).toList();

        // إضافة عمود صورة العلم
        if (!columnNames.contains('flag_image')) {
          await db
              .execute('ALTER TABLE $tableCodeData ADD COLUMN flag_image BLOB');
          // ?? ??? ????? debugPrint
        }

        // إضافة عمود اسم الوكيل
        if (!columnNames.contains('agent_name')) {
          await db
              .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_name TEXT');
          // ?? ??? ????? debugPrint
        }

        // إضافة عمود رقم هاتف الوكيل 1
        if (!columnNames.contains('agent_phone1')) {
          await db.execute(
              'ALTER TABLE $tableCodeData ADD COLUMN agent_phone1 TEXT');
          // ?? ??? ????? debugPrint
        }

        // إضافة عمود رقم هاتف الوكيل 2
        if (!columnNames.contains('agent_phone2')) {
          await db.execute(
              'ALTER TABLE $tableCodeData ADD COLUMN agent_phone2 TEXT');
          // ?? ??? ????? debugPrint
        }

        // إضافة عمود عنوان الوكيل
        if (!columnNames.contains('agent_address')) {
          await db.execute(
              'ALTER TABLE $tableCodeData ADD COLUMN agent_address TEXT');
          // ?? ??? ????? debugPrint
        }
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    // إضافة تحديث للإصدار 13 - عمود تعطيل الدول
    if (oldVersion < 13) {
      await _addCountryEnableColumn(db);
    }
  }

  Future<void> _addBooleanColumnsToCodeData(Database db) async {
    try {
      var tableInfo =
          await db.rawQuery("PRAGMA table_info(${DbConstants.tableCodeData})");
      var columnNames = tableInfo.map((col) => col['name'] as String).toList();

      if (!columnNames.contains('use_insurance')) {
        await db.execute(
            'ALTER TABLE ${DbConstants.tableCodeData} ADD COLUMN use_insurance INTEGER DEFAULT 0');
      }

      if (!columnNames.contains('transfer_unpaid_to_paid')) {
        await db.execute(
            'ALTER TABLE ${DbConstants.tableCodeData} ADD COLUMN transfer_unpaid_to_paid INTEGER DEFAULT 0');
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  Future<void> _addColumnsToCodeData(Database db) async {
    try {
      var tableInfo =
          await db.rawQuery("PRAGMA table_info(${DbConstants.tableCodeData})");
      var columnNames = tableInfo.map((col) => col['name'] as String).toList();

      if (!columnNames.contains('sender_id_type')) {
        await db.execute(
            'ALTER TABLE ${DbConstants.tableCodeData} ADD COLUMN sender_id_type TEXT');
      }

      if (!columnNames.contains('sender_id_image_path')) {
        await db.execute(
            'ALTER TABLE ${DbConstants.tableCodeData} ADD COLUMN sender_id_image_path TEXT');
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  Future<void> _createCodeDataTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DbConstants.tableCodeData} (
        ${DbConstants.columnId} INTEGER PRIMARY KEY AUTOINCREMENT,
        truck_no TEXT,
        code_no TEXT UNIQUE NOT NULL,
        date TEXT,
        code_list TEXT,
        sender_name TEXT,
        sender_phone TEXT,
        sender_id TEXT,
        sender_id_type TEXT,
        sender_id_image_path TEXT,
        receiver_name TEXT,
        receiver_phone TEXT,
        goods_description TEXT,
        box_no INTEGER,
        pallet_no INTEGER,
        real_weight_kg REAL,
        height REAL,
        length REAL,
        width REAL,
        factor REAL,
        volume_weight REAL,
        additional_kg REAL,
        total_weight_kg REAL,
        country TEXT,
        city TEXT,
        street_name_no TEXT,
        postal_code TEXT,
        city_name TEXT,
        receiver_email TEXT,
        price_door_to_door REAL,
        for_each_1_kg REAL,
        minimum_price REAL,
        exchange_rate REAL,
        notes TEXT,
        use_insurance INTEGER DEFAULT 0,
        insurance_percent REAL,
        goods_value REAL,
        insurance_amount REAL,
        export_doc REAL,
        box_packing_cost REAL,
        door_to_door_cost REAL,
        post_sub_cost REAL,
        discount_amount REAL,
        total_post_cost REAL,
        total_paid REAL,
        unpaid_amount REAL,
        total_cost_eur REAL,
        unpaid_eur REAL,
        transfer_unpaid_to_paid INTEGER DEFAULT 0,
        is_enabled INTEGER DEFAULT 1,
        ${DbConstants.columnCreatedAt} TEXT,
        ${DbConstants.columnUpdatedAt} TEXT
      )
    ''');

    // إضافة فهارس لتحسين أداء البحث
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_country ON ${DbConstants.tableCodeData} (country)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_city ON ${DbConstants.tableCodeData} (city)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_country_city ON ${DbConstants.tableCodeData} (country, city)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_code_no ON ${DbConstants.tableCodeData} (code_no)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_receiver_name ON ${DbConstants.tableCodeData} (receiver_name)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_sender_name ON ${DbConstants.tableCodeData} (sender_name)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_date ON ${DbConstants.tableCodeData} (date)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_code_data_is_enabled ON ${DbConstants.tableCodeData} (is_enabled)');
  }

  Future<void> _createGoodsDataTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${DbConstants.tableGoodsData} (
        ${DbConstants.columnId} INTEGER PRIMARY KEY AUTOINCREMENT,
        name_ar TEXT NOT NULL,
        name_en TEXT NOT NULL,
        ${DbConstants.columnCreatedAt} TEXT NOT NULL,
        ${DbConstants.columnUpdatedAt} TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createAddressTables(Database db) async {
    // إنشاء جدول عناوين ألمانيا
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableGermanyAddresses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        postal_code TEXT NOT NULL,
        city_name TEXT NOT NULL
      )
    ''');

    // إنشاء جدول عناوين هولندا
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableNetherlandsAddresses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        postal_code TEXT NOT NULL,
        postal_code_numbers TEXT NOT NULL,
        postal_code_letters TEXT NOT NULL,
        street TEXT NOT NULL,
        min_nummer TEXT NOT NULL,
        max_nummer TEXT NOT NULL,
        city_name TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createSelectedItemsTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableSelectedItems (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code_no TEXT NOT NULL,
        item_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 1,
        weight REAL NOT NULL DEFAULT 0.0,
        FOREIGN KEY (code_no) REFERENCES ${DbConstants.tableCodeData}(code_no) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES ${DbConstants.tableGoodsData}(${DbConstants.columnId}) ON DELETE CASCADE
      )
    ''');
  }

  Future<void> _createBranchesTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableBranches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        phone TEXT,
        email TEXT,
        code_prefix TEXT,
        code_digits TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createInitialBranch(Database db) async {
    // تم إزالة إنشاء الفروع الافتراضية، وستظهر فقط الفروع التي أضافها المستخدم
    // الفروع ستكون فارغة عند بدء استخدام التطبيق لأول مرة
  }

  Future<void> _createInitialGoodsData(Database db) async {
    final goodsList = [
      {'name_ar': 'ملابس', 'name_en': 'Clothes'},
      {'name_ar': 'كوزمتك', 'name_en': 'Cosmetics'},
      {'name_ar': 'مفروشات', 'name_en': 'Furniture'},
      {'name_ar': 'منزلية مطبخ', 'name_en': 'Kitchen Supplies'},
      {'name_ar': 'ماكولات', 'name_en': 'Food'},
      {'name_ar': 'بهارات', 'name_en': 'Spices'},
      {'name_ar': 'تحفيات', 'name_en': 'Antiques'},
      {'name_ar': 'العاب اطفال', 'name_en': 'Children Toys'},
      {'name_ar': 'اكسسوارات بناتية', 'name_en': 'Women Accessories'},
      {'name_ar': 'اكسسوارات رجالية', 'name_en': 'Men Accessories'},
      {'name_ar': 'ساعة يدوية', 'name_en': 'Wrist Watch'},
      {'name_ar': 'ساعة جدارية', 'name_en': 'Wall Clock'},
      {'name_ar': 'مواد احتياطية للسيارات', 'name_en': 'Car Spare Parts'},
      {'name_ar': 'كماليات سيارة', 'name_en': 'Car Accessories'},
      {'name_ar': 'موبايل جديد', 'name_en': 'New Mobile'},
      {'name_ar': 'موبايل مستخدم', 'name_en': 'Used Mobile'},
      {'name_ar': 'ايباد جديد', 'name_en': 'New iPad'},
      {'name_ar': 'ايباد مستخدم', 'name_en': 'Used iPad'},
      {'name_ar': 'تابلت جديد', 'name_en': 'New Tablet'},
      {'name_ar': 'تابلت مستخدم', 'name_en': 'Used Tablet'},
      {'name_ar': 'لابتوب جديد', 'name_en': 'New Laptop'},
      {'name_ar': 'لابتوب مستخدم', 'name_en': 'Used Laptop'},
      {'name_ar': 'حاسوب جديد', 'name_en': 'New Computer'},
      {'name_ar': 'حاسوب مستخدم', 'name_en': 'Used Computer'},
      {'name_ar': 'اكس بوكس جديد', 'name_en': 'New Xbox'},
      {'name_ar': 'اكس بوكس مستخدم', 'name_en': 'Used Xbox'},
      {'name_ar': 'بليستيشن جديد', 'name_en': 'New PlayStation'},
      {'name_ar': 'بليستيشن مستخدم', 'name_en': 'Used PlayStation'},
    ];

    final now = DateTime.now().toIso8601String();

    for (var item in goodsList) {
      await db.insert(DbConstants.tableGoodsData, {
        'name_ar': item['name_ar'],
        'name_en': item['name_en'],
        DbConstants.columnCreatedAt: now,
        DbConstants.columnUpdatedAt: now,
      });
    }
  }

  // طرق عامة للتعامل مع قاعدة البيانات
  Future<List<Map<String, dynamic>>> getAll(String table,
      {String? orderBy}) async {
    final db = await database;
    return await db.query(table, orderBy: orderBy);
  }

  Future<Map<String, dynamic>?> getById(
      String table, String idColumn, dynamic id) async {
    final db = await database;
    final results = await db.query(
      table,
      where: '$idColumn = ?',
      whereArgs: [id],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert(table, data);
  }

  Future<int> update(String table, Map<String, dynamic> data, String idColumn,
      dynamic id) async {
    final db = await database;
    return await db.update(
      table,
      data,
      where: '$idColumn = ?',
      whereArgs: [id],
    );
  }

  Future<int> delete(String table, String idColumn, dynamic id) async {
    final db = await database;
    return await db.delete(table, where: '$idColumn = ?', whereArgs: [id]);
  }

  // طرق للتعامل مع المستخدمين
  Future<List<Map<String, dynamic>>> getUsers() async {
    final db = await database;
    final users = await db.query(DbConstants.tableUsers);

    // إضافة معلومات الفرع لكل مستخدم
    List<Map<String, dynamic>> usersWithBranchInfo = [];
    for (var user in users) {
      Map<String, dynamic> userWithBranch = Map.from(user);

      if (user['branch_id'] != null) {
        final branchData = await db.query(
          tableBranches,
          where: 'id = ?',
          whereArgs: [user['branch_id']],
          limit: 1,
        );

        if (branchData.isNotEmpty) {
          userWithBranch['branch_name'] = branchData.first['name'];
        }
      } else {
        userWithBranch['branch_name'] = 'غير محدد';
      }

      usersWithBranchInfo.add(userWithBranch);
    }

    return usersWithBranchInfo;
  }

  // الحصول على المستخدمين حسب الفرع
  Future<List<Map<String, dynamic>>> getUsersByBranch(int branchId) async {
    final db = await database;
    return await db.query(
      DbConstants.tableUsers,
      where: 'branch_id = ?',
      whereArgs: [branchId],
    );
  }

  Future<int> insertUser(Map<String, dynamic> user) async {
    return await insert(DbConstants.tableUsers, user);
  }

  Future<int> updateUser(Map<String, dynamic> user) async {
    return await update(DbConstants.tableUsers, user, DbConstants.columnId,
        user[DbConstants.columnId]);
  }

  // الحصول على مستخدم بواسطة المعرف
  Future<Map<String, dynamic>?> getUserById(int id) async {
    final db = await database;
    final results = await db.query(
      DbConstants.tableUsers,
      where: '${DbConstants.columnId} = ?',
      whereArgs: [id],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<int> deleteUser(int id) async {
    return await delete(DbConstants.tableUsers, DbConstants.columnId, id);
  }

  // التحقق من وجود مستخدم بواسطة اسم المستخدم وكلمة المرور
  Future<Map<String, dynamic>?> authenticateUser(
      String username, String password) async {
    final db = await database;

    final results = await db.query(
      DbConstants.tableUsers,
      where: 'name = ? AND password = ?',
      whereArgs: [username, password],
      limit: 1,
    );

    if (results.isEmpty) {
      // محاولة بالإيميل
      final emailResults = await db.query(
        DbConstants.tableUsers,
        where: 'email = ? AND password = ?',
        whereArgs: [username, password],
        limit: 1,
      );

      if (emailResults.isEmpty) {
        return null;
      } else {
        return emailResults.first;
      }
    }
    // إضافة معلومات الفرع للمستخدم
    Map<String, dynamic> userWithBranch = Map.from(results.first);

    if (userWithBranch['branch_id'] != null) {
      final branchData = await db.query(
        tableBranches,
        where: 'id = ?',
        whereArgs: [userWithBranch['branch_id']],
        limit: 1,
      );

      if (branchData.isNotEmpty) {
        userWithBranch['branch_name'] = branchData.first['name'];
      }
    }

    return userWithBranch;
  }

  // الحصول على آخر مستخدم تمت إضافته
  Future<Map<String, dynamic>?> getLastAddedUser() async {
    final db = await database;
    final results = await db.query(
      DbConstants.tableUsers,
      orderBy: '${DbConstants.columnId} DESC',
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  // طرق للتعامل مع الشحنات
  Future<List<Map<String, dynamic>>> getShipments() async {
    return await getAll(DbConstants.tableShipments);
  }

  Future<int> insertShipment(Map<String, dynamic> shipment) async {
    return await insert(DbConstants.tableShipments, shipment);
  }

  Future<int> updateShipment(Map<String, dynamic> shipment) async {
    return await update(DbConstants.tableShipments, shipment,
        DbConstants.columnId, shipment[DbConstants.columnId]);
  }

  Future<int> deleteShipment(int id) async {
    return await delete(DbConstants.tableShipments, DbConstants.columnId, id);
  }

  // طرق للتعامل مع بيانات الكود (تستخدم قاعدة بيانات الفرع أو الرئيسية حسب دور المستخدم)
  Future<List<Map<String, dynamic>>> getAllCodeData() async {
    // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
    if (_isAdminMode) {
      debugPrint(
          '**** getAllCodeData: وضع الأدمن - استخدام قاعدة البيانات الرئيسية');
      return await getAll(DbConstants.tableCodeData, orderBy: 'date DESC');
    }

    if (_currentBranch == null) {
      debugPrint(
          'تحذير: لم يتم تعيين الفرع الحالي، استخدام قاعدة البيانات الرئيسية');
      return await getAll(DbConstants.tableCodeData, orderBy: 'date DESC');
    }

    try {
      final db = await branchDatabase;
      final result = await db.query(
        DbConstants.tableCodeData,
        orderBy: 'date DESC',
      );
      debugPrint(
          'تم جلب ${result.length} سجل من قاعدة بيانات الفرع: $_currentBranch');
      return result;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // الحصول على بيانات سجل بواسطة الكود (من قاعدة بيانات الفرع)
  Future<Map<String, dynamic>?> getCodeDataByCode(String codeNo) async {
    try {
      debugPrint(
          'البحث عن الكود: $codeNo في قاعدة بيانات الفرع: $_currentBranch');
      final db = _currentBranch != null ? await branchDatabase : await database;
      var result = await db.query(
        DbConstants.tableCodeData,
        where: 'code_no = ?',
        whereArgs: [codeNo],
        limit: 1,
      );

      if (result.isNotEmpty) {
        // ?? ??? ????? debugPrint
        // طباعة سجل البيانات للتشخيص
        final data = result.first;
        // ?? ??? ????? debugPrint
        // ?? ??? ????? debugPrint}');

        // التحقق من وجود حقل flag_image
        if (data.containsKey('flag_image')) {
          bool hasFlagImage = data['flag_image'] != null &&
              data['flag_image'].toString().isNotEmpty;
          debugPrint(
              'حالة صورة العلم في البيانات المسترجعة: ${hasFlagImage ? 'موجودة' : 'غير موجودة'}');

          if (hasFlagImage) {
            // ?? ??? ????? debugPrint
          }
        } else {
          // ?? ??? ????? debugPrint
        }

        return data;
      } else {
        // ?? ??? ????? debugPrint

        // إذا لم نجد البيانات في قاعدة بيانات الفرع، نحاول البحث في قاعدة البيانات الرئيسية
        if (_currentBranch != null && !_isAdminMode) {
          // ?? ??? ????? debugPrint
          final mainDb = await database;
          result = await mainDb.query(
            DbConstants.tableCodeData,
            where: 'code_no = ?',
            whereArgs: [codeNo],
            limit: 1,
          );

          if (result.isNotEmpty) {
            debugPrint(
                'تم العثور على الكود: $codeNo في قاعدة البيانات الرئيسية');
            return result.first;
          }
        }

        // ?? ??? ????? debugPrint
        return null;
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
      return null;
    }
  }

  Future<int> insertCodeData(Map<String, dynamic> codeData) async {
    try {
      debugPrint('=== بدء insertCodeData ===');
      debugPrint('البيانات المستلمة: $codeData');
      debugPrint('وضع الأدمن: $_isAdminMode');
      debugPrint('الفرع الحالي: $_currentBranch');

      codeData[DbConstants.columnCreatedAt] = DateTime.now().toIso8601String();
      codeData[DbConstants.columnUpdatedAt] = DateTime.now().toIso8601String();

      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      if (_isAdminMode) {
        debugPrint(
            '**** insertCodeData: وضع الأدمن - حفظ في قاعدة البيانات الرئيسية');
        final result = await insert(DbConstants.tableCodeData, codeData);
        debugPrint('نتيجة الحفظ في قاعدة البيانات الرئيسية: $result');
        return result;
      }

      if (_currentBranch != null) {
        debugPrint('حفظ في قاعدة بيانات الفرع: $_currentBranch');
        final db = await branchDatabase;
        final result = await db.insert(DbConstants.tableCodeData, codeData);
        debugPrint('نتيجة الحفظ في قاعدة بيانات الفرع: $result');
        return result;
      } else {
        debugPrint(
            'تحذير: لم يتم تعيين الفرع الحالي، استخدام قاعدة البيانات الرئيسية');
        final result = await insert(DbConstants.tableCodeData, codeData);
        debugPrint('نتيجة الحفظ في قاعدة البيانات الرئيسية (احتياطي): $result');
        return result;
      }
    } catch (e) {
      debugPrint('خطأ في insertCodeData: $e');
      debugPrint('تفاصيل الخطأ: ${e.toString()}');
      return 0;
    }
  }

  Future<int> updateCodeData(Map<String, dynamic> codeData) async {
    try {
      debugPrint(
          'بدء عملية تحديث بيانات الكود في DatabaseHelper: ${codeData['code_no']}');
      // ?? ??? ????? debugPrint

      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      if (_isAdminMode) {
        debugPrint(
            '**** updateCodeData: وضع الأدمن - تحديث في قاعدة البيانات الرئيسية');
        codeData['updated_at'] = DateTime.now().toIso8601String();
        final db = await database;
        return await db.update(
          DbConstants.tableCodeData,
          codeData,
          where: 'code_no = ?',
          whereArgs: [codeData['code_no']],
        );
      }

      // نسخة جديدة من البيانات للتعديل
      Map<String, dynamic> normalizedData = Map<String, dynamic>.from(codeData);

      // التأكد من وجود code_no وأنه غير فارغ
      if (!normalizedData.containsKey('code_no') ||
          normalizedData['code_no'] == null ||
          normalizedData['code_no'].toString().isEmpty) {
        // ?? ??? ????? debugPrint
        return 0;
      }

      // معالجة سعر الصرف بشكل خاص
      if (normalizedData.containsKey('exchange_rate')) {
        var exchangeRate = normalizedData['exchange_rate'];
        debugPrint(
            'سعر الصرف الأصلي: $exchangeRate (${exchangeRate?.runtimeType})');

        try {
          double finalRate;

          if (exchangeRate == null) {
            finalRate = 1309.0;
          } else if (exchangeRate is double) {
            finalRate = exchangeRate;
          } else if (exchangeRate is int) {
            finalRate = exchangeRate.toDouble();
          } else if (exchangeRate is String) {
            // تنظيف النص من أي أحرف غير رقمية
            String cleanText = exchangeRate.trim().replaceAll(',', '.');
            finalRate = double.parse(cleanText);
          } else {
            // محاولة التحويل من أي نوع آخر
            finalRate = double.parse(exchangeRate.toString());
          }

          // التأكد من أن القيمة موجبة
          if (finalRate <= 0) {
            finalRate = 1309.0;
          }

          normalizedData['exchange_rate'] = finalRate;
          debugPrint(
              'سعر الصرف بعد المعالجة: ${normalizedData['exchange_rate']} (${normalizedData['exchange_rate'].runtimeType})');
        } catch (e) {
          // ?? ??? ????? debugPrint
          normalizedData['exchange_rate'] = 1309.0;
        }
      }

      // تحديث وقت التعديل
      normalizedData['updated_at'] = DateTime.now().toIso8601String();

      // استخدام قاعدة بيانات الفرع أو الرئيسية حسب الفرع الحالي
      final db = _currentBranch != null ? await branchDatabase : await database;
      // ?? ??? ????? debugPrint

      // التحقق من وجود السجل أولاً
      final exists = await db.query(
        DbConstants.tableCodeData,
        columns: ['code_no'],
        where: 'code_no = ?',
        whereArgs: [normalizedData['code_no']],
      );

      if (exists.isEmpty) {
        debugPrint(
            'السجل غير موجود في قاعدة البيانات: ${normalizedData['code_no']}');
        return 0;
      } else {
        // ?? ??? ????? debugPrint
      }

      // إزالة المفتاح الرئيسي من البيانات إذا كان موجودًا لتجنب أخطاء التحديث
      normalizedData.remove(DbConstants.columnId);

      // طباعة البيانات النهائية للتحديث
      // ?? ??? ????? debugPrint

      // فحص وجود صورة العلم في البيانات
      if (normalizedData.containsKey('flag_image')) {
        bool hasFlagImage = normalizedData['flag_image'] != null &&
            normalizedData['flag_image'].toString().isNotEmpty;
        debugPrint(
            'حالة صورة العلم في بيانات التحديث: ${hasFlagImage ? "موجودة" : "غير موجودة"}');
        if (hasFlagImage) {
          int flagLength = normalizedData['flag_image'].toString().length;
          // ?? ??? ????? debugPrint
          // عرض عينة من البيانات للتأكد من صحتها
          normalizedData['flag_image']
              .toString()
              .substring(0, flagLength > 50 ? 50 : flagLength);
          // ?? ??? ????? debugPrint
        }
      } else {
        // ?? ??? ????? debugPrint
      }

      // استخدام استعلام SQL مباشر للتحديث
      int result;

      // إذا كان التحديث يتضمن سعر الصرف، أو كان يحتوي على عدد كبير من الحقول، استخدم الطريقة العادية
      result = await db.update(
        DbConstants.tableCodeData,
        normalizedData,
        where: 'code_no = ?',
        whereArgs: [normalizedData['code_no']],
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // ?? ??? ????? debugPrint

      // التحقق من نجاح التحديث
      if (result > 0) {
        debugPrint(
            'تم تحديث البيانات بنجاح في قاعدة بيانات الفرع: $_currentBranch');

        // التحقق من البيانات بعد التحديث
        final updatedDataCheck = await db.query(
          DbConstants.tableCodeData,
          where: 'code_no = ?',
          whereArgs: [normalizedData['code_no']],
        );

        if (updatedDataCheck.isNotEmpty) {
          final updatedRecord = updatedDataCheck.first;
          // ?? ??? ????? debugPrint

          // التحقق من حالة صورة العلم بعد التحديث
          if (updatedRecord.containsKey('flag_image')) {
            bool flagImageUpdated = updatedRecord['flag_image'] != null &&
                updatedRecord['flag_image'].toString().isNotEmpty;

            if (flagImageUpdated) {
              // ?? ??? ????? debugPrint
            }
          }
        } else {
          // ?? ??? ????? debugPrint
        }

        // التحقق من البيانات بعد التحديث إذا كان يتضمن سعر الصرف
        if (normalizedData.containsKey('exchange_rate')) {
          final updatedData = await db.query(
            DbConstants.tableCodeData,
            columns: ['exchange_rate'],
            where: 'code_no = ?',
            whereArgs: [normalizedData['code_no']],
          );

          if (updatedData.isNotEmpty) {
            debugPrint(
                'سعر الصرف بعد التحديث: ${updatedData.first['exchange_rate']}');
          }
        }
      } else {
        // إذا كانت النتيجة 0، فقد تكون البيانات لم تتغير
        debugPrint(
            'لم يتم تحديث أي سجلات، قد تكون البيانات لم تتغير أو هناك مشكلة في الاستعلام');
      }

      return result;
    } catch (e) {
      // ?? ??? ????? debugPrint
      throw Exception('failed to update code data: $e');
    }
  }

  Future<int> deleteCodeData(String codeNo) async {
    try {
      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      if (_isAdminMode) {
        debugPrint(
            '**** deleteCodeData: وضع الأدمن - حذف من قاعدة البيانات الرئيسية');
        final db = await database;
        return await db.delete(
          DbConstants.tableCodeData,
          where: 'code_no = ?',
          whereArgs: [codeNo],
        );
      }

      final db = _currentBranch != null ? await branchDatabase : await database;
      // ?? ??? ????? debugPrint
      return await db.delete(
        DbConstants.tableCodeData,
        where: 'code_no = ?',
        whereArgs: [codeNo],
      );
    } catch (e) {
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  // البحث في بيانات الكود
  Future<List<Map<String, dynamic>>> searchCodeData(String query) async {
    try {
      final db = _currentBranch != null ? await branchDatabase : await database;
      // ?? ??? ????? debugPrint

      final searchFields = [
        'code_no',
        'truck_no',
        'sender_name',
        'sender_phone',
        'receiver_name',
        'receiver_phone'
      ];

      final whereClause =
          searchFields.map((field) => '$field LIKE ?').join(' OR ');
      final whereArgs = List.filled(searchFields.length, '%$query%');

      return await db.query(DbConstants.tableCodeData,
          where: whereClause, whereArgs: whereArgs, orderBy: 'date DESC');
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // البحث في بيانات الكود مع تحديد الحقول المطلوبة فقط
  Future<List<Map<String, dynamic>>> searchCodeDataWithFields(
      String query) async {
    try {
      final db = _currentBranch != null ? await branchDatabase : await database;
      // ?? ??? ????? debugPrint

      final selectedColumns = [
        'code_no',
        'truck_no',
        'date',
        'sender_name',
        'sender_phone',
        'receiver_name',
        'receiver_phone',
        'country',
        'city',
        'street_name_no',
        'postal_code',
        'city_name'
      ];

      final searchFields = [
        'code_no',
        'truck_no',
        'sender_name',
        'sender_phone',
        'receiver_name',
        'receiver_phone',
        'country',
        'city',
        'street_name_no',
        'postal_code',
        'city_name'
      ];

      final whereClause =
          searchFields.map((field) => '$field LIKE ?').join(' OR ');
      final whereArgs = List.filled(searchFields.length, '%$query%');

      return await db.query(DbConstants.tableCodeData,
          columns: selectedColumns,
          where: whereClause,
          whereArgs: whereArgs,
          orderBy: 'code_no DESC');
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // البحث في بيانات الكود باستخدام عمود محدد
  Future<List<Map<String, dynamic>>> searchCodeDataByColumn(
      String columnKey, String searchValue) async {
    try {
      final db = _currentBranch != null ? await branchDatabase : await database;
      // ?? ??? ????? debugPrint

      final selectedColumns = [
        'code_no',
        'truck_no',
        'date',
        'sender_name',
        'sender_phone',
        'receiver_name',
        'receiver_phone',
        'country',
        'city',
        'street_name_no',
        'postal_code',
        'city_name'
      ];

      return await db.query(DbConstants.tableCodeData,
          columns: selectedColumns,
          where: '$columnKey LIKE ?',
          whereArgs: ['%$searchValue%'],
          orderBy: 'code_no DESC');
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // الحصول على رقم الشاحنة الافتراضي
  Future<String> getDefaultTruckNo() async {
    try {
      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      Database db;
      if (_isAdminMode) {
        debugPrint(
            '**** getDefaultTruckNo: وضع الأدمن - قراءة من قاعدة البيانات الرئيسية');
        db = await database;
      } else {
        db = _currentBranch != null ? await branchDatabase : await database;
      }
      debugPrint(
          'الحصول على رقم الشاحنة الافتراضي من قاعدة بيانات الفرع: $_currentBranch');

      final result = await db.query(DbConstants.tableCodeData,
          columns: ['truck_no'],
          where: 'truck_no IS NOT NULL AND truck_no != ""',
          orderBy: '${DbConstants.columnId} DESC',
          limit: 1);

      return result.isNotEmpty && result.first['truck_no'] != null
          ? result.first['truck_no'].toString()
          : '1';
    } catch (e) {
      // ?? ??? ????? debugPrint
      return '1';
    }
  }

  // الحصول على أكبر رقم شاحنة في قاعدة بيانات الفرع
  Future<int> getMaxTruckNo() async {
    try {
      final db = _currentBranch != null ? await branchDatabase : await database;
      final result = await db.rawQuery('''
        SELECT MAX(CAST(truck_no AS INTEGER)) as max_truck_no
        FROM ${DbConstants.tableCodeData}
        WHERE truck_no IS NOT NULL AND truck_no != "" 
        AND truck_no GLOB '[0-9]*'
      ''');

      final maxTruckNo =
          result.isNotEmpty && result.first['max_truck_no'] != null
              ? result.first['max_truck_no'] as int
              : 0;

      // ?? ??? ????? debugPrint
      return maxTruckNo;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  // التحقق من وجود رقم شاحنة في قاعدة بيانات الفرع
  Future<bool> truckNoExists(String truckNo) async {
    try {
      final db = _currentBranch != null ? await branchDatabase : await database;
      final result = await db.query(DbConstants.tableCodeData,
          columns: ['truck_no'],
          where: 'truck_no = ?',
          whereArgs: [truckNo],
          limit: 1);

      final exists = result.isNotEmpty;
      debugPrint(
          'رقم الشاحنة $truckNo في الفرع $_currentBranch: ${exists ? "موجود" : "غير موجود"}');
      return exists;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return false;
    }
  }

  // الحصول على بيانات الكود مع تحديد الأعمدة المطلوبة فقط
  Future<List<Map<String, dynamic>>> getCodeDataWithSelectedColumns(
      List<String> columns) async {
    try {
      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      Database db;
      if (_isAdminMode) {
        debugPrint(
            '**** getCodeDataWithSelectedColumns: وضع الأدمن - قراءة من قاعدة البيانات الرئيسية');
        db = await database;
      } else {
        db = _currentBranch != null ? await branchDatabase : await database;
      }
      debugPrint(
          'الحصول على البيانات المحددة من قاعدة بيانات الفرع: $_currentBranch');

      if (!columns.contains(DbConstants.columnId)) {
        columns.add(DbConstants.columnId);
      }
      return await db.query(
        DbConstants.tableCodeData,
        columns: columns,
        orderBy: '${DbConstants.columnId} DESC',
      );
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // دالة لتحديث هيكل قاعدة البيانات يدويًا
  Future<bool> updateDatabaseSchema() async {
    try {
      final db = await database;

      // تحديث جداول العناوين
      await _createAddressTables(db);

      // إضافة أعمدة معلومات الوكيل وصورة العلم
      var tableInfo = await db.rawQuery("PRAGMA table_info($tableCodeData)");
      var columnNames = tableInfo.map((col) => col['name'] as String).toList();

      // إضافة الأعمدة التي لم تكن موجودة
      if (!columnNames.contains('flag_image')) {
        await db
            .execute('ALTER TABLE $tableCodeData ADD COLUMN flag_image BLOB');
        // ?? ??? ????? debugPrint
      }

      if (!columnNames.contains('agent_name')) {
        await db
            .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_name TEXT');
        // ?? ??? ????? debugPrint
      }

      if (!columnNames.contains('agent_phone1')) {
        await db
            .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_phone1 TEXT');
        // ?? ??? ????? debugPrint
      }

      if (!columnNames.contains('agent_phone2')) {
        await db
            .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_phone2 TEXT');
        // ?? ??? ????? debugPrint
      }

      if (!columnNames.contains('agent_address')) {
        await db.execute(
            'ALTER TABLE $tableCodeData ADD COLUMN agent_address TEXT');
        // ?? ??? ????? debugPrint
      }

      return true;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return false;
    }
  }

  // طرق للتعامل مع بيانات البضائع
  Future<List<Map<String, dynamic>>> getAllGoodsData() async {
    final db = await database;

    return await db.rawQuery('''
      SELECT g.*, COUNT(si.item_id) as usage_count 
      FROM ${DbConstants.tableGoodsData} g
      LEFT JOIN $tableSelectedItems si ON g.${DbConstants.columnId} = si.item_id
      GROUP BY g.${DbConstants.columnId}
      ORDER BY 
        usage_count DESC, 
        g.${DbConstants.columnCreatedAt} DESC,
        g.name_ar ASC
    ''');
  }

  Future<int> insertGoodsData(Map<String, dynamic> goodsData) async {
    goodsData[DbConstants.columnCreatedAt] = DateTime.now().toIso8601String();
    goodsData[DbConstants.columnUpdatedAt] = DateTime.now().toIso8601String();
    return await insert(DbConstants.tableGoodsData, goodsData);
  }

  Future<int> updateGoodsData(Map<String, dynamic> goodsData) async {
    goodsData[DbConstants.columnUpdatedAt] = DateTime.now().toIso8601String();
    return await update(DbConstants.tableGoodsData, goodsData,
        DbConstants.columnId, goodsData[DbConstants.columnId]);
  }

  Future<int> deleteGoodsData(int id) async {
    return await delete(DbConstants.tableGoodsData, DbConstants.columnId, id);
  }

  // حفظ العناصر المحددة لسجل معين
  Future<bool> saveSelectedItems(
      String codeNo, List<Map<String, dynamic>> items) async {
    try {
      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      Database db;
      if (_isAdminMode) {
        debugPrint(
            '**** saveSelectedItems: وضع الأدمن - حفظ في قاعدة البيانات الرئيسية');
        db = await database;
      } else {
        db = _currentBranch != null ? await branchDatabase : await database;
      }
      // ?? ??? ????? debugPrint

      // بدء المعاملة
      await db.transaction((txn) async {
        // حذف العناصر المحددة السابقة لهذا السجل
        await txn.delete(
          tableSelectedItems,
          where: 'code_no = ?',
          whereArgs: [codeNo],
        );

        // إضافة العناصر المحددة الجديدة
        for (var item in items) {
          await txn.insert(tableSelectedItems, {
            'code_no': codeNo,
            'item_id': item['id'],
            'quantity': item['quantity'],
            'weight': item['weight'],
          });
        }
      });

      return true;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return false;
    }
  }

  // الحصول على العناصر المحددة لسجل معين
  Future<List<Map<String, dynamic>>> getSelectedItems(String codeNo) async {
    try {
      // إذا كان المستخدم أدمن، استخدم قاعدة البيانات الرئيسية دائماً
      Database db;
      if (_isAdminMode) {
        debugPrint(
            '**** getSelectedItems: وضع الأدمن - قراءة من قاعدة البيانات الرئيسية');
        db = await database;
      } else {
        db = _currentBranch != null ? await branchDatabase : await database;
      }
      debugPrint(
          'الحصول على العناصر المحددة من قاعدة بيانات الفرع: $_currentBranch');

      // استعلام للحصول على العناصر المحددة مع بياناتها الكاملة
      // ملاحظة: بيانات البضائع في قاعدة البيانات الرئيسية، لذا سنحتاج استعلام مختلف
      final selectedItems = await db.query(
        tableSelectedItems,
        where: 'code_no = ?',
        whereArgs: [codeNo],
      );

      // إذا كانت هناك عناصر محددة، نحتاج لجلب بيانات البضائع من قاعدة البيانات الرئيسية
      if (selectedItems.isNotEmpty) {
        final mainDb = await database;
        List<Map<String, dynamic>> fullResults = [];

        for (var item in selectedItems) {
          final goodsData = await mainDb.query(
            DbConstants.tableGoodsData,
            where: '${DbConstants.columnId} = ?',
            whereArgs: [item['item_id']],
            limit: 1,
          );

          if (goodsData.isNotEmpty) {
            Map<String, dynamic> fullItem = Map.from(item);
            fullItem['name_ar'] = goodsData.first['name_ar'];
            fullItem['name_en'] = goodsData.first['name_en'];
            fullResults.add(fullItem);
          } else {
            fullResults.add(item);
          }
        }

        return fullResults;
      }

      return selectedItems;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return [];
    }
  }

  // وظائف إدارة الفروع
  Future<List<Map<String, dynamic>>> getBranches() async {
    final db = await database;
    return await db.query(tableBranches);
  }

  // الحصول على معلومات فرع بناء على اسمه
  Future<Map<String, dynamic>?> getBranchByName(String branchName) async {
    final db = await database;
    final results = await db.query(
      tableBranches,
      where: 'name = ?',
      whereArgs: [branchName],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<int> insertBranch(Map<String, dynamic> branch) async {
    final db = await database;
    return await db.insert(tableBranches, branch);
  }

  Future<int> updateBranch(Map<String, dynamic> branch) async {
    try {
      // ?? ??? ????? debugPrint

      // تنظيف قواعد البيانات المكررة أولاً
      await cleanupLockedAndDuplicateDatabases();

      // منع التداخل في العمليات
      if (_isUpdatingBranch) {
        debugPrint(
            '**** updateBranch: عملية تحديث أخرى قيد التشغيل، انتظار...');
        while (_isUpdatingBranch) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      _isUpdatingBranch = true;
      // ?? ??? ????? debugPrint

      final db = await database;

      // الحصول على اسم الفرع القديم قبل التحديث
      final oldBranchData = await db.query(
        tableBranches,
        where: 'id = ?',
        whereArgs: [branch['id']],
        limit: 1,
      );

      String? oldBranchName;
      if (oldBranchData.isNotEmpty) {
        oldBranchName = oldBranchData.first['name'] as String?;
      }

      debugPrint(
          '**** updateBranch: الاسم القديم: $oldBranchName، الاسم الجديد: ${branch['name']}');

      // إذا تم تغيير اسم الفرع، قم بإغلاق قاعدة البيانات أولاً
      if (oldBranchName != null &&
          branch.containsKey('name') &&
          oldBranchName != branch['name']) {
        // ?? ??? ????? debugPrint

        // إغلاق قاعدة بيانات الفرع إذا كانت مفتوحة
        if (_branchDatabase != null && _branchDatabase!.isOpen) {
          // ?? ??? ????? debugPrint
          await _branchDatabase!.close();
          _branchDatabase = null;
        }

        // تحديث بيانات الفرع في قاعدة البيانات الرئيسية أولاً
        final result = await db.update(
          tableBranches,
          branch,
          where: 'id = ?',
          whereArgs: [branch['id']],
        );

        // إعادة تسمية قاعدة البيانات
        final renamed =
            await renameBranchDatabase(oldBranchName, branch['name']);
        if (!renamed) {
          debugPrint(
              '**** updateBranch: تحذير: فشل في إعادة تسمية قاعدة بيانات الفرع');
        }

        // تحديث الفرع الحالي إذا كان هو نفس الفرع المُحدث
        if (_currentBranch == oldBranchName) {
          _currentBranch = branch['name'];
          debugPrint(
              '**** updateBranch: تم تحديث الفرع الحالي إلى: ${branch['name']}');
        }

        // فرض إعادة تحميل قاعدة بيانات الفرع لضمان استخدام الاسم الجديد
        await reloadBranchDatabase();
        // ?? ??? ????? debugPrint

        // تأخير لضمان اكتمال العمليات
        await Future.delayed(const Duration(milliseconds: 500));
        // ?? ??? ????? debugPrint

        _isUpdatingBranch = false;
        // ?? ??? ????? debugPrint

        return result;
      } else {
        // تحديث عادي بدون تغيير الاسم
        // ?? ??? ????? debugPrint
        final result = await db.update(
          tableBranches,
          branch,
          where: 'id = ?',
          whereArgs: [branch['id']],
        );

        _isUpdatingBranch = false;
        // ?? ??? ????? debugPrint

        return result;
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
      _isUpdatingBranch = false;
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  Future<int> deleteBranch(int id) async {
    try {
      final db = await database;

      // الحصول على اسم الفرع قبل الحذف
      final branchData = await db.query(
        tableBranches,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      String? branchName;
      if (branchData.isNotEmpty) {
        branchName = branchData.first['name'] as String?;
      }

      // حذف الفرع من قاعدة البيانات الرئيسية
      final result = await db.delete(
        tableBranches,
        where: 'id = ?',
        whereArgs: [id],
      );

      // حذف قاعدة بيانات الفرع إذا كان موجوداً
      if (branchName != null) {
        // ?? ??? ????? debugPrint
        final deleted = await deleteBranchDatabase(branchName);
        if (!deleted) {
          // ?? ??? ????? debugPrint
        }
      }

      return result;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  // حذف قاعدة بيانات الفرع عند حذف الفرع
  Future<bool> deleteBranchDatabase(String branchName) async {
    try {
      Directory documentsDirectory = await _getDatabaseDirectory();

      // مسار قاعدة بيانات الفرع
      String dbName =
          'euknet_${branchName.toLowerCase().replaceAll(' ', '_')}.db';
      String dbPath = join(documentsDirectory.path, dbName);

      // ?? ??? ????? debugPrint

      // التحقق من وجود قاعدة البيانات
      File dbFile = File(dbPath);
      if (!await dbFile.exists()) {
        // ?? ??? ????? debugPrint
        return true; // تعتبر نجحاً لأن الهدف تحقق (عدم وجود الملف)
      }

      // إغلاق قاعدة بيانات الفرع إذا كانت مفتوحة
      if (_branchDatabase != null &&
          _branchDatabase!.isOpen &&
          _currentBranch == branchName) {
        await _branchDatabase!.close();
        _branchDatabase = null;
        _currentBranch = null;
      }

      // حذف ملف قاعدة البيانات
      await dbFile.delete();
      // ?? ??? ????? debugPrint

      return true;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return false;
    }
  }

  // التأكد من وجود جدول آخر تسجيل دخول
  Future<void> _ensureLastLoginTableExists() async {
    final db = await database;

    try {
      // تحقق من وجود الجدول
      final tableExists = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='last_login_info'");

      if (tableExists.isEmpty) {
        // ?? ??? ????? debugPrint
        await db.execute('''
          CREATE TABLE last_login_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            branch_id INTEGER,
            login_time TEXT NOT NULL
          )
        ''');
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  // حفظ معلومات آخر مستخدم قام بتسجيل الدخول
  Future<void> saveLastLoginUser(String username, int? branchId) async {
    try {
      // التأكد من وجود الجدول أولاً
      await _ensureLastLoginTableExists();

      final db = await database;

      // حذف البيانات السابقة
      await db.delete('last_login_info');

      // إضافة البيانات الجديدة
      await db.insert('last_login_info', {
        'username': username,
        'branch_id': branchId,
        'login_time': DateTime.now().toIso8601String(),
      });

      // ?? ??? ????? debugPrint
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  // الحصول على معلومات آخر مستخدم قام بتسجيل الدخول
  Future<Map<String, dynamic>?> getLastLoginUser() async {
    try {
      // التأكد من وجود الجدول أولاً
      await _ensureLastLoginTableExists();

      final db = await database;

      final results = await db.query('last_login_info',
          orderBy: 'login_time DESC', limit: 1);

      if (results.isNotEmpty) {
        final lastLogin = Map<String, dynamic>.from(results.first);

        // ?? ??? ????? debugPrint

        // إذا كان هناك معرف فرع، قم بجلب اسم الفرع
        if (lastLogin['branch_id'] != null) {
          final branchResults = await db.query(
            tableBranches,
            where: 'id = ?',
            whereArgs: [lastLogin['branch_id']],
            limit: 1,
          );

          if (branchResults.isNotEmpty) {
            lastLogin['branch_name'] = branchResults.first['name'];
            // ?? ??? ????? debugPrint
          }
        }

        return lastLogin;
      }

      // ?? ??? ????? debugPrint
      return null;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return null;
    }
  }

  // دالة للحصول على قاعدة بيانات جديدة في وضع الكتابة خاصة لعملية التحديث
  Future<Database> getWritableDatabase() async {
    await initializeDatabaseForPlatform();
    // أغلق قاعدة البيانات الحالية إذا كانت مفتوحة
    if (_database != null && _database!.isOpen) {
      await _database!.close();
      _database = null;
    }

    Directory documentsDirectory = await _getDatabaseDirectory();
    String path = join(documentsDirectory.path, _dbName);

    // محاولة تغيير صلاحيات الملف
    if (!kIsWeb &&
        (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
      try {
        await Process.run('chmod', ['666', path]);
        // ?? ??? ????? debugPrint
      } catch (e) {
        // ?? ??? ????? debugPrint
      }
    }

    // تهيئة قاعدة البيانات مع الخيارات الصريحة للكتابة
    final options = OpenDatabaseOptions(
      version: _dbVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      readOnly: false,
      singleInstance: false,
    );

    try {
      // ?? ??? ????? debugPrint
      return await databaseFactoryFfi.openDatabase(path, options: options);
    } catch (e) {
      // ?? ??? ????? debugPrint
      throw Exception('لا يمكن فتح قاعدة البيانات في وضع الكتابة: $e');
    }
  }

  // دالة للتأكد من وجود أعمدة معلومات الوكيل
  Future<void> ensureAgentColumnsExist() async {
    Database db = await database;

    // الحصول على معلومات الأعمدة الحالية في الجدول
    var tableInfo = await db.rawQuery('PRAGMA table_info($tableCodeData)');
    List<String> columnNames =
        tableInfo.map((col) => col['name'] as String).toList();

    // التحقق من وجود أعمدة معلومات الوكيل
    bool needsUpdate = false;

    if (!columnNames.contains('agent_name')) {
      await db.execute('ALTER TABLE $tableCodeData ADD COLUMN agent_name TEXT');
      // ?? ??? ????? debugPrint
      needsUpdate = true;
    }

    if (!columnNames.contains('agent_phone1')) {
      await db
          .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_phone1 TEXT');
      // ?? ??? ????? debugPrint
      needsUpdate = true;
    }

    if (!columnNames.contains('agent_phone2')) {
      await db
          .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_phone2 TEXT');
      // ?? ??? ????? debugPrint
      needsUpdate = true;
    }

    if (!columnNames.contains('agent_address')) {
      await db
          .execute('ALTER TABLE $tableCodeData ADD COLUMN agent_address TEXT');
      // ?? ??? ????? debugPrint
      needsUpdate = true;
    }

    if (needsUpdate) {
      // ?? ??? ????? debugPrint
    } else {
      // ?? ??? ????? debugPrint
    }
  }

  // تحديث معلومات الوكيل بطريقة مباشرة (تحسين الدالة)
  Future<Map<String, dynamic>> updateAgentInfoDirect(
      String codeNo, Map<String, dynamic> agentData) async {
    try {
      // التأكد من وجود أعمدة معلومات الوكيل
      await ensureAgentColumnsExist();

      // محاولة الحصول على اتصال جديد بقاعدة البيانات في وضع الكتابة
      Database db;
      try {
        // محاولة استخدام قاعدة البيانات الحالية
        db = await getWritableDatabase();
        // ?? ??? ????? debugPrint
      } catch (e) {
        // ?? ??? ????? debugPrint
        // محاولة فتح قاعدة البيانات بطريقة بديلة
        try {
          // ?? ??? ????? debugPrint
          db = await database;
        } catch (innerE) {
          // ?? ??? ????? debugPrint
          throw Exception('لا يمكن الوصول إلى قاعدة البيانات للكتابة');
        }
      }

      // طباعة محتويات البيانات التي سيتم تحديثها
      // ?? ??? ????? debugPrint
      // ?? ??? ????? debugPrint

      // تحديث تاريخ التحديث
      agentData['updated_at'] = DateTime.now().toIso8601String();

      // استخدام استعلام التحديث المباشر
      final result = await db.rawUpdate('''
        UPDATE $tableCodeData 
        SET agent_name = ?, agent_phone1 = ?, agent_phone2 = ?, agent_address = ?, updated_at = ? 
        WHERE code_no = ?
        ''', [
        agentData['agent_name'],
        agentData['agent_phone1'],
        agentData['agent_phone2'],
        agentData['agent_address'],
        agentData['updated_at'],
        codeNo
      ]);

      // ?? ??? ????? debugPrint

      // إذا لم يتم أي تحديث، قد يكون السبب هو أن السجل غير موجود
      // سنقوم بالتحقق من وجود السجل
      if (result == 0) {
        final checkResult = await db.query(
          tableCodeData,
          where: 'code_no = ?',
          whereArgs: [codeNo],
        );

        // ?? ??? ????? debugPrint

        if (checkResult.isNotEmpty) {
          // السجل موجود ولكن لم يتم تحديثه، قد يكون بسبب تشابه البيانات
          // أو بسبب مشكلة في الكتابة، سنحاول تحديثه مرة أخرى بطريقة أخرى
          try {
            final updateResult = await db.update(
              tableCodeData,
              {
                'agent_name': agentData['agent_name'],
                'agent_phone1': agentData['agent_phone1'],
                'agent_phone2': agentData['agent_phone2'],
                'agent_address': agentData['agent_address'],
                'updated_at': agentData['updated_at'],
              },
              where: 'code_no = ?',
              whereArgs: [codeNo],
            );

            // ?? ??? ????? debugPrint

            return {
              'success': updateResult > 0,
              'message': updateResult > 0
                  ? 'تم تحديث معلومات الوكيل بنجاح (طريقة بديلة)'
                  : 'لم يتم تحديث أي بيانات (طريقة بديلة)',
              'updates': updateResult
            };
          } catch (updateError) {
            // ?? ??? ????? debugPrint
          }
        }
      }

      // تسجيل حالة التحديث
      if (result > 0) {
        // التحقق من البيانات بعد التحديث
        final updatedData = await db.query(
          tableCodeData,
          columns: [
            'agent_name',
            'agent_phone1',
            'agent_phone2',
            'agent_address'
          ],
          where: 'code_no = ?',
          whereArgs: [codeNo],
        );

        // ?? ??? ????? debugPrint

        return {
          'success': true,
          'message': 'تم تحديث معلومات الوكيل بنجاح',
          'updates': result,
          'updated_data': updatedData.isNotEmpty ? updatedData.first : null
        };
      } else {
        return {
          'success': false,
          'message': 'لم يتم العثور على سجل لتحديثه',
          'updates': 0
        };
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
      return {
        'success': false,
        'message': 'خطأ في تحديث معلومات الوكيل: $e',
        'error': e.toString()
      };
    }
  }

  // دالة منفصلة لتحديث معلومات الوكيل فقط
  Future<int> updateAgentInfo(String codeNo, String agentName,
      String agentPhone1, String agentPhone2, String agentAddress) async {
    try {
      // استخدام الدالة الجديدة للتحديث
      Map<String, dynamic> agentData = {
        'agent_name': agentName,
        'agent_phone1': agentPhone1,
        'agent_phone2': agentPhone2,
        'agent_address': agentAddress
      };

      Map<String, dynamic> result =
          await updateAgentInfoDirect(codeNo, agentData);

      if (result['success'] == true) {
        return result['updates'];
      } else {
        // ?? ??? ????? debugPrint
        return 0;
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
      return 0;
    }
  }

  // دالة للتحقق من حالة قاعدة البيانات (للتشخيص)
  Future<Map<String, dynamic>> checkDatabaseStatus() async {
    Map<String, dynamic> status = {};
    try {
      // الحصول على مسار قاعدة البيانات
      Directory documentsDirectory = await _getDatabaseDirectory();
      String path = join(documentsDirectory.path, _dbName);

      // التحقق من وجود الملف
      File dbFile = File(path);
      bool fileExists = await dbFile.exists();
      status['fileExists'] = fileExists;

      if (fileExists) {
        // التحقق من حجم الملف
        int fileSize = await dbFile.length();
        status['fileSize'] = fileSize;

        // التحقق من صلاحيات الملف
        try {
          bool writable = true;
          try {
            await dbFile.writeAsString('', mode: FileMode.append);
          } catch (e) {
            writable = false;
          }
          status['writable'] = writable;
        } catch (e) {
          status['writable'] = false;
          status['permissionError'] = e.toString();
        }
      }

      // التحقق من حالة الاتصال
      status['databaseOpen'] = _database != null && _database!.isOpen;

      return status;
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  // حفظ قاعدة البيانات الحالية إلى مجلد الأصول
  Future<bool> backupDatabaseToAssets() async {
    try {
      // الحصول على مسار قاعدة البيانات الحالية
      Directory documentsDirectory = await _getDatabaseDirectory();
      String currentDbPath = join(documentsDirectory.path, _dbName);

      // التأكد من وجود قاعدة البيانات
      File dbFile = File(currentDbPath);
      if (!await dbFile.exists()) {
        // ?? ??? ????? debugPrint
        return false;
      }

      // التأكد من أن قاعدة البيانات غير مفتوحة
      if (_database != null && _database!.isOpen) {
        await _database!.close();
        _database = null;
      }

      // نسخ قاعدة البيانات إلى مجلد الأصول
      final targetPath =
          join(Directory.current.path, 'assets', 'database', _dbName);
      await dbFile.copy(targetPath);
      // ?? ??? ????? debugPrint
      return true;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return false;
    }
  }

  // إعادة تسمية قاعدة بيانات الفرع مع الإغلاق القسري
  Future<bool> renameBranchDatabase(String oldName, String newName) async {
    try {
      debugPrint(
          '**** renameBranchDatabase: إعادة تسمية من $oldName إلى $newName');

      Directory documentsDirectory = await _getDatabaseDirectory();

      // الإغلاق القسري لجميع قواعد البيانات
      debugPrint(
          '**** renameBranchDatabase: بدء الإغلاق القسري لجميع قواعد البيانات');

      // إغلاق قاعدة بيانات الفرع الحالية
      if (_branchDatabase != null && _branchDatabase!.isOpen) {
        debugPrint(
            '**** renameBranchDatabase: إغلاق قاعدة بيانات الفرع المفتوحة');
        await _branchDatabase!.close();
        _branchDatabase = null;
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // البحث عن الملف القديم
      String? existingDbPath =
          await findExistingBranchDatabaseImproved(oldName);
      if (existingDbPath == null) {
        debugPrint(
            '**** renameBranchDatabase: لم يتم العثور على قاعدة بيانات بالاسم القديم');
        return false;
      }

      String newDbName =
          'euknet_${newName.toLowerCase().replaceAll(' ', '_')}.db';
      String newPath = join(documentsDirectory.path, newDbName);

      File oldFile = File(existingDbPath);
      File newFile = File(newPath);

      debugPrint(
          '**** renameBranchDatabase: إعادة تسمية ${basename(existingDbPath)} إلى ${basename(newPath)}');

      // محاولة إغلاق أي اتصالات مفتوحة للملف القديم
      try {
        debugPrint(
            '**** renameBranchDatabase: محاولة إغلاق أي اتصالات للملف القديم');

        // فرض إعادة تعيين مصنع قاعدة البيانات
        databaseFactory = databaseFactoryFfi;
        await Future.delayed(const Duration(milliseconds: 300));
      } catch (e) {
        // ?? ??? ????? debugPrint
      }

      // حذف الملف الجديد إذا كان موجوداً
      if (await newFile.exists()) {
        // ?? ??? ????? debugPrint
        await newFile.delete();
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // محاولة إعادة التسمية مع المحاولات المتعددة
      int attempts = 0;
      while (attempts < 5) {
        try {
          await oldFile.rename(newPath);
          debugPrint(
              '**** renameBranchDatabase: تم إعادة تسمية قاعدة البيانات بنجاح');

          // التحقق من نجاح العملية
          if (await File(newPath).exists()) {
            debugPrint(
                '**** renameBranchDatabase: تم التأكد من وجود الملف الجديد');
            return true;
          }
        } catch (e) {
          attempts++;
          // ?? ??? ????? debugPrint

          if (attempts < 5) {
            // انتظار أطول مع كل محاولة
            await Future.delayed(Duration(milliseconds: 500 * attempts));

            // محاولة إجبار إغلاق العمليات
            if (attempts == 3) {
              debugPrint(
                  '**** renameBranchDatabase: محاولة نسخ بدلاً من إعادة التسمية');
              try {
                await oldFile.copy(newPath);
                await oldFile.delete();
                debugPrint(
                    '**** renameBranchDatabase: تم نسخ وحذف الملف القديم بنجاح');
                return true;
              } catch (copyError) {
                debugPrint(
                    '**** renameBranchDatabase: فشل في النسخ أيضاً: $copyError');
              }
            }
          }
        }
      }

      debugPrint(
          '**** renameBranchDatabase: فشل في إعادة تسمية قاعدة البيانات بعد $attempts محاولات');
      return false;
    } catch (e) {
      // ?? ??? ????? debugPrint
      return false;
    }
  }

  // فرض إعادة تحميل قاعدة بيانات الفرع
  Future<void> reloadBranchDatabase() async {
    if (_branchDatabase != null && _branchDatabase!.isOpen) {
      // ?? ??? ????? debugPrint
      await _branchDatabase!.close();
      _branchDatabase = null;
    }
    debugPrint(
        '**** reloadBranchDatabase: سيتم إعادة تحميل قاعدة البيانات عند الطلب التالي');
  }

  // تنظيف قواعد البيانات المكررة أو غير المستخدمة
  Future<void> cleanupDuplicateDatabases() async {
    try {
      debugPrint(
          '**** cleanupDuplicateDatabases: بدء تنظيف قواعد البيانات المكررة');

      Directory documentsDirectory = await _getDatabaseDirectory();
      List<FileSystemEntity> files = documentsDirectory.listSync();

      // الحصول على قائمة الفروع الموجودة في قاعدة البيانات الرئيسية
      final db = await database;
      final branchesData = await db.query(tableBranches);
      Set<String> validDatabases = {};

      for (var branch in branchesData) {
        String branchName = branch['name'] as String;
        String expectedDbName =
            'euknet_${branchName.toLowerCase().replaceAll(' ', '_')}.db';
        validDatabases.add(expectedDbName);
        debugPrint(
            '**** cleanupDuplicateDatabases: قاعدة بيانات صالحة: $expectedDbName');
      }

      // إضافة قاعدة البيانات الرئيسية
      validDatabases.add(_dbName);

      // البحث عن ملفات قواعد البيانات
      List<File> databaseFiles = [];
      for (var file in files) {
        if (file is File && file.path.endsWith('.db')) {
          databaseFiles.add(file);
        }
      }

      debugPrint(
          '**** cleanupDuplicateDatabases: عدد ملفات قواعد البيانات الموجودة: ${databaseFiles.length}');

      // فحص كل ملف قاعدة بيانات
      for (File dbFile in databaseFiles) {
        String fileName = basename(dbFile.path);
        // ?? ??? ????? debugPrint

        if (!validDatabases.contains(fileName)) {
          // ?? ??? ????? debugPrint

          // التحقق من أن الملف فارغ أو لا يحتوي على بيانات مهمة
          try {
            final fileSize = await dbFile.length();
            if (fileSize < 10240) {
              // أقل من 10KB يعتبر فارغ تقريباً
              debugPrint(
                  '**** cleanupDuplicateDatabases: حذف ملف فارغ: $fileName (حجم: $fileSize bytes)');
              await dbFile.delete();
            } else {
              debugPrint(
                  '**** cleanupDuplicateDatabases: الملف كبير، لم يتم حذفه: $fileName (حجم: $fileSize bytes)');
            }
          } catch (e) {
            debugPrint(
                '**** cleanupDuplicateDatabases: خطأ في فحص/حذف الملف $fileName: $e');
          }
        }
      }

      // ?? ??? ????? debugPrint
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  // تنظيف قواعد البيانات المُقفلة والمكررة
  Future<void> cleanupLockedAndDuplicateDatabases() async {
    try {
      debugPrint(
          '**** cleanupLockedAndDuplicateDatabases: بدء تنظيف قواعد البيانات المُقفلة');

      Directory documentsDirectory = await _getDatabaseDirectory();
      List<FileSystemEntity> files = documentsDirectory.listSync();

      // الحصول على قائمة الفروع الموجودة في قاعدة البيانات الرئيسية
      final db = await database;
      final branchesData = await db.query(tableBranches);

      // خريطة تربط كل فرع بقاعدة البيانات الأنسب له
      Map<String, String> branchToDatabase = {};

      // البحث عن ملفات قواعد البيانات
      List<File> databaseFiles = [];
      for (var file in files) {
        if (file is File &&
            file.path.endsWith('.db') &&
            !file.path.endsWith(_dbName)) {
          // استثني الملف الرئيسي
          databaseFiles.add(file);
        }
      }

      debugPrint(
          '**** cleanupLockedAndDuplicateDatabases: وُجد ${databaseFiles.length} ملف قاعدة بيانات');

      // فحص كل فرع والعثور على أفضل قاعدة بيانات له
      for (var branch in branchesData) {
        String branchName = branch['name'] as String;
        String expectedDbName =
            'euknet_${branchName.toLowerCase().replaceAll(' ', '_')}.db';
        debugPrint(
            '**** cleanupLockedAndDuplicateDatabases: فحص الفرع: $branchName');

        List<File> candidateFiles = [];

        // البحث عن الملفات المحتملة لهذا الفرع
        for (File dbFile in databaseFiles) {
          String fileName = basename(dbFile.path);
          String branchPattern = branchName.toLowerCase().replaceAll(' ', '_');

          if (fileName.contains(branchPattern) || fileName == expectedDbName) {
            candidateFiles.add(dbFile);
          }
        }

        debugPrint(
            '**** cleanupLockedAndDuplicateDatabases: وُجد ${candidateFiles.length} ملف محتمل للفرع $branchName');

        if (candidateFiles.isNotEmpty) {
          // ترتيب الملفات حسب الأولوية
          candidateFiles.sort((a, b) {
            String nameA = basename(a.path);
            String nameB = basename(b.path);

            // الأولوية للاسم الصحيح
            if (nameA == expectedDbName) return -1;
            if (nameB == expectedDbName) return 1;

            // ثم حسب الحجم (الأكبر أولاً)
            try {
              int sizeA = a.lengthSync();
              int sizeB = b.lengthSync();
              return sizeB.compareTo(sizeA);
            } catch (e) {
              return 0;
            }
          });

          // اختيار أفضل ملف
          File bestFile = candidateFiles.first;
          branchToDatabase[branchName] = bestFile.path;

          debugPrint(
              '**** cleanupLockedAndDuplicateDatabases: أفضل ملف للفرع $branchName: ${basename(bestFile.path)}');

          // حذف الملفات الأخرى المكررة
          for (int i = 1; i < candidateFiles.length; i++) {
            File duplicateFile = candidateFiles[i];
            try {
              int fileSize = await duplicateFile.length();
              String fileName = basename(duplicateFile.path);

              debugPrint(
                  '**** cleanupLockedAndDuplicateDatabases: محاولة حذف ملف مكرر: $fileName ($fileSize bytes)');

              // محاولة حذف الملف إذا لم يكن مُقفلاً
              await duplicateFile.delete();
              debugPrint(
                  '**** cleanupLockedAndDuplicateDatabases: تم حذف $fileName بنجاح');
            } catch (e) {
              debugPrint(
                  '**** cleanupLockedAndDuplicateDatabases: لا يمكن حذف ${basename(duplicateFile.path)}: $e');

              // إذا كان الملف مُقفلاً، جرب تأجيل الحذف
              if (e.toString().contains('being used') ||
                  e.toString().contains('locked')) {
                debugPrint(
                    '**** cleanupLockedAndDuplicateDatabases: الملف مُقفل، سيتم تجاهله مؤقتاً');
              }
            }
          }
        }
      }

      // طباعة النتيجة النهائية
      debugPrint(
          '**** cleanupLockedAndDuplicateDatabases: تخطيط قواعد البيانات النهائي:');
      branchToDatabase.forEach((branch, dbPath) {
        // ?? ??? ????? debugPrint}');
      });

      // ?? ??? ????? debugPrint
    } catch (e) {
      debugPrint(
          '**** cleanupLockedAndDuplicateDatabases: خطأ عام في التنظيف: $e');
    }
  }

  // إضافة عمود تعطيل الدول في جدول بيانات الكود
  Future<void> _addCountryEnableColumn(Database db) async {
    try {
      var tableInfo = await db.rawQuery("PRAGMA table_info($tableCodeData)");
      var columnNames = tableInfo.map((col) => col['name'] as String).toList();

      if (!columnNames.contains('is_enabled')) {
        await db.execute(
            'ALTER TABLE $tableCodeData ADD COLUMN is_enabled INTEGER DEFAULT 1');
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
    }
  }

  // تعطيل جميع سجلات دولة معينة
  Future<int> disableCountryData(String countryName) async {
    try {
      debugPrint('=== بدء تعطيل الدولة: $countryName ===');

      final db = _isAdminMode
          ? await database
          : (_currentBranch != null ? await branchDatabase : await database);

      final result = await db.update(
        DbConstants.tableCodeData,
        {'is_enabled': 0},
        where: 'country = ?',
        whereArgs: [countryName],
      );

      debugPrint('✅ تم تعطيل $result سجل للدولة: $countryName');
      return result;
    } catch (e) {
      debugPrint('خطأ في تعطيل الدولة $countryName: $e');
      return 0;
    }
  }

  // تفعيل جميع سجلات دولة معينة
  Future<int> enableCountryData(String countryName) async {
    try {
      debugPrint('=== بدء تفعيل الدولة: $countryName ===');

      final db = _isAdminMode
          ? await database
          : (_currentBranch != null ? await branchDatabase : await database);

      final result = await db.update(
        DbConstants.tableCodeData,
        {'is_enabled': 1},
        where: 'country = ?',
        whereArgs: [countryName],
      );

      debugPrint('✅ تم تفعيل $result سجل للدولة: $countryName');
      return result;
    } catch (e) {
      debugPrint('خطأ في تفعيل الدولة $countryName: $e');
      return 0;
    }
  }

  // التحقق من حالة تفعيل الدولة
  Future<bool> isCountryEnabled(String countryName) async {
    try {
      final db = _isAdminMode
          ? await database
          : (_currentBranch != null ? await branchDatabase : await database);

      final result = await db.query(
        DbConstants.tableCodeData,
        columns: ['is_enabled'],
        where: 'country = ?',
        whereArgs: [countryName],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return (result.first['is_enabled'] as int) == 1;
      }
      return true; // افتراضياً مفعلة إذا لم توجد
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة الدولة $countryName: $e');
      return true;
    }
  }

  // الحصول على قاعدة البيانات الرئيسية دائماً (للأدمن)
  Future<Database> get mainDatabase async {
    _database ??= await _initDatabase();
    return _database!;
  }
}
