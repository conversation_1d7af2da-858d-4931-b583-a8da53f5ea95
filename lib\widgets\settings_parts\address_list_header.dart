import 'package:flutter/material.dart';

class AddressListHeader extends StatelessWidget {
  final List<String> columns;
  final List<double> columnWidths;

  const AddressListHeader({
    super.key,
    required this.columns,
    required this.columnWidths,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withAlpha(26),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: List.generate(
          columns.length,
          (index) => Expanded(
            flex: (columnWidths[index] * 100).toInt(),
            child: Text(
              columns[index],
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}
