import 'package:flutter_riverpod/flutter_riverpod.dart';

// نموذج بيانات المستخدم
class User {
  final String username;
  final String branch;
  final String role;

  User({required this.username, required this.branch, this.role = 'user'});

  User copyWith({String? username, String? branch, String? role}) {
    return User(
      username: username ?? this.username,
      branch: branch ?? this.branch,
      role: role ?? this.role,
    );
  }
}

// مزود حالة المستخدم
class UserNotifier extends StateNotifier<User> {
  UserNotifier()
      : super(User(username: 'admin', branch: 'Baghdad', role: 'admin'));

  void setUserInfo(String username, String branch, {String role = 'user'}) {
    state = state.copyWith(username: username, branch: branch, role: role);
  }
}

// مزود Riverpod للمستخدم
final userProvider = StateNotifierProvider<UserNotifier, User>((ref) {
  return UserNotifier();
});
