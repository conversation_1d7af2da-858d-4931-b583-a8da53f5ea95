import 'dart:async';

// تعريف أنواع الأحداث
enum EventType {
  countryAdded,
  countryUpdated,
  countryDeleted,
  cityAdded,
  cityUpdated,
  cityDeleted,
  priceAdded,
  priceUpdated,
  priceDeleted,
  dataRefreshNeeded,
}

// فئة الحدث
class AppEvent {
  final EventType type;
  final Map<String, dynamic>? data;

  AppEvent(this.type, {this.data});
}

// خدمة ناقل الأحداث
class EventBusService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final EventBusService _instance = EventBusService._internal();

  factory EventBusService() {
    return _instance;
  }

  EventBusService._internal();

  // تدفق الأحداث
  final StreamController<AppEvent> _eventController =
      StreamController<AppEvent>.broadcast();

  // الحصول على تدفق الأحداث
  Stream<AppEvent> get eventStream => _eventController.stream;

  // نشر حدث جديد
  void fireEvent(AppEvent event) {
    _eventController.add(event);
  }

  // إغلاق تدفق الأحداث عند الانتهاء
  void dispose() {
    _eventController.close();
  }
}
