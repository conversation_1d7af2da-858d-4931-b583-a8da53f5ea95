import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

/// مزيج يحتوي على دوال الحسابات الخاصة بمعلومات التكلفة
mixin CostInfoCalculationsMixin<T extends StatefulWidget> on State<T> {
  // مسجل للأحداث
  final Logger _calculationsLogger = Logger('CostInfoCalculations');

  // دالة مساعدة لتنسيق الأرقام بالفواصل
  String formatNumberWithCommas(double value) {
    try {
      // تنسيق الرقم بالفواصل
      String result = '';

      // التعامل مع الجزء الصحيح
      String valueStr = value.toInt().toString();
      int length = valueStr.length;

      for (int i = 0; i < length; i++) {
        if (i > 0 && (length - i) % 3 == 0) {
          result += ',';
        }
        result += valueStr[i];
      }

      // إضافة الجزء العشري إذا كان موجودًا
      double fraction = value - value.toInt();
      if (fraction > 0) {
        String decimal =
            fraction.toStringAsFixed(2).substring(1); // إزالة الصفر في البداية
        result += decimal;
      }

      return result;
    } catch (e) {
      // في حالة حدوث خطأ مع الأرقام الكبيرة، نستخدم طريقة بديلة
      String valueStr = value.toString();
      if (valueStr.contains('e')) {
        // إذا كان الرقم بصيغة علمية، نحوله إلى صيغة عادية
        return value.toStringAsFixed(0);
      }

      // تنسيق الرقم كنص
      String intPart = valueStr;
      if (valueStr.contains('.')) {
        intPart = valueStr.split('.')[0];
      }

      String result = '';
      int length = intPart.length;

      for (int i = 0; i < length; i++) {
        if (i > 0 && (length - i) % 3 == 0) {
          result += ',';
        }
        result += intPart[i];
      }

      return result;
    }
  }

  // الحصول على متحكمات النصوص
  TextEditingController get insurancePercentController;
  TextEditingController get goodsValueController;
  TextEditingController get insuranceAmountController;
  TextEditingController get exportDocController;
  TextEditingController get boxPackingCostController;
  TextEditingController get doorToDoorCostController;
  TextEditingController get postSubCostController;
  TextEditingController get discountAmountController;
  TextEditingController get totalPostCostController;
  TextEditingController get totalPaidController;
  TextEditingController get unpaidAmountController;
  TextEditingController get totalCostEURController;
  TextEditingController get unpaidEURController;

  // الحصول على حالة خيارات التأمين وتحويل المبلغ غير المدفوع
  bool get useInsurance;
  bool get transferUnpaidToPaid;

  // حساب مبلغ التأمين
  void calculateInsuranceAmount() {
    try {
      // طباعة حالة التأمين للتصحيح
      _calculationsLogger
          .fine('حالة التأمين: ${useInsurance ? "مفعل" : "غير مفعل"}');

      if (useInsurance) {
        // الحصول على نسبة التأمين
        final insurancePercentText =
            insurancePercentController.text.replaceAll(',', '');
        double insurancePercent = double.tryParse(insurancePercentText) ?? 0;

        // الحصول على قيمة البضاعة
        final goodsValueText = goodsValueController.text.replaceAll(',', '');
        double goodsValue = double.tryParse(goodsValueText) ?? 0;

        // حساب مبلغ التأمين
        double insuranceAmount = (insurancePercent / 100) * goodsValue;

        // تنسيق مبلغ التأمين وتعيينه في الحقل
        insuranceAmountController.text =
            formatNumberWithCommas(insuranceAmount);
      } else {
        // إذا كان التأمين غير مفعل، يتم تعيين مبلغ التأمين إلى صفر
        insuranceAmountController.text = formatNumberWithCommas(0);
      }

      // إعادة حساب المجاميع
      calculateTotals();
    } catch (e) {
      _calculationsLogger.warning('خطأ في حساب مبلغ التأمين: $e');
      insuranceAmountController.text = formatNumberWithCommas(0);
    }
  }

  // حساب المجاميع
  void calculateTotals() {
    try {
      // طباعة تشخيصية للقيم قبل الحساب
      debugPrint('بدء حساب المجاميع...');

      double insuranceAmount =
          double.tryParse(insuranceAmountController.text.replaceAll(',', '')) ??
              0;
      double exportDoc =
          double.tryParse(exportDocController.text.replaceAll(',', '')) ?? 0;
      double boxPackingCost =
          double.tryParse(boxPackingCostController.text.replaceAll(',', '')) ??
              0;
      double doorToDoorCost =
          double.tryParse(doorToDoorCostController.text.replaceAll(',', '')) ??
              0;
      double postSubCost =
          double.tryParse(postSubCostController.text.replaceAll(',', '')) ?? 0;
      double discountAmount =
          double.tryParse(discountAmountController.text.replaceAll(',', '')) ??
              0;

      // طباعة تشخيصية للقيم المستخدمة في الحساب
      debugPrint('القيم المستخدمة في الحساب:');
      debugPrint('Insurance Amount: $insuranceAmount');
      debugPrint('Export Doc: $exportDoc');
      debugPrint('Box Packing Cost: $boxPackingCost');
      debugPrint('Door to Door Cost: $doorToDoorCost');
      debugPrint('Post Sub Cost: $postSubCost');
      debugPrint('Discount Amount: $discountAmount');

      // حساب إجمالي تكلفة البريد
      double totalPostCost = insuranceAmount +
          exportDoc +
          boxPackingCost +
          doorToDoorCost +
          postSubCost -
          discountAmount;

      // طباعة تشخيصية للمجموع
      debugPrint('Total Post Cost (calculated): $totalPostCost');

      // تنسيق القيمة وتعيينها في الحقل
      totalPostCostController.text = formatNumberWithCommas(totalPostCost);
      debugPrint(
          'Total Post Cost (displayed): ${totalPostCostController.text}');

      // الحصول على سعر الصرف
      double exchangeRate = getExchangeRate();

      // طباعة القيم للتشخيص
      debugPrint('Exchange Rate: $exchangeRate');

      // حساب إجمالي التكلفة باليورو
      double totalCostEUR = totalPostCost / exchangeRate;
      debugPrint('Total Cost EUR (calculated): $totalCostEUR');

      // تنسيق القيمة بدقة رقمين عشريين
      totalCostEURController.text = totalCostEUR.toStringAsFixed(2);
      debugPrint('Total Cost EUR (displayed): ${totalCostEURController.text}');

      // حساب المبلغ غير المدفوع
      calculateUnpaid();

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            // طباعة تشخيصية للقيم بعد تحديث واجهة المستخدم
            debugPrint('القيم بعد تحديث واجهة المستخدم:');
            debugPrint('Door to Door Cost: ${doorToDoorCostController.text}');
            debugPrint('Total Post Cost: ${totalPostCostController.text}');
          });
        }
      });
    } catch (e) {
      debugPrint('خطأ في حساب المجاميع: $e');
      totalPostCostController.text = formatNumberWithCommas(0);
      totalCostEURController.text = '0.00';
    }
  }

  // حساب المبلغ غير المدفوع
  void calculateUnpaid() {
    try {
      double totalPostCost =
          double.tryParse(totalPostCostController.text.replaceAll(',', '')) ??
              0;
      double totalPaid =
          double.tryParse(totalPaidController.text.replaceAll(',', '')) ?? 0;
      double unpaidAmount = totalPostCost - totalPaid;

      // تنسيق الأرقام بالفواصل
      unpaidAmountController.text = formatNumberWithCommas(unpaidAmount);

      // الحصول على سعر الصرف
      double exchangeRate = getExchangeRate();

      // طباعة القيم للتشخيص
      debugPrint('Unpaid Amount: $unpaidAmount');
      debugPrint('Exchange Rate: $exchangeRate');

      // حساب المبلغ غير المدفوع باليورو
      double unpaidEUR = unpaidAmount / exchangeRate;
      debugPrint('Unpaid EUR (calculated): $unpaidEUR');

      // تنسيق القيمة بدقة رقمين عشريين
      unpaidEURController.text = unpaidEUR.toStringAsFixed(2);
      debugPrint('Unpaid EUR (displayed): ${unpaidEURController.text}');

      // إذا كان خيار تحويل المبلغ غير المدفوع إلى المدفوع مفعل
      if (transferUnpaidToPaid && unpaidAmount > 0) {
        // تحديث قيمة المدفوع وإعادة حساب المبلغ غير المدفوع
        totalPaidController.text = formatNumberWithCommas(totalPostCost);
        unpaidAmountController.text = formatNumberWithCommas(0);
        unpaidEURController.text = '0.00';
      }

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      debugPrint('خطأ في حساب المبلغ غير المدفوع: $e');
      unpaidAmountController.text = formatNumberWithCommas(0);
      unpaidEURController.text = '0.00';
    }
  }

  // الحصول على سعر الصرف من PriceInfo
  double getExchangeRate() {
    double exchangeRate = 0.0;
    try {
      // محاولة الحصول على سعر الصرف من الأب
      final context = this.context;
      final homeScreenState = context.findAncestorStateOfType<State>();
      if (homeScreenState != null) {
        debugPrint('وجدت حالة الشاشة الرئيسية');

        // محاولة الوصول إلى receiverInfoKey بطريقة أكثر أمانًا
        dynamic receiverInfoKey;
        try {
          if (homeScreenState.toString().contains('_HomeScreenState')) {
            // التحقق أولاً مما إذا كانت حالة الشاشة الرئيسية هي _HomeScreenState
            receiverInfoKey = (homeScreenState as dynamic).receiverInfoKey;
            debugPrint('وجدت receiverInfoKey بشكل مباشر: $receiverInfoKey');
          } else {
            debugPrint(
                'الحالة ليست _HomeScreenState: ${homeScreenState.runtimeType}');
          }
        } catch (e) {
          debugPrint('خطأ في الوصول إلى receiverInfoKey: $e');
        }

        if (receiverInfoKey != null) {
          final receiverInfoState = receiverInfoKey.currentState;
          if (receiverInfoState != null) {
            debugPrint('وجدت حالة معلومات المستلم');

            dynamic priceInfoState;
            try {
              priceInfoState =
                  (receiverInfoState as dynamic).getPriceInfoState();
              debugPrint('وجدت حالة معلومات السعر: $priceInfoState');
            } catch (e) {
              debugPrint('خطأ في الحصول على حالة معلومات السعر: $e');
            }

            if (priceInfoState != null) {
              try {
                exchangeRate = (priceInfoState as dynamic).getExchangeRate();
                debugPrint('سعر الصرف من PriceInfo: $exchangeRate');
              } catch (e) {
                debugPrint('خطأ في الحصول على سعر الصرف: $e');
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ عام في الحصول على سعر الصرف: $e');
    }

    // استخدام قيمة افتراضية إذا كان سعر الصرف صفر
    if (exchangeRate <= 0) {
      exchangeRate = 1309.0; // القيمة الافتراضية المستخدمة في التطبيق
      debugPrint('استخدام سعر الصرف الافتراضي: $exchangeRate');
    }

    return exchangeRate;
  }
}
