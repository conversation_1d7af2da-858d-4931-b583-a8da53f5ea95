import 'dart:ffi';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:win32/win32.dart';
import 'dart:convert';
import 'package:ffi/ffi.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// خدمة طباعة متخصصة لنظام Windows باستخدام Win32 APIs
/// تحل مشكلة عدم حفظ إعدادات الطباعة في مكتبة printing العادية
class WindowsPrintService {
  static const String _printSettingsKey = 'windows_print_settings';

  /// حفظ إعدادات الطباعة الخاصة بنوع مستند معين
  static Future<void> saveUserPrintSettings({
    required String documentType,
    String? printerName,
    String? paperSize,
    int? orientation, // 1 = Portrait, 2 = Landscape
    int? copies,
    int? quality,
    Map<String, dynamic>? additionalSettings,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final settings = <String, dynamic>{
        'printerName': printerName,
        'paperSize': paperSize,
        'orientation': orientation,
        'copies': copies,
        'quality': quality,
        'savedAt': DateTime.now().toIso8601String(),
        'documentType': documentType,
        ...?additionalSettings,
      };

      final key = '${_printSettingsKey}_$documentType';
      await prefs.setString(key, jsonEncode(settings));

      debugPrint('💾 تم حفظ إعدادات الطباعة لـ: $documentType');
      debugPrint('   - الطابعة: ${printerName ?? 'غير محدد'}');
      debugPrint('   - حجم الورق: ${paperSize ?? 'غير محدد'}');
      debugPrint(
          '   - الاتجاه: ${orientation == 1 ? 'عمودي' : orientation == 2 ? 'أفقي' : 'افتراضي'}');
      debugPrint('   - عدد النسخ: ${copies ?? 1}');
      debugPrint('   - الجودة: ${quality ?? 'افتراضي'}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات الطباعة: $e');
    }
  }

  /// استرجاع إعدادات الطباعة المحفوظة
  static Future<Map<String, dynamic>?> getUserPrintSettings(
      String documentType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '${_printSettingsKey}_$documentType';
      final settingsJson = prefs.getString(key);

      if (settingsJson == null) {
        debugPrint('📋 لا توجد إعدادات محفوظة لـ: $documentType');
        return null;
      }

      final settings = jsonDecode(settingsJson) as Map<String, dynamic>;
      debugPrint('📋 تم استرجاع إعدادات الطباعة لـ: $documentType');

      return settings;
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع إعدادات الطباعة: $e');
      return null;
    }
  }

  /// الحصول على قائمة الطابعات المتاحة في النظام
  static List<String> getAvailablePrinters() {
    final printers = <String>[];

    try {
      // تخصيص ذاكرة للبيانات
      final pPrinterEnum = calloc<PRINTER_INFO_1>();
      final pcbNeeded = calloc<DWORD>();
      final pcReturned = calloc<DWORD>();

      // استدعاء أول للحصول على حجم البيانات المطلوب
      EnumPrinters(
        PRINTER_ENUM_LOCAL | PRINTER_ENUM_CONNECTIONS,
        nullptr,
        1,
        pPrinterEnum.cast<Uint8>(),
        0,
        pcbNeeded,
        pcReturned,
      );

      final needed = pcbNeeded.value;
      if (needed > 0) {
        // تخصيص الذاكرة المطلوبة
        final buffer = calloc<Uint8>(needed);

        // استدعاء ثاني للحصول على البيانات الفعلية
        final result = EnumPrinters(
          PRINTER_ENUM_LOCAL | PRINTER_ENUM_CONNECTIONS,
          nullptr,
          1,
          buffer,
          needed,
          pcbNeeded,
          pcReturned,
        );

        if (result != 0) {
          final count = pcReturned.value;
          final printerInfos = buffer.cast<PRINTER_INFO_1>();

          for (int i = 0; i < count; i++) {
            final printerInfo = (printerInfos + i).ref;
            if (printerInfo.pName != nullptr) {
              final printerName = printerInfo.pName.toDartString();
              printers.add(printerName);
            }
          }
        }

        calloc.free(buffer);
      }

      calloc.free(pPrinterEnum);
      calloc.free(pcbNeeded);
      calloc.free(pcReturned);
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على قائمة الطابعات: $e');
    }

    return printers;
  }

  /// الحصول على الطابعة الافتراضية في النظام
  static String? getDefaultPrinter() {
    try {
      final bufferSize = calloc<DWORD>();

      // استدعاء أول للحصول على حجم البيانات
      GetDefaultPrinter(nullptr, bufferSize);

      final size = bufferSize.value;
      if (size > 0) {
        final buffer = calloc<Uint16>(size);

        // استدعاء ثاني للحصول على اسم الطابعة
        final result = GetDefaultPrinter(buffer.cast<Utf16>(), bufferSize);

        if (result != 0) {
          final defaultPrinter = buffer.cast<Utf16>().toDartString();
          calloc.free(buffer);
          calloc.free(bufferSize);

          debugPrint('🖨️ الطابعة الافتراضية: $defaultPrinter');
          return defaultPrinter;
        }

        calloc.free(buffer);
      }

      calloc.free(bufferSize);
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الطابعة الافتراضية: $e');
    }

    return null;
  }

  /// طباعة ملف PDF مع استخدام إعدادات Windows المحفوظة
  static Future<bool> printPdfWithWindowsSettings({
    required Uint8List pdfBytes,
    required String documentName,
    required String documentType,
    int copyCount = 1,
  }) async {
    try {
      debugPrint('🖨️ بدء طباعة Windows لـ: $documentType');
      debugPrint('📄 اسم المستند: $documentName');
      debugPrint('📊 عدد النسخ: $copyCount');

      // استرجاع الإعدادات المحفوظة
      final savedSettings = await getUserPrintSettings(documentType);

      // حفظ ملف PDF مؤقت
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(path.join(tempDir.path, '$documentName.pdf'));
      await tempFile.writeAsBytes(pdfBytes);

      // تحضير إعدادات الطباعة
      String? printerName =
          savedSettings?['printerName'] ?? getDefaultPrinter();
      int orientation = savedSettings?['orientation'] ?? 1; // Portrait افتراضي
      int copies = savedSettings?['copies'] ?? copyCount;

      debugPrint('🔧 إعدادات الطباعة:');
      debugPrint('   - الطابعة: ${printerName ?? 'افتراضي'}');
      debugPrint('   - الاتجاه: ${orientation == 1 ? 'عمودي' : 'أفقي'}');
      debugPrint('   - عدد النسخ: $copies');

      // محاولة فتح نافذة الطباعة مع الإعدادات المحفوظة
      final result = await _openWindowsPrintDialog(
        filePath: tempFile.path,
        printerName: printerName,
        copies: copies,
        orientation: orientation,
        documentType: documentType,
      );

      // حذف الملف المؤقت
      try {
        await tempFile.delete();
      } catch (e) {
        debugPrint('⚠️ تعذر حذف الملف المؤقت: $e');
      }

      return result;
    } catch (e) {
      debugPrint('❌ خطأ في طباعة Windows: $e');
      return false;
    }
  }

  /// فتح نافذة الطباعة Windows مع الإعدادات المحددة
  static Future<bool> _openWindowsPrintDialog({
    required String filePath,
    String? printerName,
    int copies = 1,
    int orientation = 1,
    required String documentType,
  }) async {
    try {
      // استخدام ShellExecute لفتح ملف PDF في البرنامج الافتراضي مع إعدادات الطباعة
      final filePathPtr = filePath.toNativeUtf16();
      final actionPtr = 'print'.toNativeUtf16();

      debugPrint('🔧 محاولة فتح نافذة الطباعة...');

      final result = ShellExecute(
        NULL,
        actionPtr,
        filePathPtr,
        nullptr,
        nullptr,
        SW_HIDE, // إخفاء النافذة
      );

      calloc.free(filePathPtr);
      calloc.free(actionPtr);

      if (result > 32) {
        debugPrint('✅ تم فتح نافذة الطباعة بنجاح');

        // حفظ الإعدادات المستخدمة
        await saveUserPrintSettings(
          documentType: documentType,
          printerName: printerName,
          copies: copies,
          orientation: orientation,
        );

        return true;
      } else {
        debugPrint('❌ فشل في فتح نافذة الطباعة، رمز الخطأ: $result');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في فتح نافذة الطباعة: $e');
      return false;
    }
  }

  /// مسح جميع إعدادات الطباعة المحفوظة
  static Future<void> clearAllPrintSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith(_printSettingsKey))
          .toList();

      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('🗑️ تم مسح جميع إعدادات الطباعة المحفوظة (${keys.length} إعدادات)');
    } catch (e) {
      debugPrint('❌ خطأ في مسح إعدادات الطباعة: $e');
    }
  }

  /// عرض جميع إعدادات الطباعة المحفوظة
  static Future<void> showAllSavedPrintSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith(_printSettingsKey))
          .toList();

      if (keys.isEmpty) {
        debugPrint('📋 لا توجد إعدادات طباعة محفوظة');
        return;
      }

      debugPrint('📋 عرض جميع إعدادات الطباعة المحفوظة:');
      debugPrint('═══════════════════════════════════════════');

      for (final key in keys) {
        final settingsJson = prefs.getString(key);

        if (settingsJson != null) {
          try {
            final settings = jsonDecode(settingsJson) as Map<String, dynamic>;
            final documentType = key.replaceFirst('${_printSettingsKey}_', '');
            debugPrint('📄 نوع المستند: $documentType');
            debugPrint(
                '   - الطابعة: ${settings['printerName'] ?? 'غير محدد'}');
            debugPrint(
                '   - حجم الورق: ${settings['paperSize'] ?? 'غير محدد'}');
            debugPrint(
                '   - الاتجاه: ${settings['orientation'] == 1 ? 'عمودي' : settings['orientation'] == 2 ? 'أفقي' : 'غير محدد'}');
            debugPrint('   - عدد النسخ: ${settings['copies'] ?? 1}');
            debugPrint('   - الجودة: ${settings['quality'] ?? 'افتراضي'}');

            if (settings.containsKey('savedAt')) {
              try {
                final savedAt = DateTime.parse(settings['savedAt']);
                final now = DateTime.now();
                final diff = now.difference(savedAt);

                if (diff.inDays > 0) {
                } else if (diff.inHours > 0) {
                } else if (diff.inMinutes > 0) {
                } else {}

                String timeAgo;
                if (diff.inDays > 0) {
                  timeAgo = 'منذ ${diff.inDays} يوم';
                } else if (diff.inHours > 0) {
                  timeAgo = 'منذ ${diff.inHours} ساعة';
                } else if (diff.inMinutes > 0) {
                  timeAgo = 'منذ ${diff.inMinutes} دقيقة';
                } else {
                  timeAgo = 'الآن';
                }
                debugPrint('   - تاريخ الحفظ: $timeAgo');
              } catch (e) {
                debugPrint('   - تاريخ الحفظ: غير صحيح');
              }
            }

            debugPrint('──────────────────────────────');
          } catch (e) {
            debugPrint('❌ خطأ في قراءة إعدادات: $key');
          }
        }
      }

      debugPrint('═══════════════════════════════════════════');
    } catch (e) {
      debugPrint('❌ خطأ في عرض إعدادات الطباعة: $e');
    }
  }
}
