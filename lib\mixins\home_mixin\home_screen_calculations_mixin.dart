import 'package:flutter/material.dart';

/// مزيج يحتوي على دوال الحسابات للشاشة الرئيسية
mixin HomeScreenCalculationsMixin<T extends StatefulWidget> on State<T> {
  // الحصول على مراجع المكونات
  GlobalKey get costInfoKey;
  GlobalKey get weightInfoKey;
  GlobalKey get receiverInfoKey;

  // متغيرات الحسابات
  double get forEachKgValue;
  set forEachKgValue(double value);

  double get minimumPriceValue;
  set minimumPriceValue(double value);

  // دالة لتحديث قيمة Post sub cost
  void updatePostSubCost(double value) {
    // تخزين قيمة For each 1 Kg
    forEachKgValue = value;

    // الحصول على قيمة الوزن الكلي من WeightInfo
    final weightInfoState = weightInfoKey.currentState;
    if (weightInfoState != null) {
      final totalWeight = (weightInfoState as dynamic).getTotalWeight();

      // الحصول على الدولة المختارة من ReceiverInfo
      String? selectedCountry;
      if (receiverInfoKey.currentState != null) {
        try {
          selectedCountry =
              (receiverInfoKey.currentState as dynamic).getSelectedCountry();
        } catch (e) {
          debugPrint('خطأ في الحصول على الدولة المختارة: $e');
        }
      }

      // تحديد حد الوزن بناءً على الدولة
      double weightLimit;
      bool isOutsideEurope = selectedCountry == 'Outside Europe';
      bool isAustraliaOrNewZealand =
          selectedCountry == 'Australia & New Zealand';

      if (isOutsideEurope || isAustraliaOrNewZealand) {
        weightLimit = 5.0;
        debugPrint(
            'الحد الأقصى للوزن للدولة $selectedCountry: $weightLimit كجم');
      } else {
        weightLimit = 7.0;
        debugPrint(
            'الحد الأقصى للوزن للدولة $selectedCountry: $weightLimit كجم');
      }

      // حساب Post sub cost بناءً على قواعد الحساب
      double postSubCost;

      // طباعة تشخيصية
      debugPrint('\n***** بدء حساب Post Sub Cost *****');
      debugPrint('الوزن الكلي: $totalWeight');
      debugPrint('حد الوزن: $weightLimit');
      debugPrint('السعر الأدنى: $minimumPriceValue');
      debugPrint('سعر الكيلوغرام: $forEachKgValue');
      debugPrint('الدولة المحددة: $selectedCountry');

      // تحقق من قاعدة الحساب المناسبة بناءً على الدولة والوزن
      if (totalWeight <= weightLimit) {
        // إذا كان الوزن أقل من أو يساوي الحد المسموح (7 للدول الأوروبية، 5 للدول الأخرى)
        // استخدم minimum price للدولة المحددة
        postSubCost = minimumPriceValue;
        debugPrint(
            '✓ الوزن ($totalWeight كجم) ≤ حد تطبيق السعر الأدنى ($weightLimit كجم)');
        debugPrint(
            '✓ النتيجة: تطبيق السعر الأدنى: $minimumPriceValue للدولة $selectedCountry');
        debugPrint(
            '⚠️ التفاصيل الخاصة بالدولة: $selectedCountry - السعر الأدنى المستخدم: $minimumPriceValue');
      } else {
        // وإلا، استخدم الحساب العادي: الوزن الكلي × سعر الكيلوغرام
        postSubCost = totalWeight * forEachKgValue;
        debugPrint(
            '✓ الوزن ($totalWeight كجم) > حد تطبيق السعر الأدنى ($weightLimit كجم)');
        debugPrint(
            '✓ النتيجة: حساب السعر بالوزن: $totalWeight × $forEachKgValue = $postSubCost');
      }

      debugPrint('القيمة النهائية لـ Post Sub Cost: $postSubCost');
      debugPrint('***** نهاية حساب Post Sub Cost *****\n');

      // تحديث قيمة Post sub cost في CostInfo
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        try {
          // محاولة تحديث قيمة Post sub cost
          (costInfoState as dynamic).updatePostSubCost(postSubCost);
          debugPrint(
              'تم تحديث قيمة Post Sub Cost في CostInfo إلى: $postSubCost');
        } catch (e) {
          debugPrint('خطأ في تحديث قيمة Post Sub Cost في CostInfo: $e');
        }
      } else {
        debugPrint('لم يتم العثور على حالة CostInfo');
      }

      // تحديث قيمة Door to Door Cost بناءً على الوزن الكلي
      updateDoorToDoorCost(totalWeight);
    }
  }

  // دالة تستدعى عند تغيير الوزن الحقيقي
  void onRealWeightChanged() {
    // طباعة تشخيصية
    debugPrint('تم استدعاء دالة onRealWeightChanged');

    try {
      // الحصول على قيمة الوزن الكلي من WeightInfo
      final weightInfoState = weightInfoKey.currentState;
      if (weightInfoState != null) {
        final totalWeight = (weightInfoState as dynamic).getTotalWeight();

        // طباعة تشخيصية
        debugPrint('الوزن الكلي: $totalWeight');
        debugPrint('قيمة forEachKgValue: $forEachKgValue');

        // الحصول على الدولة المختارة من ReceiverInfo
        final receiverInfoState = receiverInfoKey.currentState;
        String? selectedCountry;
        String? selectedCity;
        double forEachKgFromPriceInfo = 0.0;
        double actualMinimumPrice = 0.0;

        if (receiverInfoState != null) {
          try {
            selectedCountry =
                (receiverInfoState as dynamic).getSelectedCountry();
            selectedCity = (receiverInfoState as dynamic).getSelectedCity();

            // طباعة تشخيصية
            debugPrint('الدولة المختارة: $selectedCountry');
            debugPrint('المدينة المختارة: $selectedCity');

            // الحصول على قيمة minimum price من PriceInfo
            try {
              // محاولة الحصول على حالة PriceInfo أولا
              final priceInfoState =
                  (receiverInfoState as dynamic).getPriceInfoState();

              if (priceInfoState != null) {
                // محاولة الحصول على قيمة minimum price مباشرة من PriceInfo
                try {
                  actualMinimumPrice =
                      (priceInfoState as dynamic).getMinimumPrice();
                  debugPrint(
                      'تم الحصول على قيمة minimum price من PriceInfo: $actualMinimumPrice');

                  // إذا كانت القيمة صالحة، نحدث القيمة في المتغير العام
                  if (actualMinimumPrice > 0) {
                    minimumPriceValue = actualMinimumPrice;
                    debugPrint(
                        'تم تحديث قيمة minimumPriceValue إلى القيمة من PriceInfo: $minimumPriceValue');
                  } else {
                    // محاولة الحصول على قيمة minimum price من ReceiverInfo
                    minimumPriceValue =
                        (receiverInfoState as dynamic).getMinimumPrice();

                    // إذا كانت القيمة صفر، استخدم القيم الافتراضية بناءً على الدولة
                    if (minimumPriceValue <= 0) {
                      minimumPriceValue =
                          _getDefaultMinimumPrice(selectedCountry);
                      debugPrint(
                          'استخدام قيمة افتراضية للحد الأدنى للسعر: $minimumPriceValue للدولة: $selectedCountry');
                    }
                  }
                } catch (e) {
                  debugPrint(
                      'خطأ في الحصول على minimum price من PriceInfo: $e');

                  // محاولة الحصول على قيمة Minimum price من ReceiverInfo
                  minimumPriceValue =
                      (receiverInfoState as dynamic).getMinimumPrice();

                  // إذا كانت القيمة صفر، استخدم القيم الافتراضية بناءً على الدولة
                  if (minimumPriceValue <= 0) {
                    minimumPriceValue =
                        _getDefaultMinimumPrice(selectedCountry);
                    debugPrint(
                        'استخدام قيمة افتراضية للحد الأدنى للسعر: $minimumPriceValue للدولة: $selectedCountry');
                  }
                }

                // محاولة الحصول على قيمة For each 1 Kg
                try {
                  forEachKgFromPriceInfo = (priceInfoState as dynamic)
                      .getForEachKgPrice()
                      .toDouble();
                  debugPrint(
                      'قيمة For each 1 Kg من PriceInfo: $forEachKgFromPriceInfo');

                  // تحديث قيمة forEachKgValue دائمًا باستخدام القيمة المستردة من PriceInfo
                  // إذا كانت القيمة صفر أو سالبة، استخدم قيمة افتراضية بناءً على الدولة
                  if (forEachKgFromPriceInfo <= 0) {
                    forEachKgFromPriceInfo =
                        _getDefaultForEachKgPrice(selectedCountry);
                    debugPrint(
                        'استخدام قيمة افتراضية لـ For each 1 Kg: $forEachKgFromPriceInfo');
                  }

                  forEachKgValue = forEachKgFromPriceInfo;
                  debugPrint(
                      'تم تحديث قيمة forEachKgValue إلى: $forEachKgValue');
                } catch (e) {
                  debugPrint('خطأ في الحصول على قيمة For each 1 Kg: $e');
                }
              } else {
                debugPrint('لم يتم العثور على حالة PriceInfo');

                // محاولة الحصول على قيمة Minimum price من ReceiverInfo
                minimumPriceValue =
                    (receiverInfoState as dynamic).getMinimumPrice();

                // إذا كانت القيمة صفر، استخدم القيم الافتراضية بناءً على الدولة
                if (minimumPriceValue <= 0) {
                  minimumPriceValue = _getDefaultMinimumPrice(selectedCountry);
                  debugPrint(
                      'استخدام قيمة افتراضية للحد الأدنى للسعر: $minimumPriceValue للدولة: $selectedCountry');
                }
              }
            } catch (e) {
              debugPrint('خطأ في الحصول على حالة PriceInfo: $e');

              // إذا لم تكن الدالة موجودة، نستخدم قيمة صفر
              minimumPriceValue = 0.0;

              // طباعة تشخيصية
              debugPrint(
                  'تم استخدام قيمة صفر لـ Minimum price: $minimumPriceValue');
              debugPrint('خطأ: $e');
            }
          } catch (e) {
            debugPrint('خطأ في الحصول على حالة ReceiverInfo: $e');
          }
        }

        // طباعة تشخيصية للقيمة النهائية للحد الأدنى للسعر
        debugPrint('القيمة النهائية لـ Minimum price: $minimumPriceValue');

        // تحديد ما إذا كانت الدولة من الدول المحددة
        bool isAustraliaOrNewZealand = false;
        bool isOutsideEurope = false;
        bool isEuropePost = false;

        // تحقق إذا كانت الدولة هي Outside Europe
        if (selectedCountry == 'Outside Europe') {
          isOutsideEurope = true;
          debugPrint('الدولة المختارة هي Outside Europe');
        }

        // تحقق إذا كانت الدولة هي Australia & New Zealand
        if (selectedCountry == 'Australia & New Zealand') {
          isAustraliaOrNewZealand = true;
          debugPrint('الدولة المختارة هي Australia & New Zealand');
        }

        // تحقق إذا كانت الدولة هي Europe Post
        if (selectedCountry == 'Europe Post') {
          isEuropePost = true;
          debugPrint('الدولة المختارة هي Europe Post');
          debugPrint(
              '👉 قيمة minimumPriceValue الحالية لـ Europe Post: $minimumPriceValue');
        }

        // طباعة تشخيصية
        debugPrint('Outside Europe: $isOutsideEurope');
        debugPrint('Australia & New Zealand: $isAustraliaOrNewZealand');
        debugPrint('Europe Post: $isEuropePost');

        // تحديد الحدود للوزن والأسعار
        double weightLimit;

        debugPrint('\n***** قواعد تطبيق الحدود للوزن والأسعار *****');
        debugPrint('الدولة: ${selectedCountry ?? "غير محددة"}');
        debugPrint('الوزن: $totalWeight');

        if (isOutsideEurope || isAustraliaOrNewZealand) {
          weightLimit = 5.0;
          debugPrint(
              'الدولة من فئة (Outside Europe / Australia & New Zealand)');
          debugPrint('الحد الأقصى للوزن لتطبيق السعر الأدنى: 5.0 كجم أو أقل');
          debugPrint(
              'في حال كان الوزن أكبر من 5.0 كجم، سيتم حساب السعر = الوزن × سعر الكيلوغرام');
        } else {
          weightLimit = 7.0;
          debugPrint('الدولة من الفئة الأخرى (أوروبا أو غيرها)');
          debugPrint('الحد الأقصى للوزن لتطبيق السعر الأدنى: 7.0 كجم أو أقل');
          debugPrint(
              'في حال كان الوزن أكبر من 7.0 كجم، سيتم حساب السعر = الوزن × سعر الكيلوغرام');
        }

        // حساب Post sub cost
        double postSubCost;

        debugPrint('\n***** تفاصيل عملية حساب Post Sub Cost *****');
        debugPrint('البيانات المستخدمة في الحساب:');
        debugPrint('- الوزن الإجمالي: $totalWeight كجم');
        debugPrint('- الحد الأقصى للوزن: $weightLimit كجم');
        debugPrint('- السعر الأدنى: $minimumPriceValue');
        debugPrint('- سعر الكيلوغرام الواحد: $forEachKgValue');

        // تأكد من صحة القيم قبل إجراء الحسابات
        if (minimumPriceValue <= 0) {
          minimumPriceValue = _getDefaultMinimumPrice(selectedCountry);
          debugPrint('! تصحيح: تم تعديل السعر الأدنى إلى $minimumPriceValue');

          // طباعة إضافية خاصة بالدولة
          debugPrint(
              '⚠️ تم استخدام القيمة الافتراضية للسعر الأدنى للدولة: $selectedCountry = $minimumPriceValue');
        }

        if (forEachKgValue <= 0) {
          forEachKgValue = _getDefaultForEachKgPrice(selectedCountry);
          debugPrint('! تصحيح: تم تعديل سعر الكيلوغرام إلى $forEachKgValue');
        }

        // تطبيق قاعدة الحساب
        debugPrint('\nتطبيق قاعدة الحساب:');
        if (totalWeight <= weightLimit) {
          // إذا كان الوزن أقل من أو يساوي الحد المسموح (7 للدول الأوروبية، 5 للدول الأخرى)
          // استخدم minimum price للدولة المحددة
          postSubCost = minimumPriceValue;
          debugPrint(
              '✓ الوزن ($totalWeight كجم) ≤ حد تطبيق السعر الأدنى ($weightLimit كجم)');
          debugPrint(
              '✓ النتيجة: تطبيق السعر الأدنى: $minimumPriceValue للدولة $selectedCountry');
          debugPrint(
              '⚠️ التفاصيل الخاصة بالدولة: $selectedCountry - السعر الأدنى المستخدم: $minimumPriceValue');
        } else {
          // وإلا، استخدم الحساب العادي: الوزن الكلي × سعر الكيلوغرام
          postSubCost = totalWeight * forEachKgValue;
          debugPrint(
              '✓ الوزن ($totalWeight كجم) > حد تطبيق السعر الأدنى ($weightLimit كجم)');
          debugPrint(
              '✓ النتيجة: حساب السعر بالوزن: $totalWeight × $forEachKgValue = $postSubCost');
        }

        // التحقق من صحة القيمة النهائية
        if (postSubCost <= 0) {
          debugPrint('! تحذير: القيمة المحسوبة صفر أو سالبة');
          postSubCost = minimumPriceValue;
          debugPrint(
              '! تصحيح: تم استخدام السعر الأدنى بدلاً من ذلك: $postSubCost');
        }

        debugPrint('\n→ القيمة النهائية لـ Post Sub Cost: $postSubCost');
        debugPrint('***** نهاية عملية الحساب *****\n');

        // تحديث قيمة Post sub cost في CostInfo
        final costInfoState = costInfoKey.currentState;
        if (costInfoState != null) {
          // حفظ قيمة postSubCost الحالية قبل التحديث للمقارنة
          double currentPostSubCost = 0;
          try {
            currentPostSubCost = (costInfoState as dynamic).getPostSubCost();
          } catch (e) {
            debugPrint('خطأ في الحصول على قيمة Post Sub Cost الحالية: $e');
          }

          // تحديث قيمة Post Sub Cost فقط إذا كانت مختلفة
          if (currentPostSubCost != postSubCost) {
            (costInfoState as dynamic).updatePostSubCost(postSubCost);
            debugPrint('تم تحديث قيمة Post sub cost إلى: $postSubCost');

            // فرض تحديث واجهة المستخدم
            WidgetsBinding.instance.addPostFrameCallback((_) {
              try {
                (costInfoState as dynamic).setState(() {});
                debugPrint('تم تحديث واجهة المستخدم لـ CostInfo');
              } catch (e) {
                debugPrint('خطأ في تحديث واجهة المستخدم لـ CostInfo: $e');
              }
            });
          } else {
            debugPrint(
                'لم يتم تحديث قيمة Post Sub Cost لأنها نفس القيمة الحالية: $currentPostSubCost');
          }

          // التحقق من القيمة بعد التحديث
          try {
            double updatedPostSubCost =
                (costInfoState as dynamic).getPostSubCost();
            debugPrint(
                'القيمة الحالية في CostInfo لـ Post Sub Cost بعد التحديث: $updatedPostSubCost');

            // التحقق من قيمة العرض في واجهة المستخدم
            String displayedValue =
                (costInfoState as dynamic).getPostSubCostText();
            debugPrint(
                'قيمة Post Sub Cost المعروضة في واجهة المستخدم: $displayedValue');
          } catch (e) {
            debugPrint('خطأ في الحصول على قيم CostInfo بعد التحديث: $e');
          }
        } else {
          // طباعة تشخيصية
          debugPrint('لم يتم العثور على حالة CostInfo');
        }

        // تحديث قيمة Door to Door Cost بناءً على الوزن الكلي
        updateDoorToDoorCost(totalWeight);
      } else {
        // طباعة تشخيصية
        debugPrint('لم يتم العثور على حالة WeightInfo');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث onRealWeightChanged: $e');
    }
  }

  // دالة لتحديث قيمة Door to Door Cost بناءً على الوزن الكلي
  void updateDoorToDoorCost(double totalWeight) {
    try {
      // الحصول على قيمة Door to Door Price من PriceInfo
      final receiverInfoState = receiverInfoKey.currentState;
      double doorToDoorPrice = 0;

      if (receiverInfoState != null) {
        // التحقق مما إذا كان قسم Address Info مفعل
        bool isAddressInfoVisible =
            (receiverInfoState as dynamic).isAddressInfoVisible();

        // طباعة تشخيصية لحالة قسم العنوان
        debugPrint(
            'حالة قسم العنوان: ${isAddressInfoVisible ? "مفعل" : "غير مفعل"}');

        // إذا كان قسم Address Info غير مفعل، نجعل قيمة Door to Door Cost تساوي 0
        if (!isAddressInfoVisible) {
          // تحديث قيمة Door to Door Cost في CostInfo
          final costInfoState = costInfoKey.currentState;
          if (costInfoState != null) {
            debugPrint(
                'قسم Address Info غير مفعل، تعيين Door to Door Cost إلى 0');

            // حفظ القيمة الحالية للمقارنة
            double currentDoorToDoorCost = 0;
            try {
              currentDoorToDoorCost =
                  (costInfoState as dynamic).getDoorToDoorCost();
            } catch (e) {
              debugPrint(
                  'خطأ في الحصول على القيمة الحالية لـ Door to Door Cost: $e');
            }

            // تحديث فقط إذا كانت القيمة مختلفة
            if (currentDoorToDoorCost != 0) {
              (costInfoState as dynamic).setDoorToDoorCost(0.0);

              // فرض تحديث واجهة المستخدم
              WidgetsBinding.instance.addPostFrameCallback((_) {
                try {
                  (costInfoState as dynamic).setState(() {});
                  debugPrint(
                      'تم تحديث واجهة المستخدم لـ CostInfo بعد تعيين Door to Door Cost إلى 0');
                } catch (e) {
                  debugPrint('خطأ في تحديث واجهة المستخدم لـ CostInfo: $e');
                }
              });
            } else {
              debugPrint('لم يتم تحديث Door to Door Cost لأنها بالفعل تساوي 0');
            }

            // طباعة تشخيصية للتأكد من تعيين القيمة
            debugPrint('تم تعيين Door to Door Cost إلى 0');

            // التحقق من القيمة بعد التعيين
            try {
              double currentValue =
                  (costInfoState as dynamic).getDoorToDoorCost();
              debugPrint(
                  'القيمة الحالية لـ Door to Door Cost بعد التعيين: $currentValue');
            } catch (e) {
              debugPrint('خطأ في الحصول على القيمة الحالية: $e');
            }
          }
          return; // الخروج من الدالة
        }

        try {
          final priceInfoState =
              (receiverInfoState as dynamic).getPriceInfoState();
          if (priceInfoState != null) {
            // محاولة الحصول على قيمة Door to Door Price
            try {
              doorToDoorPrice =
                  (priceInfoState as dynamic).getDoorToDoorPrice().toDouble();
              debugPrint('Door to Door Price: $doorToDoorPrice');
            } catch (e) {
              // إذا لم تتمكن من استدعاء getDoorToDoorPrice، حاول استدعاء _getDoorToDoorPrice
              try {
                doorToDoorPrice = (priceInfoState as dynamic)
                    ._getDoorToDoorPrice()
                    .toDouble();
                debugPrint(
                    'Door to Door Price (from _getDoorToDoorPrice): $doorToDoorPrice');
              } catch (e2) {
                // إذا فشلت جميع المحاولات، استخدم قيمة صفر
                debugPrint('فشل في الحصول على Door to Door Price: $e2');
                doorToDoorPrice = 0.0; // قيمة صفر بدلاً من القيمة الافتراضية
              }
            }
          }
        } catch (e) {
          debugPrint('خطأ في الحصول على حالة PriceInfo: $e');
          doorToDoorPrice = 0.0; // قيمة صفر بدلاً من القيمة الافتراضية
        }
      }

      // إذا كانت قيمة Door to Door Price صفر، نستخدم قيمة صفر
      if (doorToDoorPrice <= 0) {
        doorToDoorPrice = 0.0; // قيمة صفر بدلاً من القيمة الافتراضية
        debugPrint(
            'تم استخدام قيمة صفر لـ Door to Door Price: $doorToDoorPrice');
      }

      // حساب Door to Door Cost بناءً على الوزن الكلي
      double doorToDoorCost = 0;

      // حساب عدد المضاعفات بناءً على الوزن
      int multiplier = 1;
      if (totalWeight > 0) {
        // حساب المضاعف (1 للوزن 10 أو أقل، 2 للوزن بين 10 و 20، وهكذا)
        multiplier = ((totalWeight - 0.01) / 10).floor() + 1;
      }

      // حساب التكلفة النهائية
      doorToDoorCost = doorToDoorPrice * multiplier;
      debugPrint(
          'Total Weight: $totalWeight, Multiplier: $multiplier, Door to Door Cost: $doorToDoorCost');

      // تحديث قيمة Door to Door Cost في CostInfo
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        // حفظ القيمة الحالية للمقارنة
        double currentDoorToDoorCost = 0;
        try {
          currentDoorToDoorCost =
              (costInfoState as dynamic).getDoorToDoorCost();
        } catch (e) {
          debugPrint(
              'خطأ في الحصول على القيمة الحالية لـ Door to Door Cost: $e');
        }

        // تحديث فقط إذا كانت القيمة مختلفة
        if (currentDoorToDoorCost != doorToDoorCost) {
          debugPrint('تحديث قيمة Door to Door Cost إلى: $doorToDoorCost');
          (costInfoState as dynamic).setDoorToDoorCost(doorToDoorCost);

          // فرض تحديث واجهة المستخدم
          WidgetsBinding.instance.addPostFrameCallback((_) {
            try {
              (costInfoState as dynamic).setState(() {});
              debugPrint('تم تحديث واجهة المستخدم لـ CostInfo');
            } catch (e) {
              debugPrint('خطأ في تحديث واجهة المستخدم لـ CostInfo: $e');
            }
          });

          // طباعة تشخيصية للتأكد من تعيين القيمة
          debugPrint('تم تعيين Door to Door Cost إلى $doorToDoorCost');

          // التحقق من القيمة بعد التعيين
          try {
            double currentValue =
                (costInfoState as dynamic).getDoorToDoorCost();
            debugPrint(
                'القيمة الحالية لـ Door to Door Cost بعد التعيين: $currentValue');
          } catch (e) {
            debugPrint('خطأ في الحصول على القيمة الحالية: $e');
          }
        } else {
          debugPrint(
              'لم يتم تحديث Door to Door Cost لأنها نفس القيمة الحالية: $currentDoorToDoorCost');
        }
      } else {
        debugPrint('costInfoState هو null، لا يمكن تعيين Door to Door Cost');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث Door to Door Cost: $e');

      // في حالة حدوث خطأ، نحاول تعيين قيمة افتراضية
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        // استخدام قيمة افتراضية للـ Door to Door Cost
        double defaultDoorToDoorCost = 0.0;
        if (totalWeight > 0) {
          int multiplier = ((totalWeight - 0.01) / 10).floor() + 1;
          defaultDoorToDoorCost = 0.0 * multiplier;
        }

        debugPrint(
            'تعيين قيمة افتراضية لـ Door to Door Cost: $defaultDoorToDoorCost');
        (costInfoState as dynamic).setDoorToDoorCost(defaultDoorToDoorCost);

        // فرض تحديث واجهة المستخدم
        WidgetsBinding.instance.addPostFrameCallback((_) {
          try {
            (costInfoState as dynamic).setState(() {});
            debugPrint('تم تحديث واجهة المستخدم لـ CostInfo');
          } catch (e) {
            debugPrint('خطأ في تحديث واجهة المستخدم لـ CostInfo: $e');
          }
        });
      }
    }
  }

  // دالة تستدعى عند تغيير قيمة box no
  void onBoxNoChanged(int boxNo) {
    // الحصول على قيمة pallet no من WeightInfo
    final weightInfoState = weightInfoKey.currentState;
    if (weightInfoState != null) {
      final palletNo = (weightInfoState as dynamic).getPalletNo();

      // تحديث قيمة Box packing cost في CostInfo باستخدام المجموع الكلي
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        final totalItems = boxNo + palletNo;
        (costInfoState as dynamic).updateBoxPackingCost(totalItems);
      }

      final totalWeight = (weightInfoState as dynamic).getTotalWeight();

      // تحديث قيمة Door to Door Cost بناءً على الوزن الكلي
      updateDoorToDoorCost(totalWeight);
    }
  }

  // دالة تستدعى عند تغيير قيمة pallet no
  void onPalletNoChanged(int palletNo) {
    // الحصول على قيمة box no من WeightInfo
    final weightInfoState = weightInfoKey.currentState;
    if (weightInfoState != null) {
      final boxNo = (weightInfoState as dynamic).getBoxNo();

      // تحديث قيمة Box packing cost في CostInfo باستخدام المجموع الكلي
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        final totalItems = boxNo + palletNo;
        (costInfoState as dynamic).updateBoxPackingCost(totalItems);
      }

      final totalWeight = (weightInfoState as dynamic).getTotalWeight();

      // تحديث قيمة Door to Door Cost بناءً على الوزن الكلي
      updateDoorToDoorCost(totalWeight);
    }
  }

  // دالة مساعدة للحصول على السعر الافتراضي للدولة
  double _getDefaultMinimumPrice(String? country) {
    if (country == null) return 35000.0;

    double defaultPrice = 35000.0;
    switch (country) {
      case 'Germany':
      case 'Netherlands':
        defaultPrice = 35000.0;
        break;
      case 'United Kingdom':
      case 'Sweden':
        defaultPrice = 50000.0;
        break;
      case 'Finland':
        defaultPrice = 60000.0;
        break;
      case 'Europe Post':
        defaultPrice = 70000.0;
        debugPrint(
            '⚠️ استخدام القيمة الافتراضية $defaultPrice للحد الأدنى لسعر Europe Post');
        break;
      case 'Outside Europe':
        defaultPrice = 100000.0;
        break;
      case 'Australia & New Zealand':
        defaultPrice = 125000.0;
        break;
      default:
        // للدول الأخرى غير المعروفة، نستخدم قيمة افتراضية
        defaultPrice = 35000.0;
        break;
    }

    debugPrint(
        'استخدام قيمة افتراضية للحد الأدنى للسعر للدولة $country: $defaultPrice');
    return defaultPrice;
  }

  // دالة مساعدة للحصول على السعر الافتراضي للكيلوغرام الواحد بناءً على الدولة
  double _getDefaultForEachKgPrice(String? country) {
    if (country == null) return 10000.0;

    switch (country) {
      case 'Germany':
      case 'Netherlands':
        return 10000.0;
      case 'United Kingdom':
      case 'Sweden':
        return 12000.0;
      case 'Finland':
        return 15000.0;
      case 'Europe Post':
        return 18000.0;
      case 'Outside Europe':
        return 20000.0;
      case 'Australia & New Zealand':
        return 25000.0;
      default:
        return 10000.0;
    }
  }
}
