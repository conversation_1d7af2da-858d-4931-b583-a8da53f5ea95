# EUKnet Company - International Shipping Management System

A Flutter desktop application for managing international shipping operations for EUKnet Company.

## Features

- User-friendly and multilingual interface
- Shipment management and tracking
- Customer data management
- Report generation and export
- Statistical dashboard
- User and permissions management
- Local database

## System Requirements

- Operating System: Windows / macOS / Linux
- Flutter SDK: 3.0.0 or newer
- Dart SDK: 3.0.0 or newer

## Installation

1. Install Flutter SDK from [Flutter official website](https://flutter.dev/docs/get-started/install)
2. Clone the project:
   ```
   git clone https://github.com/yourusername/euknet_company_app.git
   ```
3. Navigate to the project folder:
   ```
   cd euknet_company_app
   ```
4. Install dependencies:
   ```
   flutter pub get
   ```
5. Run the application:
   ```
   flutter run -d windows  # for Windows
   flutter run -d macos    # for macOS
   flutter run -d linux    # for Linux
   ```

## Technical Structure

- **Flutter**: UI framework
- **Riverpod**: Application state management
- **SQLite**: Local database
- **FL Chart**: Charts and statistics
- **PDF**: Report generation and printing

## Project Structure

```
lib/
  ├── main.dart                  # Application entry point
  ├── models/                    # Data models
  ├── providers/                 # Riverpod providers
  │   └── riverpod/              # Riverpod providers
  ├── screens/                   # Application screens
  ├── services/                  # Application services (database, etc.)
  ├── utils/                     # Helper utilities
  ├── widgets/                   # Reusable UI components
  └── pdf_generators/            # PDF file generators
```

## Contributing

Contributions are welcome! Please follow these steps:

1. Open an issue to discuss the change you wish to make
2. Fork the project
3. Create a new branch (`git checkout -b feature/amazing-feature`)
4. Make your changes
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is licensed under the [MIT License](LICENSE).

# ملخص التعديلات على تصميم ملصقات PDF

## التعديلات الرئيسية في تصميم ملصق "post label"

### 1. تصميم الهيدر
- تم إعادة تصميم الهيدر بالكامل بترتيب 5 صور أفقياً: EUKnet_Logo، EU_info، Sters_Logo، QR، info
- تم ضبط أحجام الصور لتناسب عرض الصفحة بنسب محددة
- تم تحسين المسافات بين الصور وتنظيمها بشكل متناسق

### 2. محتوى الجدول
- تم استبدال جدول أسماء المدن وأرقام الهواتف بصورة info.png
- تم تقليل عرض الأعمدة وارتفاع الصفوف في الجدول
- تم تغيير لون حدود الجدول للأسود (PdfColors.grey800) وتقليل سماكتها
- تم إضافة صف خاص لـ "number of parcel" يحتوي على أقسام متعددة مع كلمة "Of" وفواصل عمودية

### 3. تنسيقات النص
- تم جعل قيم الجدول بخط غامق (Bold)
- تم زيادة حجم خط حقل رقم الكود (Code No)
- تم تحسين حجم الخط للبيانات المختلفة

### 4. آلية إنشاء ملفات PDF
- تم تغيير آلية الإنشاء من ملف واحد متعدد الصفحات إلى قائمة ملفات منفصلة
- تم استخدام قيمة box_no لتحديد عدد النسخ المطلوبة وترقيمها (1 من 4، 2 من 4، إلخ)
- أصبح بالإمكان طباعة كل نسخة بشكل منفصل عن الأخرى
