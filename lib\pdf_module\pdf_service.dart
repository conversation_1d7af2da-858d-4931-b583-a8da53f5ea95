import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:printing/printing.dart';
import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import '../services/print_settings_database_service.dart';
import '../models/print_settings_model.dart';

/// خدمة متكاملة للتعامل مع ملفات PDF والطباعة
class PdfService {
  // ================= قسم إنشاء ملفات PDF =================

  /// إنشاء ملف PDF بسيط يحتوي على اسم الشركة وتاريخ اليوم
  static Future<Uint8List> generateSimplePdf({
    required String companyName,
    required String title,
  }) async {
    final pdf = pw.Document();

    // تحميل مجموعة متنوعة من الخطوط العربية
    final Map<String, pw.Font> arabicFonts = await _loadArabicFonts();

    // الحصول على التاريخ الحالي
    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(now);

    // إضافة صفحة بتصميم محسن
    pdf.addPage(
      pw.Page(
        textDirection: pw.TextDirection
            .rtl, // تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
        pageFormat: PdfPageFormat.a4,
        theme: pw.ThemeData.withFont(
          base: arabicFonts['regular']!,
          bold: arabicFonts['bold']!,
        ),
        build: (pw.Context context) {
          return pw.Column(
            children: [
              // هيدر الصفحة
              _buildHeader(companyName, title, arabicFonts),

              pw.SizedBox(height: 40),

              // محتوى الصفحة الرئيسي
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    // عنوان جدول البيانات
                    pw.Text(
                      'بيانات المستند',
                      style: pw.TextStyle(
                        font: arabicFonts['bold'],
                        fontSize: 18,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),

                    pw.SizedBox(height: 15),

                    // جدول بسيط
                    _buildSimpleTable(arabicFonts),

                    pw.SizedBox(height: 30),

                    // معلومات إضافية
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey100,
                        borderRadius:
                            pw.BorderRadius.all(pw.Radius.circular(8)),
                      ),
                      child: pw.Text(
                        'هذا المستند تم إنشاؤه بواسطة نظام $companyName بتاريخ $formattedDate',
                        style: pw.TextStyle(
                          font: arabicFonts['regular'],
                          fontSize: 12,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              // فوتر الصفحة
              _buildFooter(formattedDate, arabicFonts),
            ],
          );
        },
      ),
    );

    // إنشاء ملف PDF كمصفوفة بايت
    return pdf.save();
  }

  /// تحميل الخطوط العربية المطلوبة
  static Future<Map<String, pw.Font>> _loadArabicFonts() async {
    final Map<String, pw.Font> fonts = {};

    try {
      // تحميل الخط العادي
      final regularFontData =
          await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      fonts['regular'] = pw.Font.ttf(regularFontData);

      // تحميل الخط الغامق
      final boldFontData = await rootBundle
          .load('assets/fonts/NotoSansArabic_Condensed-SemiBold.ttf');
      fonts['bold'] = pw.Font.ttf(boldFontData);

      // تحميل الخط المتوسط
      final mediumFontData =
          await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      fonts['medium'] = pw.Font.ttf(mediumFontData);

      // تحميل خط Cairo للعناوين
      final cairoFontData =
          await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      fonts['cairo'] = pw.Font.ttf(cairoFontData);
    } catch (e) {
      debugPrint('خطأ في تحميل الخطوط العربية: $e');
      // في حالة حدوث خطأ، استخدم الخط الافتراضي
      fonts['regular'] = pw.Font.helvetica();
      fonts['bold'] = pw.Font.helveticaBold();
      fonts['medium'] = pw.Font.helvetica();
      fonts['cairo'] = pw.Font.helveticaBold();
    }

    return fonts;
  }

  /// إنشاء هيدر لملف PDF
  static pw.Widget _buildHeader(
      String companyName, String title, Map<String, pw.Font> fonts) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 10),
      decoration: const pw.BoxDecoration(
          border: pw.Border(
              bottom: pw.BorderSide(width: 1, color: PdfColors.grey300))),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // مساحة فارغة للتوازن (بسبب الاتجاه RTL ستظهر على اليسار)
          pw.SizedBox(width: 60, height: 60),

          // معلومات الشركة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text(
                companyName,
                style: pw.TextStyle(
                  font: fonts['cairo'],
                  fontSize: 24,
                  color: PdfColors.blue900,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                title,
                style: pw.TextStyle(
                  font: fonts['medium'],
                  fontSize: 18,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),

          // لوجو الشركة (يمكن استبداله برمز أو صورة حقيقية)
          pw.Container(
            width: 60,
            height: 60,
            decoration: pw.BoxDecoration(
              color: PdfColors.blue100,
              border: pw.Border.all(color: PdfColors.blue800),
              borderRadius: pw.BorderRadius.circular(10),
            ),
            alignment: pw.Alignment.center,
            child: pw.Text(
              'LOGO',
              style: pw.TextStyle(
                color: PdfColors.blue800,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء فوتر لملف PDF
  static pw.Widget _buildFooter(String date, Map<String, pw.Font> fonts) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: const pw.BoxDecoration(
          border: pw.Border(
              top: pw.BorderSide(width: 1, color: PdfColors.grey300))),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'www.euknet.com',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.blue,
              decoration: pw.TextDecoration.underline,
            ),
          ),
          pw.Text(
            'الصفحة ${1}',
            style: pw.TextStyle(
              font: fonts['regular'],
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.Text(
            'تاريخ الطباعة: $date',
            style: pw.TextStyle(
              font: fonts['regular'],
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء جدول بسيط
  static pw.Widget _buildSimpleTable(Map<String, pw.Font> fonts) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FractionColumnWidth(0.3),
        1: const pw.FractionColumnWidth(0.7),
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.blue50),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'العنصر',
                style: pw.TextStyle(
                  font: fonts['bold'],
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'التفاصيل',
                style: pw.TextStyle(
                  font: fonts['bold'],
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
        // بيانات الجدول
        pw.TableRow(
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'رقم المستند',
                style: pw.TextStyle(font: fonts['regular']),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'DOC-2023-001',
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
        pw.TableRow(
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'نوع المستند',
                style: pw.TextStyle(font: fonts['regular']),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'مستند إلكتروني',
                style: pw.TextStyle(font: fonts['regular']),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
        pw.TableRow(
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'الحالة',
                style: pw.TextStyle(font: fonts['regular']),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'جديد',
                style: pw.TextStyle(font: fonts['regular']),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // ================= قسم طباعة ملفات PDF =================



  /// طباعة ملف PDF مع عرض حوار الطباعة
  /// [documentType] هو معرف فريد لنوع المستند المراد طباعته:
  /// - 'office_label' لملصقات المكتب
  /// - 'address_label' لملصقات العناوين
  /// - 'invoice' للفواتير
  /// [copyCount] عدد النسخ المطلوب طباعتها (للإرشاد فقط - يحدد المستخدم العدد النهائي في نافذة الطباعة)
  static Future<bool> printPdf(
      Uint8List pdfBytes, String documentName, String documentType,
      {int copyCount = 1}) async {
    try {
      debugPrint('🖨️ بدء عملية الطباعة لـ: $documentType');
      debugPrint('📄 اسم المستند: $documentName');
      debugPrint('📊 عدد النسخ المتوقع: $copyCount');

      // عرض جميع الإعدادات المحفوظة للتشخيص
      await showAllSavedSettings();

      // الحصول على إعدادات الطباعة المحفوظة لهذا النوع من المستندات بالتحديد
      final PdfPageFormat? savedFormat =
          await _getSavedPageFormat(documentType);

      // تحديد القياسات الافتراضية المناسبة لكل نوع مستند
      final PdfPageFormat defaultFormat =
          _getDefaultFormatForDocumentType(documentType);

      // إعداد نموذج الصفحة المطلوب
      PdfPageFormat initialFormat = savedFormat ?? defaultFormat;

      if (savedFormat != null) {
        debugPrint('✅ تم العثور على إعدادات محفوظة لـ $documentType');
        debugPrint('   - العرض: ${savedFormat.width.toStringAsFixed(1)}');
        debugPrint('   - الارتفاع: ${savedFormat.height.toStringAsFixed(1)}');
      } else {
        debugPrint(
            '📭 لا توجد إعدادات محفوظة لـ $documentType، سيتم استخدام القياسات الافتراضية المناسبة');
        debugPrint(
            '📏 القياسات الافتراضية: ${defaultFormat.width.toStringAsFixed(1)} × ${defaultFormat.height.toStringAsFixed(1)}');
      }

      // محاولة تطبيق الإعدادات المحفوظة مسبقاً قبل فتح نافذة الطباعة
      debugPrint('⚙️ تطبيق الإعدادات المحفوظة قبل فتح نافذة الطباعة...');

      // استخدام layoutPdf لفتح حوار الطباعة القياسي
      // هذه الدالة تفتح نافذة الطباعة للتحكم في الخيارات
      final result = await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async {
          // مقارنة الإعدادات قبل الحفظ
          await comparePrintSettings(documentType, format);

          // حفظ الإعدادات الجديدة في كل مرة يتم تغييرها - خاصة بنوع المستند المحدد
          await _savePageFormat(documentType, format);
          debugPrint('💾 تم حفظ إعدادات الطباعة الجديدة لـ: $documentType');
          debugPrint(
              '   - العرض الجديد: ${format.width.toStringAsFixed(1)} نقطة');
          debugPrint(
              '   - الارتفاع الجديد: ${format.height.toStringAsFixed(1)} نقطة');
          debugPrint(
              '   - الهوامش: [${format.marginTop.toStringAsFixed(1)}, ${format.marginBottom.toStringAsFixed(1)}, ${format.marginLeft.toStringAsFixed(1)}, ${format.marginRight.toStringAsFixed(1)}]');

          // عرض جميع الإعدادات المحدثة
          await showAllSavedSettings();

          return pdfBytes;
        },
        name: documentName,
        dynamicLayout: true,
        // استخدام الإعدادات المحفوظة إذا كانت متوفرة أو القياسات الافتراضية المناسبة
        format: initialFormat,
        // تحسين إعدادات الطباعة
        usePrinterSettings: false, // تعطيل هذا المعامل لتجنب التعارض
      );

      if (result) {
        debugPrint(
            '✅ تمت الطباعة بنجاح (النسخ المتوقعة: $copyCount) مع حفظ الإعدادات لـ: $documentType');
      } else {
        debugPrint('❌ تم إلغاء الطباعة لـ: $documentType');
      }

      return result;
    } catch (e) {
      debugPrint('❌ حدث خطأ أثناء الطباعة لـ $documentType: $e');
      return false;
    }
  }

  /// حفظ إعدادات الصفحة المستخدمة للطباعة
  /// [documentType] هو نوع المستند لحفظ الإعدادات الخاصة به
  static Future<void> _savePageFormat(
      String documentType, PdfPageFormat format) async {
    try {
      debugPrint('💾 حفظ إعدادات الطباعة في قاعدة البيانات لـ $documentType');
      
      final databaseService = PrintSettingsDatabaseService();
      
      // تحويل النقاط إلى مليمتر (1 نقطة = 0.352777778 مم)
      const double pointsToMm = 0.352777778;
      
      final marginTopMm = format.marginTop * pointsToMm;
      final marginBottomMm = format.marginBottom * pointsToMm;
      final marginLeftMm = format.marginLeft * pointsToMm;
      final marginRightMm = format.marginRight * pointsToMm;
      
      // تحديد حجم الورق والاتجاه بناءً على أبعاد الصفحة
      String paperSize = 'A4';
      String orientation = 'Portrait';
      
      // تحديد حجم الورق
      if ((format.width.round() == PdfPageFormat.a3.width.round() && format.height.round() == PdfPageFormat.a3.height.round()) ||
          (format.width.round() == PdfPageFormat.a3.height.round() && format.height.round() == PdfPageFormat.a3.width.round())) {
        paperSize = 'A3';
      } else if ((format.width.round() == PdfPageFormat.a4.width.round() && format.height.round() == PdfPageFormat.a4.height.round()) ||
                 (format.width.round() == PdfPageFormat.a4.height.round() && format.height.round() == PdfPageFormat.a4.width.round())) {
        paperSize = 'A4';
      } else if ((format.width.round() == PdfPageFormat.a5.width.round() && format.height.round() == PdfPageFormat.a5.height.round()) ||
                 (format.width.round() == PdfPageFormat.a5.height.round() && format.height.round() == PdfPageFormat.a5.width.round())) {
        paperSize = 'A5';
      } else if ((format.width.round() == PdfPageFormat.letter.width.round() && format.height.round() == PdfPageFormat.letter.height.round()) ||
                 (format.width.round() == PdfPageFormat.letter.height.round() && format.height.round() == PdfPageFormat.letter.width.round())) {
        paperSize = 'Letter';
      }
      
      // تحديد الاتجاه
      if (format.width > format.height) {
        orientation = 'Landscape';
      }
      
      // الحصول على الإعدادات الحالية أو إنشاء إعدادات جديدة
      PrintSettingsModel? existingSettings = await databaseService.getPrintSettings(documentType);
      
      final settings = PrintSettingsModel(
        id: existingSettings?.id,
        documentType: documentType,
        printerName: existingSettings?.printerName ?? '',
        paperSize: paperSize,
        orientation: orientation,
        marginTop: marginTopMm,
        marginBottom: marginBottomMm,
        marginLeft: marginLeftMm,
        marginRight: marginRightMm,
        dpi: existingSettings?.dpi ?? 300,
        copies: existingSettings?.copies ?? 1,
        isDefault: existingSettings?.isDefault ?? false,
        createdAt: existingSettings?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await databaseService.savePrintSettings(settings);
      
      debugPrint('✅ تم حفظ إعدادات الطباعة بنجاح في قاعدة البيانات:');
      debugPrint('   - نوع المستند: $documentType');
      debugPrint('   - حجم الورق: $paperSize');
      debugPrint('   - الاتجاه: $orientation');
      debugPrint('   - الهامش العلوي: ${marginTopMm.toStringAsFixed(1)} مم');
      debugPrint('   - الهامش السفلي: ${marginBottomMm.toStringAsFixed(1)} مم');
      debugPrint('   - الهامش الأيسر: ${marginLeftMm.toStringAsFixed(1)} مم');
      debugPrint('   - الهامش الأيمن: ${marginRightMm.toStringAsFixed(1)} مم');
      debugPrint('   - وقت الحفظ: ${DateTime.now()}');
      
    } catch (e) {
      debugPrint('❌ حدث خطأ أثناء حفظ إعدادات الطباعة لـ $documentType في قاعدة البيانات: $e');
    }
  }

  /// استرجاع إعدادات الصفحة المحفوظة
  /// [documentType] هو نوع المستند لاسترجاع الإعدادات الخاصة به
  static Future<PdfPageFormat?> _getSavedPageFormat(String documentType) async {
    try {
      debugPrint('🔍 البحث عن إعدادات الطباعة المحفوظة لـ $documentType من قاعدة البيانات');
      
      final databaseService = PrintSettingsDatabaseService();
      final savedSettings = await databaseService.getPrintSettings(documentType);

      if (savedSettings == null) {
        debugPrint('📭 لا توجد إعدادات طباعة محفوظة لـ $documentType في قاعدة البيانات');
        return null;
      }

      debugPrint('📂 تم استرجاع إعدادات الطباعة المحفوظة من قاعدة البيانات:');
      debugPrint('   - نوع المستند: $documentType');
      debugPrint('   - اسم الطابعة: ${savedSettings.printerName ?? 'غير محدد'}');
      debugPrint('   - حجم الورق: ${savedSettings.paperSize}');
      debugPrint('   - الاتجاه: ${savedSettings.orientation}');
      debugPrint('   - الهامش العلوي: ${savedSettings.marginTop.toStringAsFixed(1)} مم');
      debugPrint('   - الهامش السفلي: ${savedSettings.marginBottom.toStringAsFixed(1)} مم');
      debugPrint('   - الهامش الأيسر: ${savedSettings.marginLeft.toStringAsFixed(1)} مم');
      debugPrint('   - الهامش الأيمن: ${savedSettings.marginRight.toStringAsFixed(1)} مم');
      debugPrint('   - الدقة: ${savedSettings.dpi} DPI');
      debugPrint('   - عدد النسخ: ${savedSettings.copies}');
      debugPrint('   - تاريخ التحديث: ${savedSettings.updatedAt}');

      // تحويل إعدادات قاعدة البيانات إلى PdfPageFormat
      final pageFormat = _convertSettingsToPageFormat(savedSettings);

      debugPrint('✅ تم تحويل إعدادات قاعدة البيانات إلى PdfPageFormat بنجاح لـ $documentType');
      return pageFormat;
    } catch (e) {
      debugPrint(
          '❌ حدث خطأ أثناء استرجاع إعدادات الطباعة لـ $documentType من قاعدة البيانات: $e');
      return null;
    }
  }

  /// تحويل PrintSettingsModel إلى PdfPageFormat
  static PdfPageFormat _convertSettingsToPageFormat(PrintSettingsModel settings) {
    // تحويل الهوامش من مليمتر إلى نقاط (1 مم = 2.834645669 نقطة)
    const double mmToPoints = 2.834645669;
    
    final marginTop = settings.marginTop * mmToPoints;
    final marginBottom = settings.marginBottom * mmToPoints;
    final marginLeft = settings.marginLeft * mmToPoints;
    final marginRight = settings.marginRight * mmToPoints;
    
    // تحديد أبعاد الصفحة بناءً على حجم الورق والاتجاه
    PdfPageFormat baseFormat;
    switch (settings.paperSize.toUpperCase()) {
      case 'A3':
        baseFormat = PdfPageFormat.a3;
        break;
      case 'A4':
        baseFormat = PdfPageFormat.a4;
        break;
      case 'A5':
        baseFormat = PdfPageFormat.a5;
        break;
      case 'A6':
        baseFormat = PdfPageFormat.a6;
        break;
      case 'LETTER':
        baseFormat = PdfPageFormat.letter;
        break;
      case 'LEGAL':
        baseFormat = PdfPageFormat.legal;
        break;
      default:
        baseFormat = PdfPageFormat.a4;
        break;
    }
    
    // تطبيق الاتجاه (Portrait أو Landscape)
    double width = baseFormat.width;
    double height = baseFormat.height;
    
    if (settings.orientation.toLowerCase() == 'landscape') {
      // تبديل العرض والارتفاع للاتجاه الأفقي
      width = baseFormat.height;
      height = baseFormat.width;
    }
    
    debugPrint('📐 تحويل إعدادات الطباعة:');
    debugPrint('   - حجم الورق: ${settings.paperSize} -> ${width.toStringAsFixed(1)}×${height.toStringAsFixed(1)} نقطة');
    debugPrint('   - الاتجاه: ${settings.orientation}');
    debugPrint('   - الهوامش (مم -> نقطة): [${settings.marginTop}→${marginTop.toStringAsFixed(1)}, ${settings.marginBottom}→${marginBottom.toStringAsFixed(1)}, ${settings.marginLeft}→${marginLeft.toStringAsFixed(1)}, ${settings.marginRight}→${marginRight.toStringAsFixed(1)}]');
    
    return PdfPageFormat(
      width,
      height,
      marginTop: marginTop,
      marginBottom: marginBottom,
      marginLeft: marginLeft,
      marginRight: marginRight,
    );
  }

  /// حذف الإعدادات المحفوظة لنوع معين من المستندات أو جميع الإعدادات
  static Future<void> clearSavedSettings([String? documentType]) async {
    try {
      final databaseService = PrintSettingsDatabaseService();

      if (documentType != null) {
        // حذف الإعدادات الخاصة بنوع مستند معين
        await databaseService.deletePrintSettings(documentType);
        debugPrint('🗑️ تم حذف إعدادات الطباعة المحفوظة لـ $documentType من قاعدة البيانات');
      } else {
        // حذف جميع الإعدادات
        final allSettings = await databaseService.getAllPrintSettings();
        for (var setting in allSettings) {
          await databaseService.deletePrintSettings(setting.documentType);
        }
        debugPrint(
            '🗑️ تم حذف جميع إعدادات الطباعة المحفوظة (${allSettings.length} إعدادات) من قاعدة البيانات');
      }
    } catch (e) {
      debugPrint('❌ حدث خطأ أثناء حذف إعدادات الطباعة من قاعدة البيانات: $e');
    }
  }

  /// عرض جميع الإعدادات المحفوظة مع تفاصيل كاملة
  static Future<void> showAllSavedSettings() async {
    try {
      final databaseService = PrintSettingsDatabaseService();
      final allSettings = await databaseService.getAllPrintSettings();

      if (allSettings.isEmpty) {
        debugPrint('📋 لا توجد إعدادات طباعة محفوظة في قاعدة البيانات');
        return;
      }

      debugPrint('📋 إعدادات الطباعة المحفوظة (${allSettings.length} إعدادات):');
      debugPrint('═══════════════════════════════════════════');

      for (var setting in allSettings) {
        debugPrint('   📄 نوع المستند: ${setting.documentType}');
        debugPrint('      - اسم الطابعة: ${setting.printerName?.isNotEmpty == true ? setting.printerName : 'غير محدد'}');
        debugPrint('      - حجم الورق: ${setting.paperSize}');
        debugPrint('      - الاتجاه: ${setting.orientation}');
        debugPrint('      - الهامش العلوي: ${setting.marginTop.toStringAsFixed(1)} مم');
        debugPrint('      - الهامش السفلي: ${setting.marginBottom.toStringAsFixed(1)} مم');
        debugPrint('      - الهامش الأيسر: ${setting.marginLeft.toStringAsFixed(1)} مم');
        debugPrint('      - الهامش الأيمن: ${setting.marginRight.toStringAsFixed(1)} مم');
        debugPrint('      - الدقة: ${setting.dpi} DPI');
        debugPrint('      - عدد النسخ: ${setting.copies}');
        debugPrint('      - افتراضي: ${setting.isDefault ? 'نعم' : 'لا'}');
        
        final now = DateTime.now();
        String timeAgo;
        
        if (setting.updatedAt != null) {
          final difference = now.difference(setting.updatedAt!);
          
          if (difference.inDays > 0) {
            timeAgo = 'منذ ${difference.inDays} يوم';
          } else if (difference.inHours > 0) {
            timeAgo = 'منذ ${difference.inHours} ساعة';
          } else if (difference.inMinutes > 0) {
            timeAgo = 'منذ ${difference.inMinutes} دقيقة';
          } else {
            timeAgo = 'منذ لحظات';
          }
        } else {
          timeAgo = 'غير محدد';
        }

        debugPrint('      - تاريخ التحديث: ${setting.updatedAt ?? 'غير محدد'} ($timeAgo)');
        debugPrint('      ──────────────────────────────');
      }
      debugPrint('═══════════════════════════════════════════');
    } catch (e) {
      debugPrint('❌ حدث خطأ أثناء عرض إعدادات الطباعة من قاعدة البيانات: $e');
    }
  }

  /// مقارنة إعدادات الطباعة وتشخيص الاختلافات
  static Future<void> comparePrintSettings(
      String documentType, PdfPageFormat currentFormat) async {
    try {
      final savedFormat = await _getSavedPageFormat(documentType);

      debugPrint('🔍 مقارنة إعدادات الطباعة لـ $documentType:');
      debugPrint('══════════════════════════════════════════');

      if (savedFormat == null) {
        debugPrint('⚠️ لا توجد إعدادات محفوظة للمقارنة');
        debugPrint('📊 الإعدادات الحالية:');
        debugPrint(
            '   - العرض: ${currentFormat.width.toStringAsFixed(1)} نقطة');
        debugPrint(
            '   - الارتفاع: ${currentFormat.height.toStringAsFixed(1)} نقطة');
        return;
      }

      debugPrint('📊 المقارنة:');

      // مقارنة العرض
      final widthDiff = (currentFormat.width - savedFormat.width).abs();
      final widthStatus = widthDiff < 0.1 ? '✅' : '⚠️';
      debugPrint('   العرض: $widthStatus');
      debugPrint('      - محفوظ: ${savedFormat.width.toStringAsFixed(1)} نقطة');
      debugPrint(
          '      - حالي: ${currentFormat.width.toStringAsFixed(1)} نقطة');
      if (widthDiff >= 0.1) {
        debugPrint('      - الفرق: ${widthDiff.toStringAsFixed(1)} نقطة');
      }

      // مقارنة الارتفاع
      final heightDiff = (currentFormat.height - savedFormat.height).abs();
      final heightStatus = heightDiff < 0.1 ? '✅' : '⚠️';
      debugPrint('   الارتفاع: $heightStatus');
      debugPrint(
          '      - محفوظ: ${savedFormat.height.toStringAsFixed(1)} نقطة');
      debugPrint(
          '      - حالي: ${currentFormat.height.toStringAsFixed(1)} نقطة');
      if (heightDiff >= 0.1) {
        debugPrint('      - الفرق: ${heightDiff.toStringAsFixed(1)} نقطة');
      }

      // مقارنة الهوامش
      final marginDiffs = [
        ('الهامش العلوي', currentFormat.marginTop, savedFormat.marginTop),
        ('الهامش السفلي', currentFormat.marginBottom, savedFormat.marginBottom),
        ('الهامش الأيسر', currentFormat.marginLeft, savedFormat.marginLeft),
        ('الهامش الأيمن', currentFormat.marginRight, savedFormat.marginRight),
      ];

      for (var (name, current, saved) in marginDiffs) {
        final diff = (current - saved).abs();
        final status = diff < 0.1 ? '✅' : '⚠️';
        debugPrint('   $name: $status');
        debugPrint('      - محفوظ: ${saved.toStringAsFixed(1)} نقطة');
        debugPrint('      - حالي: ${current.toStringAsFixed(1)} نقطة');
        if (diff >= 0.1) {
          debugPrint('      - الفرق: ${diff.toStringAsFixed(1)} نقطة');
        }
      }

      debugPrint('══════════════════════════════════════════');
    } catch (e) {
      debugPrint('❌ حدث خطأ أثناء مقارنة إعدادات الطباعة: $e');
    }
  }

  /// تحديد القياسات الافتراضية المناسبة لكل نوع مستند
  static PdfPageFormat _getDefaultFormatForDocumentType(String documentType) {
    switch (documentType.toLowerCase()) {
      case 'office_label':
        // ملصق المكتب - قياس A6 (105×148 مم) مناسب للملصقات الصغيرة
        debugPrint('📏 استخدام قياس A6 لملصق المكتب');
        return PdfPageFormat.a6;

      case 'post_label':
        // ملصق العنوان/البريد - قياس مخصص (210×148 مم) كما هو محدد في post_label.dart
        debugPrint('📏 استخدام قياس مخصص (210×148 مم) لملصق العنوان');
        const double customWidth = 595.28; // 210mm
        const double customHeight = 419.53; // 148mm
        return PdfPageFormat(
          customWidth,
          customHeight,
          marginTop: 30.0,
          marginBottom: 20.0,
          marginLeft: 30.0,
          marginRight: 20.0,
        );

      case 'invoice':
        // الفاتورة - قياس A4 مناسب للفواتير والمستندات الرسمية
        debugPrint('📏 استخدام قياس A4 للفاتورة');
        return PdfPageFormat.a4;

      default:
        // للأنواع غير المعروفة، استخدم A4 كافتراضي
        debugPrint(
            '📏 نوع مستند غير معروف ($documentType)، استخدام قياس A4 كافتراضي');
        return PdfPageFormat.a4;
    }
  }

  /// دالة محسنة لطباعة PDF مع مراقبة مفصلة
  /// هذه الدالة تحل مشكلة عدم حفظ إعدادات الطباعة في Windows
  static Future<bool> printPdfWithEnhancedMonitoring(
      Uint8List pdfBytes, String documentName, String documentType,
      {int copyCount = 1}) async {
    try {
      debugPrint('🔧 استخدام دالة الطباعة المحسنة...');
      debugPrint('🖨️ بدء عملية الطباعة المحسنة لـ: $documentType');
      debugPrint('📄 اسم المستند: $documentName');
      debugPrint('📊 عدد النسخ المتوقع: $copyCount');
      debugPrint('🖥️ نظام التشغيل: ${Platform.operatingSystem}');

      // عرض جميع الإعدادات المحفوظة قبل بدء الطباعة
      debugPrint('📋 مراجعة الإعدادات قبل بدء الطباعة:');
      await showAllSavedSettings();

      // الحصول على إعدادات الطباعة المحفوظة لهذا النوع من المستندات بالتحديد
      final PdfPageFormat? savedFormat =
          await _getSavedPageFormat(documentType);

      // تحديد القياسات الافتراضية المناسبة لكل نوع مستند
      final PdfPageFormat defaultFormat =
          _getDefaultFormatForDocumentType(documentType);

      // إعداد نموذج الصفحة المطلوب
      PdfPageFormat initialFormat = savedFormat ?? defaultFormat;

      if (savedFormat != null) {
        debugPrint('✅ تم العثور على إعدادات محفوظة لـ $documentType');
        debugPrint('📐 إعدادات محفوظة:');
        debugPrint('   - العرض: ${savedFormat.width.toStringAsFixed(2)} نقطة');
        debugPrint(
            '   - الارتفاع: ${savedFormat.height.toStringAsFixed(2)} نقطة');
        debugPrint(
            '   - الهوامش: [${savedFormat.marginTop.toStringAsFixed(2)}, ${savedFormat.marginBottom.toStringAsFixed(2)}, ${savedFormat.marginLeft.toStringAsFixed(2)}, ${savedFormat.marginRight.toStringAsFixed(2)}] نقطة');
      } else {
        debugPrint('📭 لا توجد إعدادات محفوظة لـ $documentType');
        debugPrint('📐 سيتم استخدام الإعدادات الافتراضية:');
        debugPrint(
            '   - العرض: ${defaultFormat.width.toStringAsFixed(2)} نقطة');
        debugPrint(
            '   - الارتفاع: ${defaultFormat.height.toStringAsFixed(2)} نقطة');
        debugPrint(
            '   - الهوامش: [${defaultFormat.marginTop.toStringAsFixed(2)}, ${defaultFormat.marginBottom.toStringAsFixed(2)}, ${defaultFormat.marginLeft.toStringAsFixed(2)}, ${defaultFormat.marginRight.toStringAsFixed(2)}] نقطة');
      }

      // متغيرات لمراقبة عمليات onLayout
      int layoutCallCount = 0;
      PdfPageFormat? lastKnownFormat;

      debugPrint('⚙️ فتح نافذة الطباعة مع مراقبة مفصلة...');

      // استخدام layoutPdf مع مراقبة محسنة
      final result = await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async {
          layoutCallCount++;
          debugPrint('🔄 onLayout تم استدعاؤها - المرة رقم: $layoutCallCount');
          debugPrint('📊 معطيات الاستدعاء:');
          debugPrint('   - العرض: ${format.width.toStringAsFixed(2)} نقطة');
          debugPrint('   - الارتفاع: ${format.height.toStringAsFixed(2)} نقطة');
          debugPrint(
              '   - الهوامش: [${format.marginTop.toStringAsFixed(2)}, ${format.marginBottom.toStringAsFixed(2)}, ${format.marginLeft.toStringAsFixed(2)}, ${format.marginRight.toStringAsFixed(2)}] نقطة');

          // التحقق من تغييرات الإعدادات
          if (lastKnownFormat != null) {
            debugPrint('🔍 فحص التغييرات من الاستدعاء السابق:');
            final widthChanged =
                (format.width - lastKnownFormat!.width).abs() > 0.1;
            final heightChanged =
                (format.height - lastKnownFormat!.height).abs() > 0.1;
            final marginsChanged = (format.marginTop -
                            lastKnownFormat!.marginTop)
                        .abs() >
                    0.1 ||
                (format.marginBottom - lastKnownFormat!.marginBottom).abs() >
                    0.1 ||
                (format.marginLeft - lastKnownFormat!.marginLeft).abs() > 0.1 ||
                (format.marginRight - lastKnownFormat!.marginRight).abs() > 0.1;

            if (widthChanged || heightChanged || marginsChanged) {
              debugPrint('   ⚠️ تم اكتشاف تغييرات في الإعدادات');
              if (widthChanged) debugPrint('     - العرض تغير');
              if (heightChanged) debugPrint('     - الارتفاع تغير');
              if (marginsChanged) debugPrint('     - الهوامش تغيرت');
            } else {
              debugPrint('   ✅ لا توجد تغييرات في الإعدادات');
            }
          }

          lastKnownFormat = format;

          // مقارنة الإعدادات مع المحفوظة
          await comparePrintSettings(documentType, format);

          // حفظ الإعدادات الجديدة
          await _savePageFormat(documentType, format);

          debugPrint('💾 تم حفظ إعدادات الطباعة للمرة رقم: $layoutCallCount');
          debugPrint('📈 إحصائيات الاستدعاء:');
          debugPrint('   - عدد مرات استدعاء onLayout: $layoutCallCount');
          debugPrint(
              '   - آخر تنسيق محفوظ: ${format.width.toStringAsFixed(1)}×${format.height.toStringAsFixed(1)}');

          return pdfBytes;
        },
        name: documentName,
        dynamicLayout: true,
        format: initialFormat,
        usePrinterSettings: false, // تعطيل هذا لتجنب تعارض Windows
      );

      debugPrint('🏁 انتهت عملية الطباعة:');
      debugPrint('   - نتيجة الطباعة: ${result ? 'نجحت' : 'فشلت/تم الإلغاء'}');
      debugPrint('   - عدد مرات استدعاء onLayout: $layoutCallCount');

      if (result) {
        debugPrint('✅ تمت الطباعة بنجاح لـ: $documentType');
        debugPrint('📋 عرض الإعدادات النهائية المحفوظة:');
        await showAllSavedSettings();
      } else {
        debugPrint('❌ تم إلغاء الطباعة أو فشلت لـ: $documentType');
      }

      return result;
    } catch (e) {
      debugPrint('❌ حدث خطأ في دالة الطباعة المحسنة لـ $documentType: $e');
      debugPrint('📊 تفاصيل الخطأ: ${e.toString()}');
      if (e is StackTrace) {
        debugPrint('📋 مسار الخطأ: $e');
      }
      return false;
    }
  }
}
