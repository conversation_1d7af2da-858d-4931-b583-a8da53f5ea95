import 'package:flutter/foundation.dart';
import 'invoice_template.dart';
import 'post_label.dart';
import 'office_label.dart' as office;
import '../utils/invoice_language_helper.dart';

/// فئة لإنشاء قوالب مختلفة لملفات PDF
class PdfTemplates {
  /// أنواع المستندات المدعومة
  static const String officeLabel = 'office_label';
  static const String postLabel = 'post_label';
  static const String invoice = 'invoice';

  /// إنشاء ملصق المكتب
  /// يعيد إما ملف PDF واحد أو قائمة ملفات PDF منفصلة حسب نوع الملصق
  static Future<dynamic> generateInDocument({
    required String companyName,
    Map<String, dynamic>? shipmentData,
    bool forPreview = false,
  }) async {
    if (shipmentData != null) {
      // التحقق مما إذا كان هناك قيمة في عمود postal_code
      final hasPostalCode = shipmentData['postal_code'] != null &&
          shipmentData['postal_code'].toString().isNotEmpty;

      // إنشاء النوع المناسب من الملصق
      if (hasPostalCode) {
        // إذا كان لديه رمز بريدي، قم بإنشاء ملصق العنوان الكامل
        // هذا سيعيد قائمة من ملفات PDF منفصلة
        return PostLabel.generate(
          companyName: companyName,
          recipientData: shipmentData,
          forPreview: forPreview,
        );
      } else {
        // إذا لم يكن لديه رمز بريدي، قم بإنشاء ملصق المكتب البسيط (ملف واحد)
        return office.OfficeLabel.generate(
          companyName: companyName,
          shipmentData: shipmentData,
          forPreview: forPreview,
        );
      }
    } else {
      // في حالة عدم وجود بيانات، قم بإنشاء ملصق مكتب بسيط بدون بيانات
      return office.OfficeLabel.generate(
        companyName: companyName,
        shipmentData: {},
        forPreview: forPreview,
      );
    }
  }

  /// إنشاء فاتورة بتصميم خاص
  static Future<Uint8List> generatePoDocument({
    required String companyName,
    required String branchName,
    required String branchAddress,
    required String branchPhone,
    required String codeNo,
    required String truckNo,
    required Map<String, dynamic> codeData,
    String? language, // إضافة معامل اللغة
  }) async {
    // الحصول على اللغة المحفوظة إذا لم يتم تمريرها
    final invoiceLanguage =
        language ?? await InvoiceLanguageHelper.getSavedLanguage();

    // استدعاء الملف المنفصل الخاص بالفاتورة
    return InvoiceTemplate.generate(
      companyName: companyName,
      branchName: branchName,
      branchAddress: branchAddress,
      branchPhone: branchPhone,
      codeNo: codeNo,
      truckNo: truckNo,
      codeData: codeData,
      language: invoiceLanguage,
    );
  }
}
