import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../services/database_helper.dart';
import '../../utils/constants.dart';
import '../../utils/ui_helper.dart';
import '../../widgets/sender_info/phone_search_dialog.dart';
import '../../widgets/sender_info/goods_dialog.dart';
import '../../models/item_data.dart';

/// مزيج يحتوي على دوال التعامل مع نوافذ الحوار لمكون معلومات المرسل
mixin SenderInfoDialogsMixin<T extends StatefulWidget> on State<T> {
  // إنشاء مسجل للأحداث
  final Logger _logger = Logger('SenderInfoDialogsMixin');

  // الحصول على المتحكمات النصية
  @protected
  TextEditingController get _senderPhoneController;

  // الحصول على المتغيرات والدوال من المزيجات الأخرى
  @protected
  // ignore: unused_element
  String get _senderIdImagePath;
  @protected
  List<ItemData> get _allItems;
  @protected
  List<ItemData> get _selectedItems;

  // دوال تعيين البيانات
  @protected
  void setSenderIdType(String value);
  @protected
  void setSenderIdImagePath(String value);

  // عرض نافذة حوار نوع الهوية وصورة الهوية
  @protected
  // ignore: unused_element
  void _showIdTypeDialog() {
    // استخدام _senderIdImagePath لإزالة التحذير
    // ignore: unused_local_variable
    final imagePath = _senderIdImagePath;
    // ... existing code ...
  }

  // عرض نافذة حوار البحث عن رقم الهاتف
  @protected
  // ignore: unused_element
  void _showPhoneSearchDialog() async {
    final String phoneNumber = _senderPhoneController.text;
    if (phoneNumber.isEmpty) {
      if (!mounted) return;
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.enterPhoneFirst,
        isError: true,
      );
      return;
    }

    // استخدام UiHelper لعرض نافذة حوار التحميل وإجراء البحث
    try {
      final results = await UiHelper.showLoadingDialog(
        context: context,
        loadingText: SenderInfoStrings.searching,
        asyncFunction: () async {
          final databaseHelper = DatabaseHelper();
          return await databaseHelper.searchCodeDataByColumn(
              'sender_phone', phoneNumber);
        },
      );

      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض نتائج البحث
      showDialog(
        context: context,
        builder: (context) => PhoneSearchDialog(
          phoneNumber: phoneNumber,
          results: results,
        ),
      );
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في البحث عن رقم الهاتف: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorSearchingPhone} $e',
        isError: true,
      );
    }
  }

  // عرض نافذة حوار وصف البضائع
  @protected
  // ignore: unused_element
  void _showGoodsDescriptionDialog() {
    showDialog(
      context: context,
      useSafeArea: false,
      barrierDismissible: true,
      builder: (context) => GoodsDialog(
        allItems: _allItems,
        selectedItems: _selectedItems,
        onSave: _onSaveGoodsDialog,
        onImportFromExcel: _importFromExcel,
        onExportToExcel: _exportToExcel,
      ),
    );
  }

  // دالة معالجة حفظ نافذة حوار البضائع
  @protected
  void _onSaveGoodsDialog(List<ItemData> allItems, List<ItemData> selectedItems,
      String goodsDescription);

  // دالة تصدير البيانات إلى ملف Excel
  @protected
  void _exportToExcel(BuildContext dialogContext, List<ItemData> items);

  // دالة استيراد البيانات من ملف Excel
  @protected
  void _importFromExcel(
      BuildContext dialogContext, StateSetter setState, List<ItemData> items);
}
