import 'package:flutter/material.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import '../../utils/constants.dart';

/// نافذة منبثقة لاختيار نوع الهوية وصورة الهوية
class IdDialog extends StatefulWidget {
  final String initialIdType;
  final String initialImagePath;
  final String currentCode;
  final Function(String, String) onSave;

  const IdDialog({
    super.key,
    required this.initialIdType,
    required this.initialImagePath,
    required this.currentCode,
    required this.onSave,
  });

  @override
  State<IdDialog> createState() => _IdDialogState();
}

class _IdDialogState extends State<IdDialog> {
  late String selectedIdType;
  late String selectedImagePath;
  File? imageFile;

  @override
  void initState() {
    super.initState();
    selectedIdType =
        widget.initialIdType.isEmpty ? 'ID Card' : widget.initialIdType;
    selectedImagePath = widget.initialImagePath;
    if (selectedImagePath.isNotEmpty) {
      imageFile = File(selectedImagePath);
    }
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;

    // حساب العرض والارتفاع الجديدين (زيادة 20% للعرض و 10% للارتفاع)
    final dialogWidth = screenSize.width * 0.6; // زيادة 20% من العرض الافتراضي
    final dialogHeight =
        screenSize.height * 0.7; // زيادة 10% من الارتفاع الافتراضي

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      // تعيين الحجم الجديد للنافذة
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان النافذة وزر الإغلاق
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  SenderInfoStrings.selectIdTypeAndImage,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // محتوى النافذة
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اختيار نوع الهوية
                    const Text(
                      SenderInfoStrings.idType,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: selectedIdType,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      ),
                      items: const [
                        DropdownMenuItem(
                            value: 'ID Card', child: Text('ID Card')),
                        DropdownMenuItem(
                            value: 'Passport', child: Text('Passport')),
                        DropdownMenuItem(
                            value: 'Driver License',
                            child: Text('Driver License')),
                        DropdownMenuItem(
                            value: 'Residence Card',
                            child: Text('Residence Card')),
                        DropdownMenuItem(
                            value: 'National ID', child: Text('National ID')),
                        DropdownMenuItem(
                            value: 'Military ID', child: Text('Military ID')),
                        DropdownMenuItem(value: 'Other', child: Text('Other')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedIdType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 20),

                    // عرض صورة الهوية
                    const Text(
                      SenderInfoStrings.idImage,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      height:
                          dialogHeight * 0.4, // زيادة ارتفاع مساحة عرض الصورة
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: imageFile != null
                          ? Image.file(imageFile!, fit: BoxFit.contain)
                          : const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.image_not_supported,
                                      size: 48, color: Colors.grey),
                                  SizedBox(height: 8),
                                  Text(SenderInfoStrings.noImage,
                                      style: TextStyle(color: Colors.grey)),
                                ],
                              ),
                            ),
                    ),
                    const SizedBox(height: 20),

                    // أزرار اختيار وحذف وتحميل صورة الهوية
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // زر اختيار صورة
                        ElevatedButton.icon(
                          icon: const Icon(Icons.photo_library,
                              color: Colors.white),
                          label: const Text(SenderInfoStrings.selectImage,
                              style: TextStyle(color: Colors.white)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                          onPressed: () async {
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles(
                              type: FileType.image,
                              allowMultiple: false,
                            );

                            if (result != null && result.files.isNotEmpty) {
                              setState(() {
                                selectedImagePath = result.files.first.path!;
                                imageFile = File(selectedImagePath);
                              });
                            }
                          },
                        ),

                        const SizedBox(width: 10),

                        // زر حذف الصورة
                        ElevatedButton.icon(
                          icon: const Icon(Icons.delete, color: Colors.white),
                          label: const Text(SenderInfoStrings.deleteImage,
                              style: TextStyle(color: Colors.white)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                          onPressed: imageFile == null
                              ? null
                              : () {
                                  setState(() {
                                    selectedImagePath = '';
                                    imageFile = null;
                                  });
                                },
                        ),

                        const SizedBox(width: 10),

                        // زر تحميل الصورة
                        ElevatedButton.icon(
                          icon: const Icon(Icons.download, color: Colors.white),
                          label: const Text(SenderInfoStrings.saveToDevice,
                              style: TextStyle(color: Colors.white)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                          onPressed: imageFile == null
                              ? null
                              : () async {
                                  try {
                                    // الحصول على امتداد الملف الأصلي
                                    String fileExtension =
                                        path.extension(imageFile!.path);
                                    if (fileExtension.isEmpty) {
                                      fileExtension = '.jpg'; // امتداد افتراضي
                                    }

                                    // إنشاء اسم ملف مقترح
                                    String suggestedName =
                                        'sender_id$fileExtension';

                                    // السماح للمستخدم باختيار مكان الحفظ
                                    String? savePath =
                                        await FilePicker.platform.saveFile(
                                      dialogTitle:
                                          'Choose location to save ID image',
                                      fileName: suggestedName,
                                      type: FileType.image,
                                      allowedExtensions: [
                                        'jpg',
                                        'jpeg',
                                        'png',
                                        'gif',
                                        'bmp'
                                      ],
                                    );

                                    if (savePath != null) {
                                      // نسخ الملف إلى المكان المختار
                                      await imageFile!.copy(savePath);

                                      // إظهار رسالة نجاح
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'Image saved successfully to: $savePath'),
                                            backgroundColor: Colors.green,
                                            duration:
                                                const Duration(seconds: 3),
                                          ),
                                        );
                                      }
                                    }
                                  } catch (e) {
                                    // إظهار رسالة خطأ
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content:
                                              Text('Error saving image: $e'),
                                          backgroundColor: Colors.red,
                                          duration: const Duration(seconds: 3),
                                        ),
                                      );
                                    }
                                  }
                                },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // أزرار الإلغاء والحفظ
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text(SenderInfoStrings.cancel),
                ),
                const SizedBox(width: 10),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    // حفظ البيانات
                    widget.onSave(selectedIdType, selectedImagePath);
                    Navigator.pop(context);
                  },
                  child: const Text(SenderInfoStrings.save),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
