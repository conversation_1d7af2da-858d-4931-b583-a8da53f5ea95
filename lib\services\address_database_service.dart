import 'package:flutter/foundation.dart';
import '../models/germany_address.dart';
import '../models/netherlands_address.dart';
import 'database_helper.dart';

typedef DatabaseProgressCallback = void Function(
    double progress, int processedItems, int totalItems);

class AddressDatabaseService {
  final DatabaseHelper _dbHelper;

  AddressDatabaseService(this._dbHelper);

  // إنشاء جدول عناوين ألمانيا
  Future<void> createGermanyAddressTable() async {
    try {
      final db = await _dbHelper.database;
      await db.execute('''
        CREATE TABLE IF NOT EXISTS germany_addresses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          postal_code TEXT NOT NULL,
          city_name TEXT NOT NULL
        )
      ''');
    } catch (e) {
      debugPrint('خطأ في إنشاء جدول عناوين ألمانيا: $e');
      rethrow;
    }
  }

  // إنشاء جدول عناوين هولندا
  Future<void> createNetherlandsAddressTable() async {
    try {
      final db = await _dbHelper.database;
      await db.execute('''
        CREATE TABLE IF NOT EXISTS netherlands_addresses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          postal_code TEXT NOT NULL,
          postal_code_numbers TEXT NOT NULL,
          postal_code_letters TEXT NOT NULL,
          street TEXT NOT NULL,
          min_nummer TEXT NOT NULL,
          max_nummer TEXT NOT NULL,
          city_name TEXT NOT NULL
        )
      ''');
    } catch (e) {
      debugPrint('خطأ في إنشاء جدول عناوين هولندا: $e');
      rethrow;
    }
  }

  // التحقق من وجود عنوان ألمانيا في قاعدة البيانات
  Future<bool> isGermanyAddressExists(GermanyAddress address) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.query(
        'germany_addresses',
        where: 'postal_code = ?',
        whereArgs: [address.postalCode],
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود عنوان ألمانيا: $e');
      return false;
    }
  }

  // التحقق من وجود عنوان هولندا في قاعدة البيانات
  Future<bool> isNetherlandsAddressExists(NetherlandsAddress address) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.query(
        'netherlands_addresses',
        where: 'postal_code = ? AND street = ?',
        whereArgs: [address.postalCode, address.street],
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود عنوان هولندا: $e');
      return false;
    }
  }

  // حفظ عناوين ألمانيا بكفاءة عالية مع تجنب العناصر المكررة
  Future<Map<String, dynamic>> saveGermanyAddresses(
    List<GermanyAddress> addresses, {
    DatabaseProgressCallback? onProgress,
    bool skipExisting = true,
  }) async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createGermanyAddressTable();

      // إحصائيات للإرجاع
      final stats = {
        'totalImported': addresses.length,
        'newItems': 0,
        'skippedItems': 0,
      };

      if (addresses.isEmpty) {
        return {
          'success': true,
          'stats': stats,
        };
      }

      // زيادة حجم الدفعة للملفات الكبيرة
      const batchSize = 5000;
      final totalItems = addresses.length;

      // تقليل عدد مرات تحديث واجهة المستخدم
      const uiUpdateFrequency = 10000;

      // بدء المعاملة
      await db.transaction((txn) async {
        // إنشاء فهرس مؤقت للرموز البريدية الموجودة
        Set<String> existingPostalCodes = {};

        if (skipExisting) {
          // استرجاع الرموز البريدية الموجودة فقط بدلاً من استرجاع جميع العناوين
          final List<Map<String, dynamic>> postalCodes = await txn.query(
            'germany_addresses',
            columns: ['postal_code'],
            distinct: true,
          );

          existingPostalCodes =
              postalCodes.map((map) => map['postal_code'] as String).toSet();
        }

        int newItems = 0;
        int skippedItems = 0;
        int processedItems = 0;

        // معالجة العناوين على دفعات
        for (int i = 0; i < totalItems; i += batchSize) {
          final batch = txn.batch();
          final end = (i + batchSize < totalItems) ? i + batchSize : totalItems;
          final currentBatch = addresses.sublist(i, end);

          int batchNewItems = 0;

          for (var address in currentBatch) {
            // التحقق من وجود العنوان
            if (skipExisting &&
                existingPostalCodes.contains(address.postalCode)) {
              skippedItems++;
              continue;
            }

            // إضافة العنوان إلى الدفعة
            batch.insert('germany_addresses', address.toMap());

            // إضافة الرمز البريدي إلى الفهرس المؤقت لتجنب إدخاله مرة أخرى
            existingPostalCodes.add(address.postalCode);

            batchNewItems++;
          }

          // تنفيذ الدفعة فقط إذا كانت تحتوي على عناصر جديدة
          if (batchNewItems > 0) {
            await batch.commit(noResult: true);
            newItems += batchNewItems;
          }

          processedItems += currentBatch.length;

          // تحديث التقدم بشكل أقل تكراراً
          if (onProgress != null &&
              (processedItems % uiUpdateFrequency == 0 ||
                  processedItems == totalItems)) {
            final progress = processedItems / totalItems;
            onProgress(progress, processedItems, totalItems);

            // إعطاء فرصة للنظام للتنفس
            await Future.delayed(const Duration(milliseconds: 1));
          }
        }

        // تحديث الإحصائيات
        stats['newItems'] = newItems;
        stats['skippedItems'] = skippedItems;
      });

      return {
        'success': true,
        'stats': stats,
      };
    } catch (e) {
      debugPrint('خطأ في حفظ عناوين ألمانيا: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // حفظ عناوين هولندا بكفاءة عالية مع تجنب العناصر المكررة
  Future<Map<String, dynamic>> saveNetherlandsAddresses(
    List<NetherlandsAddress> addresses, {
    DatabaseProgressCallback? onProgress,
    bool skipExisting = true,
  }) async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createNetherlandsAddressTable();

      // إحصائيات للإرجاع
      final stats = {
        'totalImported': addresses.length,
        'newItems': 0,
        'skippedItems': 0,
      };

      if (addresses.isEmpty) {
        return {
          'success': true,
          'stats': stats,
        };
      }

      // زيادة حجم الدفعة للملفات الكبيرة
      const batchSize = 5000;
      final totalItems = addresses.length;

      // تقليل عدد مرات تحديث واجهة المستخدم
      const uiUpdateFrequency = 10000;

      // بدء المعاملة
      await db.transaction((txn) async {
        // إنشاء فهرس مؤقت للعناوين الموجودة
        Set<String> existingKeys = {};

        if (skipExisting) {
          // استرجاع مفاتيح العناوين الموجودة فقط بدلاً من استرجاع جميع العناوين
          final List<Map<String, dynamic>> existingAddresses = await txn.query(
            'netherlands_addresses',
            columns: ['postal_code', 'street'],
            distinct: true,
          );

          existingKeys = existingAddresses
              .map((map) => '${map['postal_code']}|${map['street']}')
              .toSet();
        }

        int newItems = 0;
        int skippedItems = 0;
        int processedItems = 0;

        // معالجة العناوين على دفعات
        for (int i = 0; i < totalItems; i += batchSize) {
          final batch = txn.batch();
          final end = (i + batchSize < totalItems) ? i + batchSize : totalItems;
          final currentBatch = addresses.sublist(i, end);

          int batchNewItems = 0;

          for (var address in currentBatch) {
            // التحقق من وجود العنوان
            final key = '${address.postalCode}|${address.street}';
            if (skipExisting && existingKeys.contains(key)) {
              skippedItems++;
              continue;
            }

            // إضافة العنوان إلى الدفعة
            batch.insert('netherlands_addresses', address.toMap());

            // إضافة المفتاح إلى الفهرس المؤقت لتجنب إدخاله مرة أخرى
            existingKeys.add(key);

            batchNewItems++;
          }

          // تنفيذ الدفعة فقط إذا كانت تحتوي على عناصر جديدة
          if (batchNewItems > 0) {
            await batch.commit(noResult: true);
            newItems += batchNewItems;
          }

          processedItems += currentBatch.length;

          // تحديث التقدم بشكل أقل تكراراً
          if (onProgress != null &&
              (processedItems % uiUpdateFrequency == 0 ||
                  processedItems == totalItems)) {
            final progress = processedItems / totalItems;
            onProgress(progress, processedItems, totalItems);

            // إعطاء فرصة للنظام للتنفس
            await Future.delayed(const Duration(milliseconds: 1));
          }
        }

        // تحديث الإحصائيات
        stats['newItems'] = newItems;
        stats['skippedItems'] = skippedItems;
      });

      return {
        'success': true,
        'stats': stats,
      };
    } catch (e) {
      debugPrint('خطأ في حفظ عناوين هولندا: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // استرجاع عناوين ألمانيا
  Future<List<GermanyAddress>> getGermanyAddresses() async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createGermanyAddressTable();

      // استرجاع البيانات
      final List<Map<String, dynamic>> maps =
          await db.query('germany_addresses');

      // تحويل البيانات إلى كائنات
      return List.generate(maps.length, (i) {
        return GermanyAddress.fromMap(maps[i]);
      });
    } catch (e) {
      debugPrint('خطأ في استرجاع عناوين ألمانيا: $e');
      return [];
    }
  }

  // البحث عن عنوان ألمانيا بناءً على الرمز البريدي
  Future<GermanyAddress?> findGermanyAddressByPostalCode(
      String postalCode) async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createGermanyAddressTable();

      // البحث عن العنوان بناءً على الرمز البريدي
      final List<Map<String, dynamic>> maps = await db.query(
        'germany_addresses',
        where: 'postal_code = ?',
        whereArgs: [postalCode],
        limit: 1, // نحتاج فقط إلى نتيجة واحدة
      );

      // إذا لم يتم العثور على نتائج، نعيد null
      if (maps.isEmpty) {
        return null;
      }

      // تحويل البيانات إلى كائن
      return GermanyAddress.fromMap(maps.first);
    } catch (e) {
      debugPrint('خطأ في البحث عن عنوان ألمانيا بناءً على الرمز البريدي: $e');
      return null;
    }
  }

  // استرجاع عناوين هولندا
  Future<List<NetherlandsAddress>> getNetherlandsAddresses() async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createNetherlandsAddressTable();

      // استرجاع البيانات
      final List<Map<String, dynamic>> maps =
          await db.query('netherlands_addresses');

      // تحويل البيانات إلى كائنات
      return List.generate(maps.length, (i) {
        return NetherlandsAddress.fromMap(maps[i]);
      });
    } catch (e) {
      debugPrint('خطأ في استرجاع عناوين هولندا: $e');
      return [];
    }
  }

  // البحث عن عنوان هولندا بناءً على الرمز البريدي
  Future<List<NetherlandsAddress>> findNetherlandsAddressesByPostalCode(
      String postalCode) async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createNetherlandsAddressTable();

      // البحث عن العناوين بناءً على الرمز البريدي
      final List<Map<String, dynamic>> maps = await db.query(
        'netherlands_addresses',
        where: 'postal_code = ?',
        whereArgs: [postalCode],
      );

      // إذا لم يتم العثور على نتائج، نعيد قائمة فارغة
      if (maps.isEmpty) {
        return [];
      }

      // تحويل البيانات إلى كائنات
      return List.generate(maps.length, (i) {
        return NetherlandsAddress.fromMap(maps[i]);
      });
    } catch (e) {
      debugPrint('خطأ في البحث عن عنوان هولندا بناءً على الرمز البريدي: $e');
      return [];
    }
  }

  // حذف جميع عناوين ألمانيا
  Future<bool> clearAllGermanyAddresses() async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createGermanyAddressTable();

      // حذف جميع البيانات
      await db.delete('germany_addresses');

      debugPrint('تم حذف جميع عناوين ألمانيا بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف جميع عناوين ألمانيا: $e');
      return false;
    }
  }

  // حذف جميع عناوين هولندا
  Future<bool> clearAllNetherlandsAddresses() async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createNetherlandsAddressTable();

      // حذف جميع البيانات
      await db.delete('netherlands_addresses');

      debugPrint('تم حذف جميع عناوين هولندا بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف جميع عناوين هولندا: $e');
      return false;
    }
  }

  // الحصول على عدد عناوين ألمانيا
  Future<int> getGermanyAddressesCount() async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createGermanyAddressTable();

      final result =
          await db.rawQuery('SELECT COUNT(*) as count FROM germany_addresses');
      return result.first['count'] as int;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد عناوين ألمانيا: $e');
      return 0;
    }
  }

  // الحصول على عدد عناوين هولندا
  Future<int> getNetherlandsAddressesCount() async {
    try {
      final db = await _dbHelper.database;

      // التأكد من وجود الجدول
      await createNetherlandsAddressTable();

      final result = await db
          .rawQuery('SELECT COUNT(*) as count FROM netherlands_addresses');
      return result.first['count'] as int;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد عناوين هولندا: $e');
      return 0;
    }
  }
}
