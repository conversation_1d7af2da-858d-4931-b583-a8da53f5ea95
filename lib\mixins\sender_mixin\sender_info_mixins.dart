import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logging/logging.dart';
import '../../models/item_data.dart';
import '../../utils/constants.dart';
import '../../utils/ui_helper.dart';
import '../../utils/file_helper.dart';
import '../../services/database_helper.dart';
import '../../services/goods_data_service.dart';
import '../../widgets/sender_info/id_dialog.dart';
import '../../widgets/sender_info/phone_search_dialog.dart';
import '../../widgets/sender_info/goods_dialog.dart';

/// مزيج يحتوي على متغيرات وطرق إدارة البيانات لمكون معلومات المرسل
mixin SenderInfoDataMixin<T extends StatefulWidget> on State<T> {
  // إنشاء مسجل للأحداث
  final Logger _logger = Logger('SenderInfoDataMixin');

  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _senderNameController = TextEditingController();
  final TextEditingController _senderPhoneController = TextEditingController();
  final TextEditingController _senderIdController = TextEditingController();
  final TextEditingController _goodsDescriptionController =
      TextEditingController();

  // متغيرات لتخزين نوع الهوية ومسار صورة الهوية
  String _senderIdType = '';
  String _senderIdImagePath = '';

  // قائمة العناصر المحددة
  final List<ItemData> _selectedItems = [];

  // قائمة جميع العناصر المتاحة
  final List<ItemData> _allItems = [];

  @override
  void initState() {
    super.initState();
    _loadGoodsData();
  }

  @override
  void dispose() {
    _senderNameController.dispose();
    _senderPhoneController.dispose();
    _senderIdController.dispose();
    _goodsDescriptionController.dispose();
    super.dispose();
  }

  // دوال الحصول على البيانات
  String getSenderName() => _senderNameController.text;
  String getSenderPhone() => _senderPhoneController.text;
  String getSenderId() => _senderIdController.text;
  String getSenderIdType() => _senderIdType;
  String getSenderIdImagePath() => _senderIdImagePath;
  String getGoodsDescription() => _goodsDescriptionController.text;
  List<ItemData> getSelectedItems() => _selectedItems;
  List<ItemData> getAllItems() => _allItems;

  // دوال تعيين البيانات
  void setSenderName(String value) => _senderNameController.text = value;
  void setSenderPhone(String value) => _senderPhoneController.text = value;
  void setSenderId(String value) {
    _senderIdController.text = value;
    // تسجيل عملية التفريغ
    if (value.isEmpty) {
      _logger.info('تم تفريغ قيمة حقل sender id');
    }
  }

  void setSenderIdType(String value) => setState(() => _senderIdType = value);
  void setSenderIdImagePath(String value) =>
      setState(() => _senderIdImagePath = value);
  void setGoodsDescription(String value) =>
      _goodsDescriptionController.text = value;

  // دالة لمسح جميع العناصر المحددة
  void clearSelectedItems() {
    setState(() {
      _selectedItems.clear();
      _goodsDescriptionController.text = '';
    });
  }

  // دالة لإعادة تعيين الحقول
  void resetFields() {
    setState(() {
      _senderNameController.clear();
      _senderPhoneController.clear();
      _senderIdController.clear(); // تفريغ قيمة حقل sender id
      _goodsDescriptionController.clear();
      _senderIdType = '';
      _senderIdImagePath = '';
      _selectedItems.clear();

      // إعادة تعيين حالة الاختيار لجميع العناصر
      for (var item in _allItems) {
        item.isSelected = false;
        item.quantity = 1;
        item.weight = 0.0;
      }
    });
  }

  // تحميل بيانات البضائع من قاعدة البيانات
  Future<void> _loadGoodsData() async {
    try {
      final goodsDataService = GoodsDataService();
      final goodsItems = await goodsDataService.getAllGoodsAsItems();

      setState(() {
        _allItems.clear();
        _allItems.addAll(goodsItems);
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات البضائع: $e');
    }
  }
}

/// مزيج يحتوي على دوال بناء واجهة المستخدم لمكون معلومات المرسل
mixin SenderInfoUiMixin<T extends StatefulWidget> on State<T> {
  // الحصول على المتحكمات النصية
  TextEditingController get _senderNameController;
  TextEditingController get _senderPhoneController;
  TextEditingController get _senderIdController;
  TextEditingController get _goodsDescriptionController;

  // الحصول على متغيرات الحالة
  String get _senderIdImagePath;

  // الحصول على دوال الأحداث
  void _showIdTypeDialog();
  void _showPhoneSearchDialog();
  void _showGoodsDescriptionDialog();

  // بناء واجهة المستخدم الرئيسية
  Widget buildSenderInfoUI() {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              SenderInfoStrings.senderInformation,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            // حقل اسم المرسل
            _buildSenderNameField(),
            const SizedBox(height: 8),

            // حقل هاتف المرسل
            _buildSenderPhoneField(),
            const SizedBox(height: 8),

            // حقل هوية المرسل مع زر لاختيار نوع الهوية وصورة الهوية
            _buildSenderIdField(),
            const SizedBox(height: 8),

            // حقل وصف البضائع
            _buildGoodsDescriptionField(),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  // بناء حقل اسم المرسل
  Widget _buildSenderNameField() {
    return SizedBox(
      height: 55, // زيادة ارتفاع الحقل لراحة أكبر
      child: TextField(
        controller: _senderNameController,
        decoration: const InputDecoration(
          labelText: SenderInfoStrings.senderName,
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.bold, // جعل النص غامقًا
        ),
        // تم إلغاء التحديد التلقائي للنص
        // إضافة ميزة تحويل النص إلى أحرف كبيرة عند التغيير
        onChanged: (value) =>
            UiHelper.convertToUpperCase(_senderNameController),
      ),
    );
  }

  // بناء حقل هاتف المرسل
  Widget _buildSenderPhoneField() {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 55, // زيادة ارتفاع الحقل لراحة أكبر
            child: TextField(
              controller: _senderPhoneController,
              decoration: const InputDecoration(
                labelText: SenderInfoStrings.senderPhone,
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                hintText:
                    SenderInfoStrings.enterDigits, // إضافة تلميح داخل الحقل
              ),
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold, // جعل النص غامقًا
                color: _senderPhoneController.text.length < 11
                    ? Colors.red
                    : null, // تغيير لون النص إلى أحمر إذا كان عدد الأرقام أقل من 11
              ),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(
                    11), // تحديد الطول إلى 11 رقمًا فقط
              ],
              // تم إلغاء التحديد التلقائي للنص
              // تغيير لون النص بناءً على عدد الأرقام
              onChanged: (value) => setState(() {}),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showPhoneSearchDialog,
          tooltip: 'Search by Phone Number',
        ),
      ],
    );
  }

  // بناء حقل هوية المرسل
  Widget _buildSenderIdField() {
    return SizedBox(
      height: 55, // زيادة ارتفاع الحقل لراحة أكبر
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _senderIdController,
              decoration: const InputDecoration(
                labelText: SenderInfoStrings.senderId,
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              ),
              // تم إلغاء التحديد التلقائي للنص
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.more_vert,
              // تغيير لون الأيقونة بناءً على وجود صورة الهوية
              color: _senderIdImagePath.isNotEmpty ? Colors.green : Colors.red,
            ),
            onPressed: _showIdTypeDialog,
            tooltip: 'Select ID Type and Image',
          ),
        ],
      ),
    );
  }

  // بناء حقل وصف البضائع
  Widget _buildGoodsDescriptionField() {
    return SizedBox(
      height:
          65, // زيادة ارتفاع الحقل لراحة أكبر (أعلى من الحقول الأخرى لأنه متعدد الأسطر)
      child: TextField(
        controller: _goodsDescriptionController,
        decoration: const InputDecoration(
          labelText: SenderInfoStrings.goodsDescription,
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        ),
        maxLines: 2,
        // عند النقر على الحقل، عرض نافذة حوار مباشرة
        onTap: _showGoodsDescriptionDialog,
        readOnly: true, // جعل الحقل للقراءة فقط لمنع ظهور لوحة المفاتيح
      ),
    );
  }
}

/// مزيج يحتوي على دوال التعامل مع نوافذ الحوار لمكون معلومات المرسل
mixin SenderInfoDialogsMixin<T extends StatefulWidget> on State<T> {
  // إنشاء مسجل للأحداث
  final Logger _logger = Logger('SenderInfoDialogsMixin');

  // الحصول على المتحكمات النصية
  TextEditingController get _senderPhoneController;

  // الحصول على المتغيرات والدوال من المزيجات الأخرى
  String get _senderIdType;
  String get _senderIdImagePath;
  List<ItemData> get _allItems;
  List<ItemData> get _selectedItems;

  // دوال تعيين البيانات
  void setSenderIdType(String value);
  void setSenderIdImagePath(String value);

  // عرض نافذة حوار نوع الهوية وصورة الهوية
  @protected
  // ignore: unused_element
  void _showIdTypeDialog() {
    // الحصول على رمز السجل الحالي
    String currentCode = '';
    try {
      final homeScreenState = context.findAncestorStateOfType<State>();
      if (homeScreenState != null) {
        final basicInfoState =
            (homeScreenState as dynamic).basicInfoKey.currentState;
        if (basicInfoState != null) {
          currentCode = (basicInfoState as dynamic).getCodeNo();
        }
      }
    } catch (e) {
      _logger.severe('خطأ في الحصول على رمز السجل الحالي: $e');
    }

    showDialog(
      context: context,
      builder: (context) => IdDialog(
        initialIdType: _senderIdType,
        initialImagePath: _senderIdImagePath,
        currentCode: currentCode,
        onSave: (idType, imagePath) {
          setSenderIdType(idType);
          setSenderIdImagePath(imagePath);
        },
      ),
    );
  }

  // دالة لحفظ الصورة على جهاز المستخدم

  // عرض نافذة حوار البحث عن رقم الهاتف
  @protected
  // ignore: unused_element
  void _showPhoneSearchDialog() async {
    final String phoneNumber = _senderPhoneController.text;
    if (phoneNumber.isEmpty) {
      if (!mounted) return;
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.enterPhoneFirst,
        isError: true,
      );
      return;
    }

    // استخدام UiHelper لعرض نافذة حوار التحميل وإجراء البحث
    try {
      final results = await UiHelper.showLoadingDialog(
        context: context,
        loadingText: SenderInfoStrings.searching,
        asyncFunction: () async {
          final databaseHelper = DatabaseHelper();
          return await databaseHelper.searchCodeDataByColumn(
              'sender_phone', phoneNumber);
        },
      );

      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض نتائج البحث
      showDialog(
        context: context,
        builder: (context) => PhoneSearchDialog(
          phoneNumber: phoneNumber,
          results: results,
        ),
      );
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في البحث عن رقم الهاتف: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorSearchingPhone} $e',
        isError: true,
      );
    }
  }

  // عرض نافذة حوار وصف البضائع
  @protected
  // ignore: unused_element
  void _showGoodsDescriptionDialog() {
    showDialog(
      context: context,
      useSafeArea: false,
      barrierDismissible: true,
      builder: (context) => GoodsDialog(
        allItems: _allItems,
        selectedItems: _selectedItems,
        onSave: _onSaveGoodsDialog,
        onImportFromExcel: _importFromExcel,
        onExportToExcel: _exportToExcel,
      ),
    );
  }

  // دالة معالجة حفظ نافذة حوار البضائع
  void _onSaveGoodsDialog(List<ItemData> allItems, List<ItemData> selectedItems,
      String goodsDescription);

  // دالة تصدير البيانات إلى ملف Excel
  void _exportToExcel(BuildContext dialogContext, List<ItemData> items);

  // دالة استيراد البيانات من ملف Excel
  void _importFromExcel(
      BuildContext dialogContext, StateSetter setState, List<ItemData> items);
}

/// مزيج يحتوي على دوال التعامل مع ملفات Excel لمكون معلومات المرسل
mixin SenderInfoExcelMixin<T extends StatefulWidget> on State<T> {
  // إنشاء مسجل للأحداث
  final Logger _logger = Logger('SenderInfoExcelMixin');

  // دالة تصدير البيانات إلى ملف Excel
  @protected
  // ignore: unused_element
  void _exportToExcel(BuildContext dialogContext, List<ItemData> items) async {
    try {
      _logger.info('بدء تصدير البيانات إلى ملف Excel');

      // التحقق من وجود عناصر للتصدير
      if (items.isEmpty) {
        _logger.warning('لا توجد عناصر للتصدير');
        if (!mounted) return;

        UiHelper.showSnackBar(
          context,
          'لا توجد عناصر للتصدير',
          isError: true,
        );
        return;
      }

      // التحقق من الأذونات
      if (await FileHelper.checkStoragePermissions(dialogContext)) {
        // تحضير البيانات للتصدير
        final headers = [SenderInfoStrings.itemArKu, SenderInfoStrings.itemEn];
        final data = items.map((item) => [item.nameAr, item.nameEn]).toList();

        _logger.info('تم تحضير ${data.length} عنصر للتصدير');

        // طباعة البيانات للتحقق
        for (var i = 0; i < data.length; i++) {
          _logger.info('العنصر ${i + 1}: ${data[i].join(', ')}');
        }

        // تصدير البيانات
        final filePath = await FileHelper.exportToExcel(
          sheetName: 'Items',
          headers: headers,
          data: data,
          fileName: 'Goods List.xlsx',
        );

        // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
        if (!mounted) return;

        // عرض رسالة نجاح
        if (filePath != null) {
          _logger.info('تم تصدير البيانات بنجاح إلى: $filePath');

          // إغلاق نافذة الحوار إذا كانت مفتوحة
          if (dialogContext.mounted && Navigator.canPop(dialogContext)) {
            Navigator.pop(dialogContext);
          }

          // عرض رسالة نجاح للمستخدم
          // التحقق مرة أخرى من أن الـ widget لا يزال في شجرة واجهة المستخدم بعد العمليات غير المتزامنة
          if (context.mounted) {
            UiHelper.showInfoDialog(
              context,
              title: 'Export Successful',
              content:
                  '${SenderInfoStrings.exportSuccess}\nتم تصدير ${data.length} عنصر\nتم حفظ الملف في:\n$filePath',
              buttonText: 'موافق',
            );
          }
        } else {
          _logger.warning(
              'لم يتم تصدير البيانات، ربما تم إلغاء العملية من قبل المستخدم');
        }
      } else {
        _logger.warning('لم يتم منح الأذونات اللازمة للتصدير');
      }
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في تصدير البيانات: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorExportingData} $e',
        isError: true,
      );
    }
  }

  // دالة استيراد البيانات من ملف Excel
  @protected
  // ignore: unused_element
  void _importFromExcel(BuildContext dialogContext, StateSetter setState,
      List<ItemData> items) async {
    try {
      // استيراد البيانات
      final result = await FileHelper.importFromExcel();

      if (result['success'] == true) {
        final excel = result['excel'];

        // التحقق من وجود أوراق
        if (excel.tables.isEmpty) {
          if (!mounted) return;
          UiHelper.showSnackBar(
            context,
            SenderInfoStrings.fileHasNoData,
            isError: true,
          );
          return;
        }

        // البحث في جميع الأوراق عن الأعمدة المطلوبة
        final sheetInfo = _findColumnsInExcel(excel);
        final selectedSheetName = sheetInfo['sheetName'];
        final arColumnIndex = sheetInfo['arColumnIndex'];
        final enColumnIndex = sheetInfo['enColumnIndex'];

        if (selectedSheetName == null) {
          if (!mounted) return;
          UiHelper.showSnackBar(
            context,
            'الأعمدة المطلوبة غير موجودة في أي ورقة',
            isError: true,
          );
          return;
        }

        // استيراد البيانات من الورقة المحددة
        final importResult = _importItemsFromSheet(
          excel.tables[selectedSheetName]!,
          arColumnIndex,
          enColumnIndex,
          items,
          setState,
        );

        // عرض رسالة النتيجة
        if (!mounted) return;
        _showImportResult(importResult, selectedSheetName);
      }
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في استيراد البيانات: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorImportingData} $e',
        isError: true,
      );
    }
  }

  // البحث عن الأعمدة المطلوبة في ملف Excel
  Map<String, dynamic> _findColumnsInExcel(dynamic excel) {
    String? selectedSheetName;
    int arColumnIndex = -1;
    int enColumnIndex = -1;

    // التحقق من كل ورقة للعثور على الأعمدة المطلوبة
    for (var sheetName in excel.tables.keys) {
      final table = excel.tables[sheetName]!;

      // التحقق من وجود صفوف
      if (table.rows.isEmpty) continue;

      // البحث عن الترويسات المطلوبة في الصف الأول
      int tempArColumnIndex = -1;
      int tempEnColumnIndex = -1;

      if (table.rows.isNotEmpty && table.rows[0].isNotEmpty) {
        for (var i = 0; i < table.rows[0].length; i++) {
          final headerCell = table.rows[0][i]?.value?.toString() ?? '';

          if (headerCell.toLowerCase().contains('ar') ||
              headerCell.contains('عربي') ||
              headerCell.contains('كردي')) {
            tempArColumnIndex = i;
          } else if (headerCell.toLowerCase().contains('en') ||
              headerCell.toLowerCase().contains('eng') ||
              headerCell.contains('انجليزي')) {
            tempEnColumnIndex = i;
          }
        }
      }

      // إذا وجدنا الأعمدة المطلوبة في هذه الورقة، استخدمها
      if (tempArColumnIndex != -1 && tempEnColumnIndex != -1) {
        selectedSheetName = sheetName;
        arColumnIndex = tempArColumnIndex;
        enColumnIndex = tempEnColumnIndex;
        break;
      }
    }

    // إذا لم نجد الأعمدة المطلوبة في أي ورقة، استخدم الورقة الأولى وافترض أن العمودين الأول والثاني هما المطلوبان
    if (selectedSheetName == null && excel.tables.isNotEmpty) {
      selectedSheetName = excel.tables.keys.first;
      arColumnIndex = 0;
      enColumnIndex = 1;
    }

    return {
      'sheetName': selectedSheetName,
      'arColumnIndex': arColumnIndex,
      'enColumnIndex': enColumnIndex,
    };
  }

  // استيراد العناصر من ورقة Excel
  Map<String, dynamic> _importItemsFromSheet(
    dynamic table,
    int arColumnIndex,
    int enColumnIndex,
    List<ItemData> items,
    StateSetter setState,
  ) {
    int importedCount = 0;
    int duplicateCount = 0;

    // تخطي الصف الأول (الترويسات) والبدء من الصف الثاني
    for (var i = 1; i < table.rows.length; i++) {
      final row = table.rows[i];

      // التحقق من وجود بيانات في الصف
      if (row.isNotEmpty &&
          row.length > arColumnIndex &&
          row.length > enColumnIndex) {
        // التحقق من وجود قيم في الخلايا
        final cellAr = row[arColumnIndex];
        final cellEn = row[enColumnIndex];

        if (cellAr != null && cellEn != null) {
          final nameAr = cellAr.value?.toString() ?? '';
          final nameEn = cellEn.value?.toString() ?? '';

          if (nameAr.isNotEmpty && nameEn.isNotEmpty) {
            // التحقق مما إذا كان العنصر موجودًا بالفعل في القائمة
            bool isDuplicate = items.any((item) =>
                item.nameAr.toLowerCase() == nameAr.toLowerCase() ||
                item.nameEn.toLowerCase() == nameEn.toLowerCase());

            if (!isDuplicate) {
              setState(() {
                final newId = items.isNotEmpty
                    ? items.map((e) => e.id).reduce((a, b) => a > b ? a : b) + 1
                    : 1;
                items.add(ItemData(
                  id: newId,
                  nameAr: nameAr,
                  nameEn: nameEn,
                  isSelected: false,
                  quantity: 1,
                  weight: 0.0,
                ));
              });
              importedCount++;
            } else {
              duplicateCount++;
            }
          }
        }
      }
    }

    return {
      'importedCount': importedCount,
      'duplicateCount': duplicateCount,
    };
  }

  // عرض نتيجة الاستيراد
  void _showImportResult(Map<String, dynamic> result, String sheetName) {
    final importedCount = result['importedCount'];
    final duplicateCount = result['duplicateCount'];

    if (!mounted) return;

    if (importedCount > 0) {
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.importSuccess.replaceAll('%d', '$importedCount').replaceAll('%s', sheetName)}'
        '${duplicateCount > 0 ? SenderInfoStrings.duplicatesIgnored.replaceAll('%d', '$duplicateCount') : ''}',
      );
    } else if (duplicateCount > 0) {
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.onlyDuplicatesFound
            .replaceAll('%d', '$duplicateCount')
            .replaceAll('%s', sheetName),
        isError: true,
      );
    } else {
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.noItemsImported.replaceAll('%s', sheetName),
        isError: true,
      );
    }
  }
}
