import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

/// Helper for UI operations
class UiHelper {
  static final Logger _logger = Logger('UiHelper');

  /// Show success or error message at bottom center
  static void showSnackBar(BuildContext context, String message,
      {bool isError = false, bool forceShow = false}) {
    // Log the message only
    if (isError) {
      _logger.warning('Error message: $message');
    } else {
      _logger.info('Message: $message');
    }

    // Show messages to the user at bottom center
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: isError ? Colors.red.shade600 : Colors.green.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isError ? Icons.error : Icons.check_circle,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Remove the overlay after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // Ignore error if already removed
      }
    });
  }

  /// Safely show success or error message (with mounted check) at bottom center
  static void safelyShowSnackBar(BuildContext? context, String message,
      {bool isError = false, bool forceShow = false}) {
    // Log the message only
    if (isError) {
      _logger.warning('Error message: $message');
    } else {
      _logger.info('Message: $message');
    }

    // Show messages to the user if context is valid
    if (context != null && context.mounted) {
      final overlay = Overlay.of(context);
      late OverlayEntry overlayEntry;

      overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          bottom: 100, // عرض في الأسفل
          left: 0,
          right: 0,
          child: Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                constraints: const BoxConstraints(
                  maxWidth: 400, // حد أقصى للعرض
                ),
                decoration: BoxDecoration(
                  color: isError ? Colors.red.shade600 : Colors.green.shade600,
                  borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isError ? Icons.error : Icons.check_circle,
                      color: Colors.white,
                      size: 22,
                    ),
                    const SizedBox(width: 12),
                    Flexible(
                      child: Text(
                        message,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      overlay.insert(overlayEntry);

      // Remove the overlay after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        try {
          overlayEntry.remove();
        } catch (e) {
          // Ignore error if already removed
        }
      });
    }
  }

  /// Show confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = 'Yes',
    String cancelText = 'No',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(confirmText),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show information dialog
  static Future<void> showInfoDialog(
    BuildContext context, {
    required String title,
    required String content,
    String buttonText = 'OK',
  }) async {
    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  static Future<void> showErrorDialog(
    BuildContext context, {
    required String title,
    required String content,
    String buttonText = 'OK',
  }) async {
    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }

  /// Show loading dialog
  static Future<T> showLoadingDialog<T>({
    required BuildContext context,
    required Future<T> Function() asyncFunction,
    String loadingText = 'Loading...',
  }) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(loadingText),
          ],
        ),
      ),
    );

    try {
      // Execute the async function
      final result = await asyncFunction();

      // Close the loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      return result;
    } catch (e) {
      // Close the loading indicator in case of error
      if (context.mounted) {
        Navigator.of(context).pop();

        // Show error message
        await showErrorDialog(
          context,
          title: 'Error',
          content: e.toString(),
        );
      }

      rethrow;
    }
  }

  /// Convert text to uppercase
  static void convertToUpperCase(TextEditingController controller) {
    final String text = controller.text;
    final String upperCaseText = text.toUpperCase();

    if (text != upperCaseText) {
      controller.value = controller.value.copyWith(
        text: upperCaseText,
        selection: TextSelection.collapsed(offset: upperCaseText.length),
        composing: TextRange.empty,
      );
    }
  }

  /// عرض رسالة تنبيهية في أسفل منتصف الشاشة
  static void showNotification(BuildContext context,
      {required String messageEn,
      bool isError = false,
      int durationSeconds = 3}) {
    // تسجيل الرسالة
    if (isError) {
      _logger.warning('Error message: $messageEn');
    } else {
      _logger.info('Message: $messageEn');
    }

    // عرض الرسالة للمستخدم في أسفل منتصف الشاشة
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: isError ? Colors.red.shade600 : Colors.green.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isError ? Icons.error : Icons.check_circle,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      messageEn,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد المدة المحددة
    Future.delayed(Duration(seconds: durationSeconds), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // Ignore error if already removed
      }
    });
  }
}
