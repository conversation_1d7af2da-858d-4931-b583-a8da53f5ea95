import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logging/logging.dart';
import '../../models/item_data.dart';
import '../../utils/constants.dart';
import '../../utils/ui_helper.dart';
import '../../utils/file_helper.dart';
import '../../services/database_helper.dart';
import '../../services/goods_data_service.dart';
import '../sender_info/id_dialog.dart';
import '../sender_info/phone_search_dialog.dart';
import '../sender_info/goods_dialog.dart';

/// واجهة إدخال معلومات المرسل
class SenderInfo extends StatefulWidget {
  const SenderInfo({super.key});

  @override
  State<SenderInfo> createState() => _SenderInfoState();
}

class _SenderInfoState extends State<SenderInfo> {
  // إنشاء مسجل لهذا الكلاس
  final Logger _logger = Logger('SenderInfo');

  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _senderNameController = TextEditingController();
  final TextEditingController _senderPhoneController = TextEditingController();
  final TextEditingController _senderIdController = TextEditingController();
  final TextEditingController _goodsDescriptionController =
      TextEditingController();

  // متغيرات لتخزين نوع الهوية ومسار صورة الهوية
  String _senderIdType = '';
  String _senderIdImagePath = '';

  // قائمة العناصر المحددة
  final List<ItemData> _selectedItems = [];

  // قائمة جميع العناصر المتاحة
  final List<ItemData> _allItems = [];

  @override
  void initState() {
    super.initState();
    _loadGoodsData();
  }

  @override
  void dispose() {
    _senderNameController.dispose();
    _senderPhoneController.dispose();
    _senderIdController.dispose();
    _goodsDescriptionController.dispose();
    super.dispose();
  }

  // دوال الحصول على البيانات
  String getSenderName() => _senderNameController.text;
  String getSenderPhone() => _senderPhoneController.text;
  String getSenderId() => _senderIdController.text;
  String getSenderIdType() => _senderIdType;
  String getSenderIdImagePath() => _senderIdImagePath;
  String getGoodsDescription() => _goodsDescriptionController.text;
  List<ItemData> getSelectedItems() => _selectedItems;

  // دوال تعيين البيانات
  void setSenderName(String value) => _senderNameController.text = value;
  void setSenderPhone(String value) => _senderPhoneController.text = value;
  void setSenderId(String value) {
    _senderIdController.text = value;
    if (value.isEmpty) {
      _logger.info('تم تفريغ قيمة حقل sender id');
    }
  }

  void setSenderIdType(String value) => setState(() => _senderIdType = value);
  void setSenderIdImagePath(String value) =>
      setState(() => _senderIdImagePath = value);
  void setGoodsDescription(String value) =>
      _goodsDescriptionController.text = value;

  // دالة لمسح جميع العناصر المحددة
  void clearSelectedItems() {
    setState(() {
      _selectedItems.clear();
      _goodsDescriptionController.text = '';
    });
  }

  // دالة لإعادة تعيين الحقول
  void resetFields() {
    setState(() {
      _senderNameController.clear();
      _senderPhoneController.clear();
      _senderIdController.clear();
      _goodsDescriptionController.clear();
      _senderIdType = '';
      _senderIdImagePath = '';
      _selectedItems.clear();

      // إعادة تعيين حالة الاختيار لجميع العناصر
      for (var item in _allItems) {
        item.isSelected = false;
        item.quantity = 1;
        item.weight = 0.0;
      }
    });
  }

  // دالة لمسح حقول المرسل المطلوبة بعد حذف السجل
  void clearSenderFields() {
    setState(() {
      _senderNameController.clear();
      _senderPhoneController.clear();
      _senderIdController.clear();
      _senderIdType = '';
      _senderIdImagePath = '';
      _goodsDescriptionController.clear();
      _selectedItems.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4.r,
      margin: EdgeInsets.all(4.r),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              SenderInfoStrings.senderInformation,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 12.h),

            // حقل اسم المرسل
            _buildSenderNameField(),
            SizedBox(height: 8.h),

            // حقل هاتف المرسل
            _buildSenderPhoneField(),
            SizedBox(height: 8.h),

            // حقل هوية المرسل مع زر لاختيار نوع الهوية وصورة الهوية
            _buildSenderIdField(),
            SizedBox(height: 8.h),

            // حقل وصف البضائع
            _buildGoodsDescriptionField(),
            SizedBox(height: 12.h),
          ],
        ),
      ),
    );
  }

  // بناء حقل اسم المرسل
  Widget _buildSenderNameField() {
    return SizedBox(
      height: 60.h, // زيادة ارتفاع الحقل لراحة أكبر
      child: TextField(
        controller: _senderNameController,
        decoration: InputDecoration(
          labelText: SenderInfoStrings.senderName,
          border: const OutlineInputBorder(),
          contentPadding:
              EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
          labelStyle: TextStyle(fontSize: 13.sp),
        ),
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.bold, // جعل النص غامقًا
        ),
        // إضافة ميزة تحويل النص إلى أحرف كبيرة عند التغيير
        onChanged: (value) =>
            UiHelper.convertToUpperCase(_senderNameController),
      ),
    );
  }

  // بناء حقل هاتف المرسل
  Widget _buildSenderPhoneField() {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 60.h, // زيادة ارتفاع الحقل لراحة أكبر
            child: TextField(
              controller: _senderPhoneController,
              decoration: InputDecoration(
                labelText: SenderInfoStrings.senderPhone,
                border: const OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
                hintText:
                    SenderInfoStrings.enterDigits, // إضافة تلميح داخل الحقل
                labelStyle: TextStyle(fontSize: 13.sp),
              ),
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold, // جعل النص غامقًا
                color: _senderPhoneController.text.length < 11
                    ? Colors.red
                    : null, // تغيير لون النص إلى أحمر إذا كان عدد الأرقام أقل من 11
              ),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(
                    11), // تحديد الطول إلى 11 رقمًا فقط
              ],
              // تغيير لون النص بناءً على عدد الأرقام
              onChanged: (value) => setState(() {}),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showPhoneSearchDialog,
          tooltip: 'Search by Phone Number',
        ),
      ],
    );
  }

  // بناء حقل هوية المرسل
  Widget _buildSenderIdField() {
    return SizedBox(
      height: 60.h, // زيادة ارتفاع الحقل لراحة أكبر
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _senderIdController,
              decoration: InputDecoration(
                labelText: SenderInfoStrings.senderId,
                border: const OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
                labelStyle: TextStyle(fontSize: 13.sp),
              ),
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold, // جعل النص غامقًا
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.credit_card,
              // تغيير لون الأيقونة بناءً على وجود صورة الهوية
              color: _senderIdImagePath.isNotEmpty ? Colors.green : Colors.red,
              // إضافة حجم أكبر للأيقونة لتكون أكثر وضوحاً
              size: 24.sp,
            ),
            onPressed: _showIdTypeDialog,
            tooltip: 'Select ID Type and Image',
            // إضافة تأثير عند الضغط على الزر
            splashRadius: 24,
          ),
        ],
      ),
    );
  }

  // بناء حقل وصف البضائع
  Widget _buildGoodsDescriptionField() {
    return SizedBox(
      height: 70
          .h, // زيادة ارتفاع الحقل لراحة أكبر (أعلى من الحقول الأخرى لأنه متعدد الأسطر)
      child: TextField(
        controller: _goodsDescriptionController,
        decoration: InputDecoration(
          labelText: SenderInfoStrings.goodsDescription,
          border: const OutlineInputBorder(),
          contentPadding:
              EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
          labelStyle: TextStyle(fontSize: 13.sp),
        ),
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 2,
        // عند النقر على الحقل، عرض نافذة حوار مباشرة
        onTap: _showGoodsDescriptionDialog,
        readOnly: true, // جعل الحقل للقراءة فقط لمنع ظهور لوحة المفاتيح
      ),
    );
  }

  // عرض نافذة حوار نوع الهوية وصورة الهوية
  void _showIdTypeDialog() {
    // الحصول على رمز السجل الحالي
    String currentCode = '';
    try {
      final homeScreenState = context.findAncestorStateOfType<State>();
      if (homeScreenState != null) {
        final basicInfoState =
            (homeScreenState as dynamic).basicInfoKey.currentState;
        if (basicInfoState != null) {
          currentCode = (basicInfoState as dynamic).getCodeNo();
        }
      }
    } catch (e) {
      _logger.severe('خطأ في الحصول على رمز السجل الحالي: $e');
    }

    showDialog(
      context: context,
      builder: (context) => IdDialog(
        initialIdType: _senderIdType,
        initialImagePath: _senderIdImagePath,
        currentCode: currentCode,
        onSave: (idType, imagePath) {
          setSenderIdType(idType);
          setSenderIdImagePath(imagePath);
        },
      ),
    );
  }

  // عرض نافذة حوار البحث عن رقم الهاتف
  void _showPhoneSearchDialog() async {
    final String phoneNumber = _senderPhoneController.text;
    if (phoneNumber.isEmpty) {
      if (!mounted) return;
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.enterPhoneFirst,
        isError: true,
      );
      return;
    }

    // استخدام UiHelper لعرض نافذة حوار التحميل وإجراء البحث
    try {
      final results = await UiHelper.showLoadingDialog(
        context: context,
        loadingText: SenderInfoStrings.searching,
        asyncFunction: () async {
          final databaseHelper = DatabaseHelper();
          return await databaseHelper.searchCodeDataByColumn(
              'sender_phone', phoneNumber);
        },
      );

      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض نتائج البحث
      showDialog(
        context: context,
        builder: (context) => PhoneSearchDialog(
          phoneNumber: phoneNumber,
          results: results,
        ),
      );
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في البحث عن رقم الهاتف: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorSearchingPhone} $e',
        isError: true,
      );
    }
  }

  // عرض نافذة حوار وصف البضائع
  void _showGoodsDescriptionDialog() {
    showDialog(
      context: context,
      useSafeArea: false,
      barrierDismissible: true,
      builder: (context) => GoodsDialog(
        allItems: _allItems,
        selectedItems: _selectedItems,
        onSave: _onSaveGoodsDialog,
        onImportFromExcel: _importFromExcel,
        onExportToExcel: _exportToExcel,
      ),
    );
  }

  // دالة معالجة حفظ نافذة حوار البضائع
  void _onSaveGoodsDialog(List<ItemData> allItems, List<ItemData> selectedItems,
      String goodsDescription) {
    setState(() {
      _allItems.clear();
      _allItems.addAll(allItems);

      // إذا كانت قائمة العناصر المحددة فارغة، نحتفظ بالعناصر المحددة الحالية
      if (selectedItems.isNotEmpty) {
        _selectedItems.clear();
        _selectedItems.addAll(selectedItems);
        _goodsDescriptionController.text = goodsDescription;
      }
    });
  }

  // دالة تصدير البيانات إلى ملف Excel
  void _exportToExcel(BuildContext dialogContext, List<ItemData> items) async {
    try {
      // التحقق من الأذونات
      if (await FileHelper.checkStoragePermissions(dialogContext)) {
        // تحضير البيانات للتصدير
        final headers = [SenderInfoStrings.itemArKu, SenderInfoStrings.itemEn];
        final data = items.map((item) => [item.nameAr, item.nameEn]).toList();

        // تصدير البيانات
        final filePath = await FileHelper.exportToExcel(
          sheetName: 'Items',
          headers: headers,
          data: data,
          fileName: 'Goods List.xlsx',
        );

        // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
        if (!mounted) return;

        // عرض رسالة نجاح
        if (filePath != null) {
          UiHelper.showSnackBar(
            context,
            SenderInfoStrings.exportSuccess,
          );
        }
      }
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في تصدير البيانات: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorExportingData} $e',
        isError: true,
      );
    }
  }

  // دالة استيراد البيانات من ملف Excel
  void _importFromExcel(BuildContext dialogContext, StateSetter setState,
      List<ItemData> items) async {
    try {
      // استيراد البيانات
      final result = await FileHelper.importFromExcel();

      if (result['success'] == true) {
        final excel = result['excel'];

        // التحقق من وجود أوراق
        if (excel.tables.isEmpty) {
          if (!mounted) return;
          UiHelper.showSnackBar(
            context,
            SenderInfoStrings.fileHasNoData,
            isError: true,
          );
          return;
        }

        // البحث في جميع الأوراق عن الأعمدة المطلوبة
        final sheetInfo = _findColumnsInExcel(excel);
        final selectedSheetName = sheetInfo['sheetName'];
        final arColumnIndex = sheetInfo['arColumnIndex'];
        final enColumnIndex = sheetInfo['enColumnIndex'];

        if (selectedSheetName == null) {
          if (!mounted) return;
          UiHelper.showSnackBar(
            context,
            'الأعمدة المطلوبة غير موجودة في أي ورقة',
            isError: true,
          );
          return;
        }

        // استيراد البيانات من الورقة المحددة
        final importResult = _importItemsFromSheet(
          excel.tables[selectedSheetName]!,
          arColumnIndex,
          enColumnIndex,
          items,
          setState,
        );

        // عرض رسالة النتيجة
        if (!mounted) return;
        _showImportResult(importResult, selectedSheetName);
      }
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      // عرض رسالة خطأ
      _logger.severe('خطأ في استيراد البيانات: $e');
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.errorImportingData} $e',
        isError: true,
      );
    }
  }

  // البحث عن الأعمدة المطلوبة في ملف Excel
  Map<String, dynamic> _findColumnsInExcel(dynamic excel) {
    String? selectedSheetName;
    int arColumnIndex = -1;
    int enColumnIndex = -1;

    // التحقق من كل ورقة للعثور على الأعمدة المطلوبة
    for (var sheetName in excel.tables.keys) {
      final table = excel.tables[sheetName]!;

      // التحقق من وجود صفوف
      if (table.rows.isEmpty) continue;

      // البحث عن الترويسات المطلوبة في الصف الأول
      int tempArColumnIndex = -1;
      int tempEnColumnIndex = -1;

      if (table.rows.isNotEmpty && table.rows[0].isNotEmpty) {
        for (var i = 0; i < table.rows[0].length; i++) {
          final headerCell = table.rows[0][i]?.value?.toString() ?? '';

          if (headerCell.toLowerCase().contains('ar') ||
              headerCell.contains('عربي') ||
              headerCell.contains('كردي')) {
            tempArColumnIndex = i;
          } else if (headerCell.toLowerCase().contains('en') ||
              headerCell.toLowerCase().contains('eng') ||
              headerCell.contains('انجليزي')) {
            tempEnColumnIndex = i;
          }
        }
      }

      // إذا وجدنا الأعمدة المطلوبة في هذه الورقة، استخدمها
      if (tempArColumnIndex != -1 && tempEnColumnIndex != -1) {
        selectedSheetName = sheetName;
        arColumnIndex = tempArColumnIndex;
        enColumnIndex = tempEnColumnIndex;
        break;
      }
    }

    // إذا لم نجد الأعمدة المطلوبة في أي ورقة، استخدم الورقة الأولى وافترض أن العمودين الأول والثاني هما المطلوبان
    if (selectedSheetName == null && excel.tables.isNotEmpty) {
      selectedSheetName = excel.tables.keys.first;
      arColumnIndex = 0;
      enColumnIndex = 1;
    }

    return {
      'sheetName': selectedSheetName,
      'arColumnIndex': arColumnIndex,
      'enColumnIndex': enColumnIndex,
    };
  }

  // استيراد العناصر من ورقة Excel
  Map<String, dynamic> _importItemsFromSheet(
    dynamic table,
    int arColumnIndex,
    int enColumnIndex,
    List<ItemData> items,
    StateSetter setState,
  ) {
    int importedCount = 0;
    int duplicateCount = 0;

    // تخطي الصف الأول (الترويسات) والبدء من الصف الثاني
    for (var i = 1; i < table.rows.length; i++) {
      final row = table.rows[i];

      // التحقق من وجود بيانات في الصف
      if (row.isNotEmpty &&
          row.length > arColumnIndex &&
          row.length > enColumnIndex) {
        // التحقق من وجود قيم في الخلايا
        final cellAr = row[arColumnIndex];
        final cellEn = row[enColumnIndex];

        if (cellAr != null && cellEn != null) {
          final nameAr = cellAr.value?.toString() ?? '';
          final nameEn = cellEn.value?.toString() ?? '';

          if (nameAr.isNotEmpty && nameEn.isNotEmpty) {
            // التحقق مما إذا كان العنصر موجودًا بالفعل في القائمة
            bool isDuplicate = items.any((item) =>
                item.nameAr.toLowerCase() == nameAr.toLowerCase() ||
                item.nameEn.toLowerCase() == nameEn.toLowerCase());

            if (!isDuplicate) {
              setState(() {
                final newId = items.isNotEmpty
                    ? items.map((e) => e.id).reduce((a, b) => a > b ? a : b) + 1
                    : 1;
                items.add(ItemData(
                  id: newId,
                  nameAr: nameAr,
                  nameEn: nameEn,
                  isSelected: false,
                  quantity: 1,
                  weight: 0.0,
                ));
              });
              importedCount++;
            } else {
              duplicateCount++;
            }
          }
        }
      }
    }

    return {
      'importedCount': importedCount,
      'duplicateCount': duplicateCount,
    };
  }

  // عرض نتيجة الاستيراد
  void _showImportResult(Map<String, dynamic> result, String sheetName) {
    final importedCount = result['importedCount'];
    final duplicateCount = result['duplicateCount'];

    if (!mounted) return;

    if (importedCount > 0) {
      UiHelper.showSnackBar(
        context,
        '${SenderInfoStrings.importSuccess.replaceAll('%d', '$importedCount').replaceAll('%s', sheetName)}'
        '${duplicateCount > 0 ? SenderInfoStrings.duplicatesIgnored.replaceAll('%d', '$duplicateCount') : ''}',
      );
    } else if (duplicateCount > 0) {
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.onlyDuplicatesFound
            .replaceAll('%d', '$duplicateCount')
            .replaceAll('%s', sheetName),
        isError: true,
      );
    } else {
      UiHelper.showSnackBar(
        context,
        SenderInfoStrings.noItemsImported.replaceAll('%s', sheetName),
        isError: true,
      );
    }
  }

  // تحميل بيانات البضائع من قاعدة البيانات
  Future<void> _loadGoodsData() async {
    try {
      final goodsDataService = GoodsDataService();
      final goodsItems = await goodsDataService.getAllGoodsAsItems();

      setState(() {
        _allItems.clear();
        _allItems.addAll(goodsItems);
      });
    } catch (e) {
      // خطأ في تحميل بيانات البضائع
    }
  }
}
