import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:share_plus/share_plus.dart';

import 'pdf_service.dart';
import 'windows_print_service.dart';
import 'print_settings_dialog.dart';
import '../services/print_settings_database_service.dart';
import '../models/print_settings_model.dart';
import 'package:path_provider/path_provider.dart';

/// نافذة منبثقة لمعاينة ملفات PDF مع إمكانية الطباعة والمشاركة
class PdfPreviewDialog extends StatefulWidget {
  final Uint8List pdfBytes;
  final String documentTitle;
  final String documentType;
  final int copyCount; // عدد النسخ المطلوب طباعتها

  const PdfPreviewDialog({
    super.key,
    required this.pdfBytes,
    required this.documentTitle,
    required this.documentType,
    this.copyCount = 1, // قيمة افتراضية
  });

  // متغير ثابت لمنع فتح أكثر من نافذة معاينة في نفس الوقت
  static bool _isDialogShowing = false;

  /// عرض النافذة المنبثقة للمعاينة
  static Future<void> show({
    required BuildContext context,
    required Uint8List pdfBytes,
    required String documentTitle,
    required String documentType,
    int copyCount = 1, // إضافة معامل عدد النسخ
  }) async {
    if (_isDialogShowing) {
      debugPrint('نافذة معاينة PDF مفتوحة بالفعل');
      return;
    }

    _isDialogShowing = true;

    try {
      await showDialog(
        context: context,
        builder: (context) => PdfPreviewDialog(
          pdfBytes: pdfBytes,
          documentTitle: documentTitle,
          documentType: documentType,
          copyCount: copyCount,
        ),
      );
    } finally {
      _isDialogShowing = false;
    }
  }

  @override
  State<PdfPreviewDialog> createState() => _PdfPreviewDialogState();
}

class _PdfPreviewDialogState extends State<PdfPreviewDialog> {
  late Uint8List currentPdfBytes;

  @override
  void initState() {
    super.initState();
    currentPdfBytes = widget.pdfBytes;
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    // تقليل عرض النافذة المنبثقة (49.5% من العرض، 90% من الارتفاع) - تقليل العرض مع الحفاظ على النسبة
    final dialogWidth = screenSize.width * 0.495;
    final dialogHeight = screenSize.height * 0.95;

    return Dialog(
      insetPadding:
          EdgeInsets.zero, // إزالة الهوامش الخارجية تماماً لاستخدام مساحة أكبر
      backgroundColor: Colors.transparent,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Container(
          width: dialogWidth,
          height: dialogHeight,
          color: Colors.white,
          child: Column(
            children: [
              // شريط العنوان مع زر الإغلاق - تقليل حجم الشريط
              Container(
                color: Theme.of(context).primaryColor,
                padding: const EdgeInsets.symmetric(
                    horizontal: 16, vertical: 4), // تقليل الهوامش العمودية
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Preview ${widget.documentTitle}${widget.copyCount > 1 ? ' (Print ${widget.copyCount} copies required)' : ''}',
                        style:
                            const TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ),

                    // زر إعدادات الطباعة (متاح لجميع الأنظمة)
                    Container(
                      margin: const EdgeInsets.only(right: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.settings,
                            color: Colors.white, size: 22),
                        onPressed: () => _showPrintSettings(context),
                        tooltip: 'إعدادات الطباعة - Print Settings',
                      ),
                    ),

                    // زر الطباعة
                    IconButton(
                      icon: const Icon(Icons.print, color: Colors.white),
                      onPressed: () => _printPdf(context),
                      tooltip: 'Print',
                    ),
                    // زر المشاركة
                    IconButton(
                      icon: const Icon(Icons.share, color: Colors.white),
                      onPressed: () => _sharePdf(context),
                      tooltip: 'Share',
                    ),
                    // زر الإغلاق
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: 'Close',
                    ),
                  ],
                ),
              ),

              // معاينة ملف PDF - تكبير مساحة المعاينة
              Expanded(
                child: Center(
                  // توسيط المعاينة
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(10), // هامش حول المعاينة
                    child: PdfPreview(
                      // تصغير عرض المعاينة لملصق المكتب فقط (zoom out)
                      maxPageWidth: widget.documentType == 'office_label'
                          ? 350
                          : 500, // zoom out أكثر للجميع
                      build: (PdfPageFormat format) => currentPdfBytes,
                      canChangePageFormat: false,
                      canChangeOrientation: false,
                      canDebug: false,
                      pdfFileName: widget.documentTitle,
                      scrollViewDecoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      // إزالة أزرار المكتبة الافتراضية تماماً
                      actions: const [],
                      useActions: false,
                      allowPrinting: false,
                      allowSharing: false,
                      previewPageMargin: widget.documentType == 'office_label'
                          ? const EdgeInsets.all(4) // هوامش أصغر لملصق المكتب
                          : const EdgeInsets.all(
                              8), // هوامش عادية للملصقات الأخرى
                      initialPageFormat: PdfPageFormat.a4,
                      pageFormats: const {
                        'A4': PdfPageFormat.a4,
                      },
                      dpi: 200, // زيادة الدقة قليلاً لوضوح أفضل مع الحجم الأكبر
                      padding: EdgeInsets.zero,
                      shouldRepaint: true,
                      dynamicLayout: false,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة لطباعة ملف PDF مع تطبيق الإعدادات المحفوظة
  Future<void> _printPdf(BuildContext context) async {
    try {
      debugPrint('🖨️ بدء الطباعة مع تطبيق الإعدادات المحفوظة');

      // تحميل إعدادات الطباعة من قاعدة البيانات
      final databaseService = PrintSettingsDatabaseService();
      final printSettings = await databaseService
              .getPrintSettings(widget.documentType) ??
          DefaultPrintSettings.getDefaultForDocumentType(widget.documentType);

      debugPrint('📋 إعدادات الطباعة المطبقة: $printSettings');

      // التحقق من نظام التشغيل
      if (Platform.isWindows) {
        debugPrint('🖥️ استخدام Windows Print Service للطباعة');

        // محاولة استخدام Windows Print Service أولاً
        final windowsResult =
            await WindowsPrintService.printPdfWithWindowsSettings(
          pdfBytes: currentPdfBytes,
          documentName: widget.documentTitle,
          documentType: widget.documentType,
          copyCount: printSettings.copies,
        );

        if (windowsResult) {
          debugPrint('✅ تمت الطباعة بنجاح مع Windows Print Service');
        } else {
          debugPrint('⚠️ فشلت طباعة Windows، التراجع إلى الطباعة العادية');

          // في حالة فشل Windows Print Service، استخدم الطباعة العادية مع الإعدادات
          await _printWithSettings(printSettings);
        }
      } else {
        debugPrint('🖥️ استخدام الطباعة العادية (ليس Windows)');

        // للأنظمة الأخرى، استخدم الطباعة العادية مع الإعدادات
        await _printWithSettings(printSettings);
      }
    } catch (e) {
      debugPrint('❌ خطأ في الطباعة: $e');

      // في حالة الخطأ، محاولة التراجع إلى الطباعة العادية
      try {
        debugPrint('🔄 محاولة التراجع إلى الطباعة العادية...');
        await PdfService.printPdf(
          currentPdfBytes,
          widget.documentTitle,
          widget.documentType,
          copyCount: widget.copyCount,
        );
      } catch (fallbackError) {
        debugPrint('❌ فشل في الطباعة العادية أيضاً: $fallbackError');
      }
    }
  }

  /// طباعة PDF مع تطبيق الإعدادات المحفوظة
  Future<void> _printWithSettings(PrintSettingsModel settings) async {
    try {
      // تطبيق الإعدادات على تنسيق الصفحة
      final pageFormat = settings.getPdfPageFormat();

      debugPrint(
          '📄 تنسيق الصفحة المطبق: ${settings.paperSize} - ${settings.orientation}');
      debugPrint(
          '📏 الهوامش: T:${settings.marginTop} B:${settings.marginBottom} L:${settings.marginLeft} R:${settings.marginRight}');
      debugPrint('🖨️ عدد النسخ: ${settings.copies}');

      // استخدام مكتبة printing مع الإعدادات المخصصة
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async {
          // إرجاع PDF مع تطبيق التنسيق المحدد
          return currentPdfBytes;
        },
        name: widget.documentTitle,
        format: pageFormat,
      );

      debugPrint('✅ تمت الطباعة بنجاح مع الإعدادات المخصصة');
    } catch (e) {
      debugPrint('❌ خطأ في الطباعة مع الإعدادات: $e');
      rethrow;
    }
  }

  // دالة لمشاركة ملف PDF
  Future<void> _sharePdf(BuildContext context) async {
    try {
      // حفظ الملف مؤقتًا
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/${widget.documentTitle}.pdf');
      await file.writeAsBytes(currentPdfBytes);

      // مشاركة الملف باستخدام SharePlus الجديد
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path)],
          text: 'Share ${widget.documentTitle} file',
        ),
      );
    } catch (e) {
      debugPrint('خطأ في المشاركة: $e');
    }
  }

  /// عرض نافذة إعدادات الطباعة
  Future<void> _showPrintSettings(BuildContext context) async {
    try {
      debugPrint('⚙️ فتح نافذة إعدادات الطباعة لـ ${widget.documentType}');

      // تحميل الإعدادات الحالية من قاعدة البيانات
      final databaseService = PrintSettingsDatabaseService();
      final currentSettings =
          await databaseService.getPrintSettings(widget.documentType);

      if (!mounted) return;
      final result = await showDialog<PrintSettingsModel>(
        context: context, // Using the BuildContext from the builder parameter
        barrierDismissible: false, // منع الإغلاق بالنقر خارج النافذة
        builder: (BuildContext context) {
          return PrintSettingsDialog(
            documentType: widget.documentType,
            initialSettings: currentSettings,
            onSettingsSaved: (settings) {
              debugPrint('✅ تم حفظ إعدادات الطباعة: $settings');
            },
          );
        },
      );

      if (result != null) {
        debugPrint('📋 إعدادات الطباعة المحدثة: $result');
        // الرسالة الإشعارية ستظهر الآن داخل النافذة المنبثقة نفسها
      }
    } catch (e) {
      debugPrint('❌ خطأ في عرض إعدادات الطباعة: $e');
    }
  }
}

/// نافذة منبثقة لمعاينة PDF مع التحميل في الخلفية
class PdfPreviewDialogWithLoading extends StatefulWidget {
  final String documentTitle;
  final String documentType;
  final bool hasPostalCode;
  final Future<Map<String, dynamic>> Function() onLoadPdf;
  final Future<Map<String, dynamic>> Function()?
      onLoadPdfForPrint; // دالة منفصلة للطباعة

  const PdfPreviewDialogWithLoading({
    super.key,
    required this.documentTitle,
    required this.documentType,
    required this.hasPostalCode,
    required this.onLoadPdf,
    this.onLoadPdfForPrint, // معامل اختياري
  });

  @override
  State<PdfPreviewDialogWithLoading> createState() =>
      _PdfPreviewDialogWithLoadingState();
}

class _PdfPreviewDialogWithLoadingState
    extends State<PdfPreviewDialogWithLoading> {
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  Uint8List? _pdfBytes;
  int _copyCount = 1;
  int _actualPrintCopies = 1; // العدد الحقيقي للطباعة

  @override
  void initState() {
    super.initState();
    // تأخير بدء تحميل PDF لضمان ظهور النافذة أولاً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تأخير إضافي صغير لضمان عرض النافذة في release mode
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _loadPdfInBackground();
        }
      });
    });
  }

  Future<void> _loadPdfInBackground() async {
    try {
      final result = await widget.onLoadPdf();

      if (mounted) {
        if (result['success'] == true) {
          setState(() {
            _pdfBytes = result['pdfBytes'];
            _copyCount = result['copyCount'] ?? 1;
            _actualPrintCopies =
                result['actualPrintCopies'] ?? _copyCount; // حفظ العدد الحقيقي
            _isLoading = false;
            _hasError = false;
          });
        } else {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = result['error'] ?? 'Unknown error';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  /// تحديد رسالة التحميل المناسبة بناءً على نوع المستند
  String _getLoadingMessage() {
    // التحقق من نوع المستند أولاً
    if (widget.documentType == 'invoice') {
      return 'Creating invoice';
    }

    // للمستندات الأخرى، استخدم المنطق القديم
    return widget.hasPostalCode
        ? 'Creating address label'
        : 'Creating office label';
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = screenSize.width * 0.495;
    final dialogHeight = screenSize.height * 0.95;

    return Dialog(
      insetPadding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Container(
          width: dialogWidth,
          height: dialogHeight,
          color: Colors.white,
          child: Column(
            children: [
              // شريط العنوان
              Container(
                color: Theme.of(context).primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'preview ${widget.documentTitle}${_actualPrintCopies > 1 ? ' (print $_actualPrintCopies copies)' : ''}',
                        style:
                            const TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ),

                    // زر إعدادات الطباعة - مُعطل أثناء التحميل
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.settings,
                            color: Colors.white, size: 20),
                        onPressed: _isLoading || _hasError
                            ? null
                            : () => _showPrintSettings(context),
                        tooltip: 'إعدادات الطباعة / Print Settings',
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                    // زر الطباعة - مُعطل أثناء التحميل
                    IconButton(
                      icon: const Icon(Icons.print, color: Colors.white),
                      onPressed: _isLoading || _hasError
                          ? null
                          : () => _printPdf(context),
                      tooltip: 'print',
                    ),
                    // زر المشاركة - مُعطل أثناء التحميل
                    IconButton(
                      icon: const Icon(Icons.share, color: Colors.white),
                      onPressed: _isLoading || _hasError
                          ? null
                          : () => _sharePdf(context),
                      tooltip: 'share',
                    ),
                    // زر الإغلاق
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: 'close',
                    ),
                  ],
                ),
              ),

              // المحتوى الأساسي
              Expanded(
                child: _buildContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      // عرض مؤشر إنشاء PDF مباشرة
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة PDF بدلاً من مؤشر التحميل العادي
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    size: 48,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 8),
                  // نقاط ترقص بدلاً من مؤشر التحميل الدائري
                  SizedBox(
                    width: 60,
                    height: 24,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(3, (index) {
                        return AnimatedContainer(
                          duration: Duration(milliseconds: 600 + (index * 100)),
                          curve: Curves.easeInOut,
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: TweenAnimationBuilder<double>(
                            duration:
                                Duration(milliseconds: 1000 + (index * 200)),
                            tween: Tween(begin: 0.3, end: 1.0),
                            builder: (context, value, child) {
                              return Transform.scale(
                                scale: value,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withValues(alpha: value),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              );
                            },
                            onEnd: () {
                              // إعادة تشغيل الحركة
                              if (mounted) {
                                setState(() {});
                              }
                            },
                          ),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Generating ${widget.documentTitle}...',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              _getLoadingMessage(),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please wait...',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else if (_hasError) {
      // عرض رسالة الخطأ
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'error occurred while preparing the label',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            if (_errorMessage != null)
              Text(
                _errorMessage!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                  _errorMessage = null;
                });
                _loadPdfInBackground();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('try again'),
            ),
          ],
        ),
      );
    } else if (_pdfBytes != null) {
      // عرض معاينة PDF
      return Center(
        // توسيط المعاينة
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10), // هامش حول المعاينة
          child: PdfPreview(
            maxPageWidth: widget.documentType == 'office_label'
                ? 350
                : 500, // zoom out أكثر للجميع
            build: (PdfPageFormat format) => _pdfBytes!,
            canChangePageFormat: false,
            canChangeOrientation: false,
            canDebug: false,
            pdfFileName: widget.documentTitle,
            scrollViewDecoration: const BoxDecoration(color: Colors.white),
            actions: const [],
            useActions: false,
            allowPrinting: false,
            allowSharing: false,
            previewPageMargin: widget.documentType == 'office_label'
                ? const EdgeInsets.all(4) // هوامش أصغر لملصق المكتب
                : const EdgeInsets.all(8), // هوامش عادية للملصقات الأخرى
            initialPageFormat: PdfPageFormat.a4,
            pageFormats: const {'A4': PdfPageFormat.a4},
            dpi: 200, // زيادة الدقة قليلاً لوضوح أفضل
            padding: EdgeInsets.zero,
            shouldRepaint: true,
            dynamicLayout: false,
          ),
        ),
      );
    } else {
      // حالة غير متوقعة
      return const Center(
        child: Text('unexpected case'),
      );
    }
  }

  // دالة لطباعة ملف PDF
  Future<void> _printPdf(BuildContext context) async {
    if (_pdfBytes == null) return;

    try {
      // إذا كان العدد الحقيقي للطباعة أكبر من 1، نحتاج لإنشاء PDF جديد بالعدد الكامل
      if (_actualPrintCopies > 1 && widget.onLoadPdfForPrint != null) {
        debugPrint('إنشاء PDF للطباعة بعدد $_actualPrintCopies نسخ');

        // الحصول على PDF للطباعة بالعدد الكامل
        final result = await widget.onLoadPdfForPrint!();

        if (result['success'] == true) {
          // طباعة PDF الجديد الذي يحتوي على العدد الكامل
          await PdfService.printPdfWithEnhancedMonitoring(
            result['pdfBytes'],
            widget.documentTitle,
            widget.documentType,
            copyCount: _actualPrintCopies,
          );
        } else {
          debugPrint('فشل في إنشاء PDF للطباعة: ${result['error']}');
        }
      } else {
        // للحالات الأخرى، استخدم الدالة المحسنة للطباعة
        await PdfService.printPdfWithEnhancedMonitoring(
          _pdfBytes!,
          widget.documentTitle,
          widget.documentType,
          copyCount: _actualPrintCopies,
        );
      }
    } catch (e) {
      debugPrint('error in print: $e');
    }
  }

  // دالة لمشاركة ملف PDF
  Future<void> _sharePdf(BuildContext context) async {
    if (_pdfBytes == null) return;

    try {
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/${widget.documentTitle}.pdf');
      await file.writeAsBytes(_pdfBytes!);

      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path)],
          text: 'Share ${widget.documentTitle} file',
        ),
      );
    } catch (e) {
      debugPrint('error in share: $e');
    }
  }

  // دالة لعرض إعدادات الطباعة كنافذة منبثقة
  Future<void> _showPrintSettings(BuildContext context) async {
    try {
      debugPrint(
          '⚙️ فتح نافذة إعدادات الطباعة المنبثقة لـ ${widget.documentType}');

      // تحميل الإعدادات الحالية من قاعدة البيانات
      final databaseService = PrintSettingsDatabaseService();
      final currentSettings =
          await databaseService.getPrintSettings(widget.documentType);

      if (!mounted) return;
      final contextToUse = context;

      final result = await showDialog<PrintSettingsModel>(
        context: contextToUse,
        barrierDismissible: false, // منع الإغلاق بالنقر خارج النافذة
        builder: (BuildContext context) {
          return PrintSettingsDialog(
            documentType: widget.documentType,
            initialSettings: currentSettings,
            onSettingsSaved: (settings) {
              debugPrint('✅ تم حفظ إعدادات الطباعة: $settings');
            },
          );
        },
      );

      if (result != null) {
        debugPrint('📋 إعدادات الطباعة المحدثة: $result');
        // الرسالة الإشعارية تظهر الآن داخل النافذة المنبثقة نفسها
      }
    } catch (e) {
      debugPrint('❌ خطأ في عرض إعدادات الطباعة: $e');
    }
  }
}
