import 'package:flutter/material.dart';

import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../widgets/home_parts/basic_info.dart';

import '../widgets/home_parts/cost_info.dart';

import '../widgets/home_parts/sender_info.dart';

import '../widgets/home_parts/receiver_info.dart';

import '../widgets/home_parts/weight_info.dart';

import '../widgets/home_parts/notes_widget.dart';

import '../widgets/home_parts/action_buttons.dart';

import '../services/code_data_service.dart';

import '../mixins/home_mixin/home_screen_search_mixin.dart';

import '../mixins/home_mixin/home_screen_calculations_mixin.dart';

import '../mixins/home_mixin/home_screen_data_mixin.dart';

import '../mixins/home_mixin/home_screen_save_mixin.dart';

import '../mixins/home_mixin/home_screen_update_mixin.dart';

import '../mixins/home_mixin/home_screen_clear_mixin.dart';

import '../utils/ui_helper.dart';

import '../pdf_module/pdf_preview_dialog.dart';
import '../pdf_module/pdf_templates.dart';
import '../providers/riverpod/user_provider.dart';
import 'dart:math' as math;
import 'dart:async';
// import '../services/win32_print_service.dart';

// Definition of ShowSearchIntent class

class ShowSearchIntent extends Intent {
  const ShowSearchIntent();
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  // دالة ساكنة للوصول إلى دالة البحث من الخارج
  static void showSearch(BuildContext context) {
    // البحث عن حالة الشاشة الرئيسية
    final homeScreenState = context.findAncestorStateOfType<_HomeScreenState>();
    if (homeScreenState != null) {
      try {
        homeScreenState.showSearchColumnsDialog();
      } catch (e) {
        debugPrint('خطأ في استدعاء دالة showSearchColumnsDialog: $e');
      }
    } else {
      debugPrint('لم يتم العثور على حالة _HomeScreenState');
    }
  }

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with
        HomeScreenSearchMixin,
        HomeScreenCalculationsMixin,
        HomeScreenDataMixin,
        HomeScreenSaveMixin,
        HomeScreenUpdateMixin,
        HomeScreenClearMixin {
  // Component references

  @override
  final GlobalKey costInfoKey = GlobalKey();

  @override
  final GlobalKey weightInfoKey = GlobalKey();

  @override
  final GlobalKey basicInfoKey = GlobalKey();

  @override
  final GlobalKey senderInfoKey = GlobalKey();

  @override
  final GlobalKey receiverInfoKey = GlobalKey();

  @override
  final GlobalKey notesKey = GlobalKey();

  // دالة لإعادة تحميل الصفحة بالكامل بعد حذف السجل
  void resetAndReloadScreen() {
    debugPrint('بدء عملية إعادة تحميل الصفحة بالكامل');

    // مسح الحقول المحددة فقط
    try {
      // الحصول على مراجع للمكونات
      final senderInfoState = senderInfoKey.currentState;
      final receiverInfoState = receiverInfoKey.currentState;
      final weightInfoState = weightInfoKey.currentState;

      if (senderInfoState != null) {
        // مسح حقول المرسل
        (senderInfoState as dynamic).clearSenderFields();
      }

      if (receiverInfoState != null) {
        // مسح حقول المستلم
        (receiverInfoState as dynamic).clearReceiverFields();
      }

      if (weightInfoState != null) {
        // مسح حقول الوزن
        (weightInfoState as dynamic).clearWeightFields();
      }
    } catch (e) {
      debugPrint('خطأ في مسح الحقول المحددة: $e');
    }

    // إعادة تعيين حالة الأزرار
    setState(() {
      isSaveEnabled = true;
      isUpdateEnabled = false;
    });

    // إعادة تحميل البيانات الأساسية
    try {
      final basicInfoState = basicInfoKey.currentState;
      if (basicInfoState != null) {
        // توليد كود جديد
        (basicInfoState as dynamic).generateNewCode();
        debugPrint('تم توليد كود جديد بنجاح');
      }
    } catch (e) {
      debugPrint('خطأ في توليد كود جديد: $e');
    }

    debugPrint('تم إعادة تحميل الصفحة بنجاح');
  }

  // Variable to store For each 1 Kg value

  double _forEachKgValue = 0;

  @override
  double get forEachKgValue => _forEachKgValue;

  @override
  set forEachKgValue(double value) => _forEachKgValue = value;

  // Variable to store Minimum price value

  double _minimumPriceValue = 0;

  @override
  double get minimumPriceValue => _minimumPriceValue;

  @override
  set minimumPriceValue(double value) => _minimumPriceValue = value;

  // Add code data service

  @override
  final CodeDataService codeDataService = CodeDataService();

  // Add variables to control button states

  bool _isSaveEnabled = true;

  @override
  bool get isSaveEnabled => _isSaveEnabled;

  @override
  set isSaveEnabled(bool value) => setState(() => _isSaveEnabled = value);

  bool _isUpdateEnabled = false;

  @override
  bool get isUpdateEnabled => _isUpdateEnabled;

  @override
  set isUpdateEnabled(bool value) => setState(() => _isUpdateEnabled = value);

  // Variable to store current code - used for internal tracking and future functions

  String _currentCode = '';

  @override
  String get currentCode => _currentCode;

  @override
  set currentCode(String value) => _currentCode = value;

  // List of available search columns

  @override
  final List<Map<String, String>> searchColumns = [
    {'key': 'code_no', 'title': 'Shipment Code'},
    {'key': 'truck_no', 'title': 'Truck Number'},
    {'key': 'sender_name', 'title': 'Sender Name'},
    {'key': 'sender_phone', 'title': 'Sender Phone'},
    {'key': 'receiver_name', 'title': 'Receiver Name'},
    {'key': 'receiver_phone', 'title': 'Receiver Phone'},
    {'key': 'country', 'title': 'Country'},
    {'key': 'city', 'title': 'City'},
    {'key': 'street_name_no', 'title': 'Street'},
    {'key': 'postal_code', 'title': 'Postal Code'},
    {'key': 'city_name', 'title': 'City Name'},
  ];

  @override
  void initState() {
    super.initState();

    _initializeNewCode();
  }

  // Override of clearFields to show "Cleaned up" message
  @override
  void clearFields({bool loadDefaultTruckNo = true}) {
    // Call the parent method to perform the actual clearing
    super.clearFields(loadDefaultTruckNo: loadDefaultTruckNo);

    // Show a custom notification with the new message
    if (mounted) {
      UiHelper.showNotification(
        context,
        messageEn: "Cleaned up",
        isError: false,
        durationSeconds: 2,
      );
    }
  }

  // Function to initialize a new code when starting the application

  void _initializeNewCode() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      generateNewCode().then((_) {
        // Check that the widget is still mounted in the tree before using context

        if (!mounted) return;
      }).catchError((error) {
        // Handle error

        if (!mounted) return;

        UiHelper.showSnackBar(
          context,
          'Error generating new code: $error',
          isError: true,
        );
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: FocusNode(),
      onKeyEvent: (KeyEvent event) {
        // الكشف عن ضغطة CTRL+F في أي مكان في الشاشة

        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.keyF &&
            (HardwareKeyboard.instance.isControlPressed ||
                HardwareKeyboard.instance.isMetaPressed)) {
          showSearchColumnsDialog();
        }
      },
      child: Shortcuts(
        shortcuts: <ShortcutActivator, Intent>{
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF):
              const ShowSearchIntent(),
        },
        child: Actions(
          actions: <Type, Action<Intent>>{
            ShowSearchIntent: CallbackAction<ShowSearchIntent>(
              onInvoke: (ShowSearchIntent intent) {
                showSearchColumnsDialog();

                return null;
              },
            ),
          },
          child: Focus(
            autofocus: true,
            child: Scaffold(
              appBar: null,
              body: Column(
                children: [
                  Expanded(
                    child: _buildMainContent(),
                  ),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build main content

  Widget _buildMainContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMainRow(),
        ],
      ),
    );
  }

  // Build main row containing two columns

  Widget _buildMainRow() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column: contains BasicInfo and below it sender and receiver information

        Expanded(
          flex: 63,
          child: _buildLeftColumn(),
        ),

        const SizedBox(width: 8),

        // Right column: contains cost information and notes

        Expanded(
          flex: 37,
          child: _buildRightColumn(),
        ),
      ],
    );
  }

  // Build left column

  Widget _buildLeftColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Basic information

        BasicInfo(key: basicInfoKey),

        const SizedBox(height: 4),

        // Row containing sender and receiver information

        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sender information

              Expanded(
                child: _buildSenderColumn(),
              ),

              const SizedBox(width: 8),

              // Receiver information

              Expanded(
                child: ReceiverInfo(
                  key: receiverInfoKey,
                  onPostSubCostChanged: updatePostSubCost,
                  onDoorToDoorCostChanged: updateDoorToDoorCost,
                  onAddressInfoVisibilityChanged: updateAddressInfoVisibility,
                  codeDataService: codeDataService,
                  notesWidget: NotesWidget(key: notesKey),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build sender column

  Widget _buildSenderColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SenderInfo(key: senderInfoKey),

        const SizedBox(height: 4),

        // Weight information (below sender information)

        WeightInfo(
          key: weightInfoKey,
          onRealWeightChanged: onRealWeightChanged,
          onBoxNoChanged: onBoxNoChanged,
          onPalletNoChanged: onPalletNoChanged,
        ),
      ],
    );
  }

  // Build right column

  Widget _buildRightColumn() {
    return Column(
      children: [
        CostInfo(key: costInfoKey),
        // تم نقل NotesWidget إلى ReceiverInfo لتكون بين AddressInfo و PriceInfo
      ],
    );
  }

  // معالجة طباعة ملصق المكتب
  Future<void> printOfficeLabel() async {
    try {
      // الحصول على الكود الحالي من حقل Code No مباشرة
      String codeNoFromField = '';
      final basicInfoState = basicInfoKey.currentState;
      if (basicInfoState != null) {
        try {
          codeNoFromField = (basicInfoState as dynamic).getCodeNo().trim();
        } catch (e) {
          debugPrint('خطأ في الحصول على الكود من BasicInfo: $e');
        }
      }

      // التحقق من وجود كود في الحقل
      if (codeNoFromField.isEmpty) {
        if (!mounted) return;
        UiHelper.showNotification(
          context,
          messageEn: 'Please enter a code number first',
          isError: true,
          durationSeconds: 2,
        );
        return;
      }

      // التحقق من وجود الكود في قاعدة البيانات
      Map<String, dynamic>? codeDetails =
          await codeDataService.getCodeDataByCode(codeNoFromField);

      if (codeDetails == null || codeDetails.isEmpty) {
        if (!mounted) return;
        UiHelper.showNotification(
          context,
          messageEn:
              'Code not found in database. Please save the data first before creating the label.',
          isError: true,
          durationSeconds: 3,
        );
        return;
      }

      // تحديد نوع الملصق بناءً على وجود الرمز البريدي
      final bool hasPostalCode = codeDetails['postal_code'] != null &&
          codeDetails['postal_code'].toString().isNotEmpty;

      // تحديد العنوان ونوع المستند
      final String labelTitle = hasPostalCode ? 'Post Label' : 'Office Label';
      final String documentType =
          hasPostalCode ? PdfTemplates.postLabel : PdfTemplates.officeLabel;

      // إظهار نافذة المعاينة مباشرة مع ملف PDF فارغ
      if (!mounted) return;

      // عرض نافذة المعاينة مع تحميل فارغ أولاً
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => PdfPreviewDialogWithLoading(
          documentTitle: labelTitle,
          documentType: documentType,
          hasPostalCode: hasPostalCode,
          onLoadPdf: () => _generatePdfInBackground(codeDetails, hasPostalCode),
          onLoadPdfForPrint: () =>
              _generatePdfForPrint(codeDetails, hasPostalCode),
        ),
      );
    } catch (e) {
      debugPrint('Error creating the label: $e');
      if (!mounted) return;
      UiHelper.showNotification(
        context,
        messageEn: 'Error creating the label: $e',
        isError: true,
        durationSeconds: 2,
      );
    }
  }

  // وظيفة لمعالجة PDF في الخلفية
  Future<Map<String, dynamic>> _generatePdfInBackground(
      Map<String, dynamic> codeDetails, bool hasPostalCode) async {
    try {
      // تأخير صغير لضمان ظهور النافذة أولاً في release mode
      await Future.delayed(const Duration(milliseconds: 50));

      // يبدو أن flag_image قيمتها فارغة أو null، دعنا نتحقق من البيانات
      debugPrint(
          'حالة صورة العلم في البيانات المسترجعة: ${codeDetails.containsKey('flag_image') ? (codeDetails['flag_image'] != null && codeDetails['flag_image'].toString().isNotEmpty ? "موجودة" : "فارغة") : "غير موجودة"}');

      // إضافة صورة المدينة إذا كان هناك رمز بريدي
      if (hasPostalCode &&
          codeDetails['city_name'] != null &&
          codeDetails['city_name'].toString().isNotEmpty) {
        try {
          // البحث عن سجل المدينة للحصول على صورة العلم
          final String cityName = codeDetails['city_name'].toString();
          final String country = codeDetails['country'].toString();
          // إضافة اسم المدينة من حقل city أيضاً كاحتمال ثانٍ للبحث
          final String cityFromCityField = codeDetails['city'] != null &&
                  codeDetails['city'].toString().isNotEmpty
              ? codeDetails['city'].toString()
              : '';

          debugPrint('=== البحث عن صورة المدينة ===');
          debugPrint('اسم المدينة المطلوبة: "$cityName"');
          debugPrint('اسم المدينة من حقل city: "$cityFromCityField"');
          debugPrint('اسم الدولة: "$country"');

          // استعلام لجميع المدن للعثور على المدينة المطابقة
          final cities = await codeDataService.getAllUniqueCities();
          debugPrint('عدد المدن المتاحة: ${cities.length}');

          // طباعة أسماء بعض المدن للتشخيص
          if (cities.isNotEmpty) {
            debugPrint('عينة من المدن المتاحة:');
            for (var i = 0; i < math.min(cities.length, 3); i++) {
              final city = cities[i];
              final hasFlagImage = city['flag_image'] != null &&
                  city['flag_image'].toString().isNotEmpty;
              debugPrint(
                  '- ${city['city']} (${city['country']}) - ${hasFlagImage ? "لها صورة" : "بدون صورة"}');
            }
          }

          // 1. البحث المباشر عن المطابقة الدقيقة باسم المدينة
          List<Map<String, dynamic>> exactMatches = cities
              .where((city) =>
                  city['city'] == cityName && city['country'] == country)
              .toList();

          // 2. البحث عن المطابقة بتجاهل حالة الأحرف
          List<Map<String, dynamic>> caseInsensitiveMatches = cities
              .where((city) =>
                  city['city'].toString().toLowerCase() ==
                      cityName.toLowerCase() &&
                  city['country'].toString().toLowerCase() ==
                      country.toLowerCase())
              .toList();

          // 3. البحث باستخدام اسم المدينة من حقل city في القسم الأساسي
          List<Map<String, dynamic>> cityFieldMatches = [];
          if (cityFromCityField.isNotEmpty) {
            cityFieldMatches = cities
                .where((city) =>
                    city['city'] == cityFromCityField &&
                    city['country'] == country)
                .toList();
          }

          // 4. البحث عن تطابق جزئي (يحتوي على جزء من اسم المدينة)
          List<Map<String, dynamic>> containsMatches = cities
              .where((city) =>
                  city['city']
                      .toString()
                      .toLowerCase()
                      .contains(cityName.toLowerCase()) &&
                  city['country'].toString().toLowerCase() ==
                      country.toLowerCase())
              .toList();

          // 5. البحث عن المدينة بالدولة فقط (كخيار أخير)
          List<Map<String, dynamic>> countryOnlyMatches =
              cities.where((city) => city['country'] == country).toList();

          debugPrint(
              'معايير التطابق الأولى: المدينة تساوي "$cityName" والدولة تساوي "$country"');
          debugPrint('عدد المطابقات الدقيقة: ${exactMatches.length}');
          debugPrint(
              'عدد المطابقات بتجاهل حالة الأحرف: ${caseInsensitiveMatches.length}');
          debugPrint(
              'عدد المطابقات باستخدام حقل city: ${cityFieldMatches.length}');
          debugPrint(
              'عدد المطابقات التي تحتوي على جزء من اسم المدينة: ${containsMatches.length}');
          debugPrint('عدد المطابقات بالدولة فقط: ${countryOnlyMatches.length}');

          // فلترة المطابقات للتأكد من أن لها صور أعلام
          List<Map<String, dynamic>> filterByFlag(
              List<Map<String, dynamic>> matches) {
            return matches
                .where((city) =>
                    city['flag_image'] != null &&
                    city['flag_image'].toString().isNotEmpty)
                .toList();
          }

          exactMatches = filterByFlag(exactMatches);
          caseInsensitiveMatches = filterByFlag(caseInsensitiveMatches);
          cityFieldMatches = filterByFlag(cityFieldMatches);
          containsMatches = filterByFlag(containsMatches);
          countryOnlyMatches = filterByFlag(countryOnlyMatches);

          debugPrint(
              'بعد التصفية - المطابقات الدقيقة مع صور: ${exactMatches.length}');
          debugPrint(
              'بعد التصفية - المطابقات بتجاهل حالة الأحرف مع صور: ${caseInsensitiveMatches.length}');
          debugPrint(
              'بعد التصفية - المطابقات بحقل city مع صور: ${cityFieldMatches.length}');
          debugPrint(
              'بعد التصفية - المطابقات التي تحتوي على اسم المدينة مع صور: ${containsMatches.length}');
          debugPrint(
              'بعد التصفية - المطابقات بالدولة فقط مع صور: ${countryOnlyMatches.length}');

          // اختيار أفضل مطابقة بناءً على الأولوية
          Map<String, dynamic>? cityWithFlag;
          if (exactMatches.isNotEmpty) {
            cityWithFlag = exactMatches.first;
            debugPrint(
                'تم العثور على مطابقة دقيقة: ${cityWithFlag['city']} (${cityWithFlag['country']})');
          } else if (caseInsensitiveMatches.isNotEmpty) {
            cityWithFlag = caseInsensitiveMatches.first;
            debugPrint(
                'تم العثور على مطابقة بتجاهل حالة الأحرف: ${cityWithFlag['city']} (${cityWithFlag['country']})');
          } else if (cityFieldMatches.isNotEmpty) {
            cityWithFlag = cityFieldMatches.first;
            debugPrint(
                'تم العثور على مطابقة باستخدام حقل city: ${cityWithFlag['city']} (${cityWithFlag['country']})');
          } else if (containsMatches.isNotEmpty) {
            cityWithFlag = containsMatches.first;
            debugPrint(
                'تم العثور على مطابقة تحتوي على جزء من اسم المدينة: ${cityWithFlag['city']} (${cityWithFlag['country']})');
          } else if (countryOnlyMatches.isNotEmpty) {
            cityWithFlag = countryOnlyMatches.first;
            debugPrint(
                'تم العثور على مدينة في نفس الدولة: ${cityWithFlag['city']} (${cityWithFlag['country']})');
          }

          // فحص وجود flag_image في المدينة المطابقة
          if (cityWithFlag != null &&
              cityWithFlag['flag_image'] != null &&
              cityWithFlag['flag_image'].toString().isNotEmpty) {
            debugPrint('المدينة ${cityWithFlag['city']} تحتوي على صورة علم');
            // إضافة صورة العلم إلى بيانات الكود
            codeDetails['flag_image'] = cityWithFlag['flag_image'];
          } else if (cityWithFlag != null) {
            debugPrint(
                'المدينة ${cityWithFlag['city']} لا تحتوي على صورة في قاعدة البيانات');
          } else {
            debugPrint('لم يتم العثور على أي مدينة مطابقة');
          }

          debugPrint('===========================');
        } catch (e) {
          debugPrint('خطأ أثناء البحث عن صورة المدينة: $e');
        }
      }

      // إضافة معلومات الفرع للملصق
      try {
        // إنشاء نسخة قابلة للتعديل من البيانات
        Map<String, dynamic> editableCodeDetails =
            Map<String, dynamic>.from(codeDetails);

        // جمع البيانات الحالية لاستخراج معلومات الفرع
        Map<String, dynamic> currentData = collectAllData(forLabel: true);

        // إضافة معلومات الفرع إلى بيانات الكود
        if (currentData.containsKey('current_branch')) {
          editableCodeDetails['current_branch'] = currentData['current_branch'];
        }
        if (currentData.containsKey('current_user')) {
          editableCodeDetails['current_user'] = currentData['current_user'];
        }
        if (currentData.containsKey('user_role')) {
          editableCodeDetails['user_role'] = currentData['user_role'];
        }

        // استخدام النسخة القابلة للتعديل
        codeDetails = editableCodeDetails;

        debugPrint(
            'تم إضافة معلومات الفرع للملصق: ${codeDetails['current_branch']}');
      } catch (e) {
        debugPrint('خطأ في إضافة معلومات الفرع للملصق: $e');
        // إنشاء نسخة قابلة للتعديل مع قيم افتراضية
        codeDetails = Map<String, dynamic>.from(codeDetails);
        codeDetails['current_branch'] = 'Baghdad';
        codeDetails['current_user'] = 'admin';
        codeDetails['user_role'] = 'admin';
      }

      // توليد ملف PDF باستخدام القالب المناسب
      final pdfResult = await PdfTemplates.generateInDocument(
        companyName: 'EUKnet Company',
        shipmentData: codeDetails,
        forPreview: true, // إنشاء نسخة واحدة فقط للمعاينة
      );

      // التحقق من نوع النتيجة المُرجعة - إما Uint8List أو List<Uint8List>
      Uint8List previewPdfBytes;
      if (hasPostalCode) {
        // إذا كانت النتيجة قائمة ملفات PDF منفصلة (في حالة ملصق البريد)
        final pdfFiles = pdfResult as List<Uint8List>;
        previewPdfBytes = pdfFiles.first; // أخذ الصفحة الأولى فقط للمعاينة
      } else {
        // إذا كانت النتيجة ملف PDF واحد (في حالة ملصق المكتب)
        final fullPdf = pdfResult as Uint8List;

        // للمعاينة، نستخدم الملف كاملاً - سيعرض PdfPreview الصفحة الأولى فقط
        previewPdfBytes = fullPdf;
      }

      // حساب عدد النسخ المطلوب من box_no + pallet_no
      int boxCount = 0;
      int palletCount = 0;

      // box_no دائماً أكبر من 0، لذلك لا حاجة للتحقق
      if (codeDetails['box_no'] != null &&
          codeDetails['box_no'].toString().isNotEmpty) {
        try {
          boxCount = int.parse(codeDetails['box_no'].toString());
        } catch (e) {
          debugPrint('خطأ في تحويل box_no إلى رقم: $e');
          boxCount = 1; // قيمة افتراضية
        }
      } else {
        boxCount = 1; // قيمة افتراضية إذا كان الحقل فارغ
      }

      if (codeDetails['pallet_no'] != null &&
          codeDetails['pallet_no'].toString().isNotEmpty) {
        try {
          palletCount = int.parse(codeDetails['pallet_no'].toString());
        } catch (e) {
          debugPrint('خطأ في تحويل pallet_no إلى رقم: $e');
        }
      }

      int totalActualCopyCount = boxCount + palletCount;

      debugPrint(
          'عدد الصناديق: $boxCount، عدد الباليت: $palletCount، العدد الكلي: $totalActualCopyCount');

      return {
        'success': true,
        'pdfBytes': previewPdfBytes,
        'copyCount': 1, // عرض نسخة واحدة فقط في المعاينة
        'actualPrintCopies':
            totalActualCopyCount, // العدد الحقيقي للطباعة لجميع الأنواع
      };
    } catch (e) {
      debugPrint('خطأ في معالجة PDF: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // وظيفة لإنشاء PDF للطباعة مع العدد الكامل
  Future<Map<String, dynamic>> _generatePdfForPrint(
      Map<String, dynamic> codeDetails, bool hasPostalCode) async {
    try {
      debugPrint('إنشاء PDF للطباعة مع العدد الكامل');

      // إنشاء PDF بدون معامل forPreview (سيتم إنشاء العدد الكامل)
      final pdfResult = await PdfTemplates.generateInDocument(
        companyName: 'EUKnet Company',
        shipmentData: codeDetails,
        forPreview: false, // عدم استخدام وضع المعاينة - إنشاء العدد الكامل
      );

      if (hasPostalCode) {
        // إذا كانت النتيجة قائمة ملفات PDF منفصلة (في حالة ملصق البريد)
        final pdfFiles = pdfResult as List<Uint8List>;
        return {
          'success': true,
          'pdfBytes': pdfFiles.first, // سيحتوي على جميع الصفحات
        };
      } else {
        // إذا كانت النتيجة ملف PDF واحد (في حالة ملصق المكتب)
        final pdfBytes = pdfResult as Uint8List;
        return {
          'success': true,
          'pdfBytes': pdfBytes,
        };
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء PDF للطباعة: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // معالجة طباعة الفاتورة
  Future<void> printInvoice() async {
    try {
      // التحقق من وجود الكود في الشاشة
      if (currentCode.isEmpty) {
        if (!mounted) return;
        UiHelper.showNotification(
          context,
          messageEn: 'Please save the data first before creating an invoice',
          isError: true,
          durationSeconds: 2,
        );
        return;
      }

      // التحقق المفصل من وجود الكود في قاعدة البيانات قبل فتح الحوار
      debugPrint('=== بدء التحقق من الكود للفاتورة ===');

      // الحصول على الكود الفعلي من واجهة المستخدم
      String displayedCode = '';
      try {
        final basicInfoState = basicInfoKey.currentState;
        if (basicInfoState != null) {
          displayedCode = (basicInfoState as dynamic).getCodeNo() ?? '';
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على الكود المعروض: $e');
      }

      debugPrint('الكود المحفوظ في currentCode: [$currentCode]');
      debugPrint('الكود المعروض في الواجهة: [$displayedCode]');
      debugPrint('طول الكود المحفوظ: ${currentCode.length}');
      debugPrint('طول الكود المعروض: ${displayedCode.length}');

      // إذا كان هناك تضارب بين الكودين، استخدم الكود المعروض
      String actualCode =
          displayedCode.isNotEmpty ? displayedCode : currentCode;
      if (displayedCode != currentCode) {
        debugPrint('⚠️ تحذير: تضارب بين الكود المحفوظ والمعروض!');
        debugPrint('سيتم استخدام الكود المعروض: [$actualCode]');
        currentCode = actualCode; // تحديث currentCode ليطابق الواجهة
      }

      if (actualCode.isEmpty) {
        if (!mounted) return;
        UiHelper.showNotification(
          context,
          messageEn: 'Please enter a code number first',
          isError: true,
          durationSeconds: 2,
        );
        return;
      }

      Map<String, dynamic>? codeDetails =
          await codeDataService.getCodeDataByCode(actualCode);

      if (codeDetails == null || codeDetails.isEmpty) {
        if (!mounted) return;
        UiHelper.showNotification(
          context,
          messageEn:
              'Code "$actualCode" not found in database. Please save the data first.',
          isError: true,
          durationSeconds: 3,
        );
        debugPrint('❌ الكود [$actualCode] غير موجود في قاعدة البيانات');
        return;
      }

      // التحقق من أن الكود المسترد يطابق الكود المطلوب
      String? retrievedCode = codeDetails['code_no']?.toString();
      debugPrint('الكود المسترد من قاعدة البيانات: [$retrievedCode]');

      if (retrievedCode != actualCode) {
        if (!mounted) return;
        UiHelper.showNotification(
          context,
          messageEn:
              'Data mismatch error. Expected: "$actualCode", Got: "$retrievedCode"',
          isError: true,
          durationSeconds: 4,
        );
        debugPrint('❌ خطأ في مطابقة البيانات!');
        debugPrint('المطلوب: [$actualCode]');
        debugPrint('المسترد: [$retrievedCode]');
        return;
      }

      debugPrint(
          '✅ تم العثور على الكود الصحيح في قاعدة البيانات: [$actualCode]');

      // فتح نافذة المعاينة فوراً مع التحميل في الخلفية
      if (!mounted) return;

      // حفظ الكود الصحيح للاستخدام داخل الحوار
      final String verifiedCode = actualCode;

      // الحصول على معلومات المستخدم قبل async function
      String userBranch = '';
      try {
        final user = ProviderScope.containerOf(context).read(userProvider);
        userBranch = user.branch;
        debugPrint('اسم الفرع من المستخدم: $userBranch');
      } catch (e) {
        debugPrint('خطأ في الحصول على معلومات المستخدم: $e');
      }

      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => PdfPreviewDialogWithLoading(
          documentTitle: 'Invoice',
          documentType: PdfTemplates.invoice,
          hasPostalCode: false, // الفاتورة ليست لها رمز بريدي
          onLoadPdf: () async {
            // معالجة إنشاء PDF في الخلفية
            try {
              // استخدام البيانات التي تم جلبها مسبقاً والتأكد من تحديثها
              debugPrint('=== إعادة التحقق من الكود داخل onLoadPdf ===');
              debugPrint('الكود المطلوب مرة أخرى: [$verifiedCode]');

              Map<String, dynamic> currentCodeDetails =
                  await codeDataService.getCodeDataByCode(verifiedCode) ?? {};

              if (currentCodeDetails.isEmpty) {
                debugPrint(
                    '❌ البيانات فارغة في onLoadPdf للكود: [$verifiedCode]');
                return {
                  'success': false,
                  'error':
                      'Code data not found or corrupted for code: $verifiedCode',
                };
              }

              // التحقق الثاني من مطابقة الكود
              String? verifyCode = currentCodeDetails['code_no']?.toString();
              debugPrint('الكود المسترد في onLoadPdf: [$verifyCode]');

              if (verifyCode != verifiedCode) {
                debugPrint('❌ خطأ في المطابقة في onLoadPdf!');
                debugPrint('المطلوب: [$verifiedCode], المسترد: [$verifyCode]');
                return {
                  'success': false,
                  'error':
                      'Code mismatch in PDF generation. Expected: $verifiedCode, Got: $verifyCode',
                };
              }

              debugPrint('✅ تأكيد مطابقة الكود في onLoadPdf: [$verifiedCode]');

              // طباعة معلومات إضافية للتشخيص
              debugPrint('=== معلومات البيانات المستردة ===');
              debugPrint(
                  'المرسل: ${currentCodeDetails['sender_name'] ?? 'غير محدد'}');
              debugPrint(
                  'المستلم: ${currentCodeDetails['receiver_name'] ?? 'غير محدد'}');
              debugPrint(
                  'المدينة: ${currentCodeDetails['city'] ?? 'غير محدد'}');
              debugPrint(
                  'الدولة: ${currentCodeDetails['country'] ?? 'غير محدد'}');
              debugPrint(
                  'تاريخ التحديث: ${currentCodeDetails['updated_at'] ?? 'غير محدد'}');
              debugPrint('=====================================');

              // إنشاء نسخة قابلة للتعديل من البيانات
              Map<String, dynamic> editableCodeDetails =
                  Map<String, dynamic>.from(currentCodeDetails);

              // جلب معلومات الوكيل من المدينة إذا كانت متوفرة
              try {
                String cityName = editableCodeDetails['city'] ?? '';
                String countryName = editableCodeDetails['country'] ?? '';

                if (cityName.isNotEmpty && countryName.isNotEmpty) {
                  Map<String, dynamic>? agentInfo = await codeDataService
                      .getAgentInfoByCity(cityName, countryName);
                  if (agentInfo != null) {
                    // إضافة معلومات الوكيل إلى البيانات القابلة للتعديل
                    editableCodeDetails['agent_name'] =
                        agentInfo['agent_name'] ?? '';
                    editableCodeDetails['agent_phone1'] =
                        agentInfo['agent_phone1'] ?? '';
                    editableCodeDetails['agent_phone2'] =
                        agentInfo['agent_phone2'] ?? '';
                    editableCodeDetails['agent_address'] =
                        agentInfo['agent_address'] ?? '';

                    debugPrint('تم جلب معلومات الوكيل للمدينة: $cityName');
                    debugPrint(
                        'اسم الوكيل: ${editableCodeDetails['agent_name']}');
                    debugPrint(
                        'هاتف الوكيل 1: ${editableCodeDetails['agent_phone1']}');
                    debugPrint(
                        'هاتف الوكيل 2: ${editableCodeDetails['agent_phone2']}');
                    debugPrint(
                        'عنوان الوكيل: ${editableCodeDetails['agent_address']}');
                  } else {
                    debugPrint(
                        'لم يتم العثور على معلومات الوكيل للمدينة: $cityName، الدولة: $countryName');
                  }
                }
              } catch (e) {
                debugPrint('خطأ في جلب معلومات الوكيل: $e');
                // استمر بدون معلومات الوكيل
              }

              // استخدام البيانات القابلة للتعديل بدلاً من الأصلية
              currentCodeDetails = editableCodeDetails;

              // طباعة معلومات الوكيل النهائية قبل إرسالها للـ PDF
              debugPrint('=== معلومات الوكيل النهائية للـ PDF ===');
              debugPrint(
                  'اسم الوكيل: ${currentCodeDetails['agent_name'] ?? 'فارغ'}');
              debugPrint(
                  'هاتف الوكيل 1: ${currentCodeDetails['agent_phone1'] ?? 'فارغ'}');
              debugPrint(
                  'هاتف الوكيل 2: ${currentCodeDetails['agent_phone2'] ?? 'فارغ'}');
              debugPrint(
                  'عنوان الوكيل: ${currentCodeDetails['agent_address'] ?? 'فارغ'}');
              debugPrint('==========================================');

              // الحصول على رقم الشاحنة من قاعدة البيانات
              String truckNo = await codeDataService.getDefaultTruckNo();

              // جلب معلومات الفرع الحالي من المستخدم المسجل
              String branchName = 'الفرع الرئيسي';
              String branchAddress = 'بغداد - الدورة - قرب كلية دجلة';
              String branchPhone = '07702961701 - 07721001999';

              try {
                // استخدام معلومات الفرع المحفوظة
                if (userBranch.isNotEmpty) {
                  // جلب معلومات الفرع من قاعدة البيانات
                  final branchInfo =
                      await codeDataService.getBranchInfo(userBranch);

                  if (branchInfo != null) {
                    branchName = branchInfo['name'] ?? branchName;
                    branchAddress = branchInfo['address'] ?? branchAddress;
                    branchPhone = branchInfo['phone'] ?? branchPhone;

                    debugPrint('معلومات الفرع من قاعدة البيانات:');
                    debugPrint('- الاسم: $branchName');
                    debugPrint('- العنوان: $branchAddress');
                    debugPrint('- الهاتف: $branchPhone');
                  } else {
                    debugPrint(
                        'لم يتم العثور على معلومات الفرع في قاعدة البيانات، استخدام القيم الافتراضية');
                  }
                }
              } catch (e) {
                debugPrint('خطأ في جلب معلومات الفرع: $e');
                // الاستمرار بالقيم الافتراضية
              }

              // توليد ملف PDF باستخدام القالب المخصص لمستند Po
              final pdfBytes = await PdfTemplates.generatePoDocument(
                companyName: 'EUKnet Company',
                branchName: branchName,
                branchAddress: branchAddress,
                branchPhone: branchPhone,
                codeNo: verifiedCode,
                truckNo: truckNo,
                codeData: currentCodeDetails,
              );

              return {
                'success': true,
                'pdfBytes': pdfBytes,
                'copyCount': 1,
              };
            } catch (e) {
              debugPrint('خطأ في إنشاء PDF: $e');
              return {
                'success': false,
                'error': e.toString(),
              };
            }
          },
        ),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء Invoice: $e');
      if (!mounted) return;
      UiHelper.showNotification(
        context,
        messageEn: 'Error creating Invoice',
        isError: true,
        durationSeconds: 2,
      );
    }
  }

  // Build action buttons bar
  Widget _buildActionButtons() {
    return Column(
      children: [
        ActionButtons(
          onSave: saveData,
          onUpdate: updateData,
          onClear: clearFields,
          onInPrint: printOfficeLabel,
          onPoPrint: printInvoice,
          currentData: getCurrentData(),
          context: context,
          isSaveEnabled: isSaveEnabled,
          isUpdateEnabled: isUpdateEnabled,
        ),
      ],
    );
  }

  // تحديث قيمة Post Sub Cost
  @override
  void updatePostSubCost(double value) {
    final costInfoState = costInfoKey.currentState;
    if (costInfoState != null) {
      try {
        (costInfoState as dynamic).updatePostSubCost(value);
      } catch (e) {
        debugPrint('خطأ في تحديث Post Sub Cost: $e');
      }
    }
  }

  // تحديث قيمة Door to Door Cost
  @override
  void updateDoorToDoorCost(double value) {
    debugPrint(
        'تم استدعاء updateDoorToDoorCost في HomeScreen مع القيمة: $value');

    try {
      // استدعاء الدالة في home_screen_calculations_mixin.dart التي تحتوي على منطق الحساب الصحيح
      super.updateDoorToDoorCost(value);

      debugPrint('تم استدعاء updateDoorToDoorCost في الـ mixin بنجاح');
    } catch (e) {
      debugPrint('خطأ في استدعاء updateDoorToDoorCost في الـ mixin: $e');

      // في حالة حدوث خطأ، نحاول تعيين القيمة مباشرة
      final costInfoState = costInfoKey.currentState;
      if (costInfoState != null) {
        try {
          debugPrint('محاولة تعيين Door to Door Cost مباشرة في CostInfo');
          (costInfoState as dynamic).setDoorToDoorCost(value);
        } catch (e2) {
          debugPrint('خطأ في تعيين Door to Door Cost مباشرة: $e2');
        }
      }
    }
  }

  // تحديث حالة ظهور معلومات العنوان
  void updateAddressInfoVisibility(bool isVisible) {
    setState(() {
      // يمكن استخدام هذه الحالة لتحديث أي شيء آخر في الشاشة الرئيسية
      // عندما تتغير حالة ظهور معلومات العنوان
    });

    // يمكن أيضًا تحديث CostInfo إذا كان هناك حاجة لذلك
    final costInfoState = costInfoKey.currentState;
    if (costInfoState != null) {
      try {
        (costInfoState as dynamic).updateAddressInfoVisibility(isVisible);
      } catch (e) {
        debugPrint('خطأ في تحديث حالة ظهور معلومات العنوان: $e');
      }
    }
  }
}
