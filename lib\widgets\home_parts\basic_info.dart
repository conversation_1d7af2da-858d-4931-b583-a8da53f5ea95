import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../providers/riverpod/user_provider.dart';
import 'package:intl/intl.dart';
import '../../services/code_data_service.dart';
import 'package:logging/logging.dart';
import '../../screens/home_screen.dart';
import '../../utils/ui_helper.dart';
import 'dart:math' as math;
import '../../services/event_bus_service.dart';

class BasicInfo extends ConsumerStatefulWidget {
  const BasicInfo({super.key});

  @override
  ConsumerState<BasicInfo> createState() => _BasicInfoState();
}

class _BasicInfoState extends ConsumerState<BasicInfo> {
  // إنشاء مسجل خاص بهذا الكلاس
  final Logger _logger = Logger('BasicInfo');

  final TextEditingController _truckNoController = TextEditingController();
  final TextEditingController _codeNoController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  // قائمة للأكواد المخزنة (ستستبدل بالبيانات الفعلية من قاعدة البيانات)
  final List<Map<String, dynamic>> _savedCodes = [];

  // خدمة بيانات الكود
  final CodeDataService _codeDataService = CodeDataService();

  // مؤشر لحالة تحميل البيانات
  bool _isLoading = false;

  // مؤشر لحالة تفعيل زر الحذف (مرتبط بحالة زر التحديث)
  bool _isDeleteEnabled = false;

  @override
  void initState() {
    super.initState();
    // تعيين تاريخ اليوم فقط إذا لم يكن هناك تاريخ محدد مسبقاً
    if (_dateController.text.isEmpty) {
      _dateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());
    }

    // استدعاء دالة لتوليد كود جديد عند بدء الشاشة
    _initializeNewCode();

    // تعيين قيمة افتراضية لحقل truck_no من قاعدة البيانات
    loadDefaultTruckNo();

    // إضافة مستمع للتغييرات في حالة الشاشة الرئيسية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUpdateButtonState();
    });
  }

  // دالة للتحقق من حالة زر التحديث في الشاشة الرئيسية وتحديث حالة زر الحذف
  void _checkUpdateButtonState() {
    if (!mounted) return;

    try {
      final homeScreenState =
          context.findAncestorStateOfType<State<HomeScreen>>();
      if (homeScreenState != null) {
        // الحصول على حالة زر التحديث
        final isUpdateEnabled = (homeScreenState as dynamic).isUpdateEnabled;

        // تحديث حالة زر الحذف بناءً على حالة زر التحديث
        setState(() {
          _isDeleteEnabled = isUpdateEnabled;
        });

        _logger.info('تم تحديث حالة زر الحذف: $_isDeleteEnabled');
      }
    } catch (e) {
      _logger.warning('خطأ في التحقق من حالة زر التحديث: $e');
    }
  }

  @override
  void dispose() {
    _truckNoController.dispose();
    _codeNoController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  // دالة لتحميل الأكواد المخزنة من قاعدة البيانات
  void _loadSavedCodes() async {
    try {
      _logger.info('بدء تحميل الأكواد المخزنة من قاعدة البيانات');

      setState(() {
        _isLoading = true;
      });

      // استعلام قاعدة البيانات للحصول على الأكواد المخزنة
      List<Map<String, dynamic>> codes =
          await _codeDataService.getAllCodeData();

      _logger.info('تم استرداد ${codes.length} كود من قاعدة البيانات');

      // التحقق من أن الـ widget لا يزال مثبتًا في الشجرة قبل استخدام setState
      if (!mounted) return;

      setState(() {
        _savedCodes.clear();

        // إضافة الأكواد المستردة من قاعدة البيانات
        for (var code in codes) {
          _savedCodes.add({
            'code': code['code_no'],
            'date': code['date'] ?? '',
            'truck_no': code['truck_no'] ?? '',
          });
        }

        // ترتيب الأكواد من الأحدث إلى الأقدم
        _savedCodes.sort((a, b) => b['date'].compareTo(a['date']));

        _isLoading = false;
      });

      _logger.info('تم تحميل ${_savedCodes.length} كود بنجاح');

      // طباعة بعض الأكواد للتحقق
      if (_savedCodes.isNotEmpty) {
        _logger.info('أمثلة على الأكواد المحملة:');
        for (int i = 0; i < math.min(3, _savedCodes.length); i++) {
          _logger.info('كود ${i + 1}: ${_savedCodes[i]['code']}');
        }
      }
    } catch (e, stackTrace) {
      _logger.severe('خطأ في تحميل الأكواد المخزنة: $e');
      _logger.severe('تتبع الخطأ: $stackTrace');

      // التحقق من أن الـ widget لا يزال مثبتًا في الشجرة قبل استخدام context و setState
      if (!mounted) return;

      // عرض رسالة خطأ للمستخدم
      UiHelper.showNotification(
        context,
        messageEn: 'Error loading saved codes: $e',
        isError: true,
        durationSeconds: 4,
      );

      setState(() {
        _isLoading = false;
      });
    }
  }

  // دالة لتحميل بيانات الكود المحدد

  // دالة لإضافة كود جديد إلى قائمة الأكواد المخزنة
  void addCodeToList(String code, String date, String truckNo) {
    setState(() {
      // إضافة الكود الجديد إلى القائمة مع رقم الشاحنة
      _savedCodes.add({'code': code, 'date': date, 'truck_no': truckNo});

      // ترتيب الأكواد من الأحدث إلى الأقدم
      _savedCodes.sort((a, b) => b['date'].compareTo(a['date']));

      // تحديث الكود المحدد
    });
  }

  // دالة لتحميل القيمة الافتراضية لحقل truck_no
  Future<void> loadDefaultTruckNo() async {
    try {
      // الحصول على آخر رقم شاحنة موجود في قاعدة البيانات
      String defaultTruckNo = await _codeDataService.getDefaultTruckNo();

      // التحقق من أن الـ widget لا يزال مثبتًا في الشجرة قبل استخدام setState
      if (!mounted) {
        return;
      }

      setState(() {
        _truckNoController.text = defaultTruckNo;
      });
      _logger.info('تم تحميل رقم الشاحنة الافتراضي: $defaultTruckNo');
    } catch (e) {
      // في حالة حدوث خطأ، تعيين القيمة الافتراضية "1"

      // التحقق من أن الـ widget لا يزال مثبتًا في الشجرة قبل استخدام setState
      if (!mounted) return;

      setState(() {
        _truckNoController.text = "1";
      });
      _logger.warning('حدث خطأ أثناء تحميل رقم الشاحنة الافتراضي: $e');
    }
  }

  // دالة للتحقق من صحة رقم الشاحنة وتطبيق القواعد المطلوبة
  void _validateTruckNo(String value) async {
    if (value.isEmpty) return;

    try {
      int currentTruckNo = int.parse(value);
      int maxTruckNo = await _codeDataService.getMaxTruckNo();

      // السماح بالرقم الحالي (آخر رقم في القاعدة) أو الرقم الجديد (آخر رقم + 1)
      int allowedCurrentTruckNo = maxTruckNo;
      int allowedNextTruckNo = maxTruckNo + 1;

      if (currentTruckNo < allowedCurrentTruckNo) {
        // إذا كان الرقم أقل من الرقم الحالي، نعيد تعيينه للرقم الحالي
        if (mounted) {
          setState(() {
            _truckNoController.text = allowedCurrentTruckNo.toString();
            _truckNoController.selection = TextSelection.fromPosition(
              TextPosition(offset: _truckNoController.text.length),
            );
          });

          // عرض رسالة تنبيه
          UiHelper.showNotification(
            context,
            messageEn:
                "can't use truck number less than $allowedCurrentTruckNo",
            isError: true,
            durationSeconds: 3,
          );
        }
      } else if (currentTruckNo > allowedNextTruckNo) {
        // إذا كان الرقم أكبر من المسموح، نعيد تعيينه للرقم الجديد المسموح
        if (mounted) {
          setState(() {
            _truckNoController.text = allowedNextTruckNo.toString();
            _truckNoController.selection = TextSelection.fromPosition(
              TextPosition(offset: _truckNoController.text.length),
            );
          });

          // عرض رسالة تنبيه
          UiHelper.showNotification(
            context,
            messageEn:
                "can't use truck number greater than $allowedNextTruckNo",
            isError: true,
            durationSeconds: 3,
          );
        }
      }
      // إذا كان الرقم = allowedCurrentTruckNo أو allowedNextTruckNo فهو مقبول
    } catch (e) {
      // في حالة خطأ في التحويل، نعيد تعيين القيمة للافتراضية
      if (mounted) {
        String defaultTruckNo = await _codeDataService.getDefaultTruckNo();
        setState(() {
          _truckNoController.text = defaultTruckNo;
        });
      }
    }
  }

  // دالة للحصول على قيمة Code No
  String getCodeNo() {
    return _codeNoController.text.trim();
  }

  // دالة للحصول على قيمة Truck No
  String getTruckNo() {
    return _truckNoController.text.trim();
  }

  // دالة للحصول على قيمة Date
  String getDate() {
    return _dateController.text.trim();
  }

  // دالة للحصول على قائمة الأكواد المحفوظة
  List<Map<String, dynamic>> getSavedCodes() {
    return _savedCodes;
  }

  // دالة لتعيين قيمة Truck No
  void setTruckNo(String value) {
    _truckNoController.text = value;
  }

  // دالة لتعيين قيمة Code No
  void setCodeNo(String value) {
    setState(() {
      _codeNoController.text = value;
      // تحديث _savedCodes للتأكد من تفعيل أيقونة الحذف
      _updateSavedCodesListForDelete(value);
    });
  }

  // دالة خاصة لتحديث قائمة الأكواد المحفوظة لتفعيل زر الحذف
  void _updateSavedCodesListForDelete(String code) {
    if (code.isEmpty) return;

    // التحقق مما إذا كان الكود موجود في القائمة
    if (!_savedCodes.any((savedCode) => savedCode['code'] == code)) {
      // إضافة الكود إلى القائمة لتفعيل زر الحذف
      _savedCodes.add({'code': code});
    }
  }

  // دالة لتعيين قيمة Date
  void setDate(String value) {
    _dateController.text = value;
  }

  // دالة لحذف السجل الحالي
  void _deleteRecord() async {
    try {
      // الحصول على الكود الحالي
      final codeNo = _codeNoController.text.trim();

      if (codeNo.isEmpty) {
        UiHelper.showNotification(
          context,
          messageEn: "No code specified for deletion",
          isError: true,
          durationSeconds: 2,
        );
        return;
      }

      // عرض مربع حوار للتأكيد
      bool confirmDelete = await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Delete Confirmation'),
              content: Text('Are you sure you want to delete record $codeNo?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child:
                      const Text('Delete', style: TextStyle(color: Colors.red)),
                ),
              ],
            ),
          ) ??
          false;

      if (!confirmDelete) return;

      // حذف السجل من قاعدة البيانات
      final result = await _codeDataService.deleteCodeData(codeNo);

      if (!mounted) return;

      if (result > 0) {
        // عرض رسالة نجاح
        UiHelper.showNotification(
          context,
          messageEn: "Record deleted successfully",
          isError: false,
          durationSeconds: 2,
        );

        // إرسال إشعار للشاشات الأخرى بأن البيانات تم تحديثها
        EventBusService().fireEvent(AppEvent(EventType.dataRefreshNeeded));
        _logger.info('تم إرسال إشعار تحديث البيانات بعد الحذف');

        // توليد كود جديد
        await generateNewCode();

        if (!mounted) return;

        // إعادة تعيين الحقول في الشاشة الرئيسية
        final homeScreenState =
            context.findAncestorStateOfType<State<HomeScreen>>();
        if (homeScreenState != null) {
          (homeScreenState as dynamic).resetAndReloadScreen();
        }

        // إعادة تعيين حقول قسم Cost Info
        _resetCostInfoFields();
      } else {
        // عرض رسالة خطأ
        UiHelper.showNotification(
          context,
          messageEn: "Failed to delete record from database",
          isError: true,
          durationSeconds: 2,
        );
      }
    } catch (e) {
      _logger.severe('error deleting record: $e');

      if (!mounted) return;

      // عرض رسالة خطأ
      UiHelper.showNotification(
        context,
        messageEn: "Error deleting record: $e",
        isError: true,
        durationSeconds: 3,
      );
    }
  }

  // دالة لإعادة تعيين حقول قسم Cost Info
  void _resetCostInfoFields() {
    try {
      // الحصول على مرجع لقسم Cost Info من الشاشة الرئيسية
      final homeScreenState =
          context.findAncestorStateOfType<State<HomeScreen>>();
      if (homeScreenState != null) {
        // الوصول إلى مفتاح Cost Info
        final costInfoKey = (homeScreenState as dynamic).costInfoKey;
        if (costInfoKey != null && costInfoKey.currentState != null) {
          // استدعاء دالة resetFields في حالة Cost Info
          (costInfoKey.currentState as dynamic).resetFields();
          _logger.info('تم إعادة تعيين حقول قسم Cost Info بنجاح');
        } else {
          _logger.warning('لم يتم العثور على حالة Cost Info');
        }
      }
    } catch (e) {
      _logger.severe('خطأ في إعادة تعيين حقول قسم Cost Info: $e');
    }
  }

  // دالة لتوليد كود جديد بناءً على الفرع
  Future<void> _generateNewCode() async {
    final user = ref.read(userProvider);
    final codeFormat = await _getCodeFormat(user.branch);
    String branchPrefix = codeFormat['prefix'] ?? 'XX';
    String codeDigits = codeFormat['digits'] ?? '25';

    // هنا يجب استعلام قاعدة البيانات للحصول على آخر رقم تسلسلي
    // سنستخدم رقم ثابت للتوضيح
    String sequenceNumber = "00001";

    // تنسيق الكود: {رمز الفرع}{أرقام الكود}{رقم تسلسلي}
    setState(() {
      _codeNoController.text = "$branchPrefix$codeDigits$sequenceNumber";
    });
  }

  // دالة للحصول على تنسيق الكود كاملاً
  Future<Map<String, String>> _getCodeFormat(String branchName) async {
    try {
      // الحصول على تنسيق الكود من قاعدة البيانات (المخصص من قبل المستخدم في شاشة الإدارة)
      return await _codeDataService.getBranchCodeFormat(branchName);
    } catch (e) {
      _logger.severe('خطأ في الحصول على تنسيق الكود: $e');

      // هذه القيمة ستستخدم فقط إذا فشل استرجاع التنسيق المخصص من قاعدة البيانات
      return {'prefix': 'XX', 'digits': '00'};
    }
  }

  // دالة عامة لتوليد كود جديد يمكن استدعاؤها من خارج الكلاس
  Future<void> generateNewCode({bool isInitialLoad = false}) async {
    try {
      // استعلام قاعدة البيانات للحصول على جميع الأكواد
      List<Map<String, dynamic>> codes =
          await _codeDataService.getAllCodeData();

      // فلترة السجلات للحصول فقط على السجلات التي تحتوي على اسم المرسل واسم المستلم
      List<Map<String, dynamic>> validCodes = codes.where((code) {
        final senderName = code['sender_name']?.toString().trim() ?? '';
        final receiverName = code['receiver_name']?.toString().trim() ?? '';
        return senderName.isNotEmpty && receiverName.isNotEmpty;
      }).toList();

      _logger.info('عدد السجلات الكلي: ${codes.length}');
      _logger.info(
          'عدد السجلات الصالحة (بها اسم مرسل ومستلم): ${validCodes.length}');

      // استدعاء الدالة الداخلية لتوليد كود جديد بناءً على السجلات الصالحة فقط
      await generateNewCodeFromRecords(validCodes);

      // تحديث المتغير _selectedCode لإلغاء تحديد أي كود
      setState(() {
        // تعيين تاريخ اليوم في حقل التاريخ فقط عند توليد كود جديد حقيقي (ليس عند التحميل الأولي)
        if (!isInitialLoad) {
          _dateController.text =
              DateFormat('yyyy-MM-dd').format(DateTime.now());
        }
        // إعادة تعيين حالة زر الحذف عند توليد كود جديد
        _isDeleteEnabled = false;
      });

      _logger.info('تم توليد كود جديد: ${_codeNoController.text}');
    } catch (e) {
      _logger.severe('خطأ في توليد كود جديد: $e');
      // في حالة حدوث خطأ، استخدم الطريقة البسيطة لتوليد الكود
      await _generateNewCode();
    }
  }

  // دالة لتوليد كود جديد بناءً على السجلات الموجودة
  Future<void> generateNewCodeFromRecords(
      List<Map<String, dynamic>> records) async {
    // ملاحظة: تم تعديل هذه الدالة للعمل على السجلات الصالحة فقط (التي تحتوي على اسم المرسل واسم المستلم)
    final user = ref.read(userProvider);
    final codeFormat = await _getCodeFormat(user.branch);
    String branchPrefix = codeFormat['prefix'] ?? 'XX';
    String codeDigits = codeFormat['digits'] ?? '25';
    String codeFormatPrefix = '$branchPrefix$codeDigits';

    // التحقق مما إذا كانت صيغة الكود قد تغيرت
    bool isCodeFormatChanged =
        await _isCodeFormatChanged(codeFormatPrefix, records);

    // إذا تم تغيير صيغة الكود، نبدأ من الرقم 1
    if (isCodeFormatChanged) {
      // تنسيق الكود الجديد مع بدء الترقيم من 1
      String formattedSequenceNumber = '00001';
      setState(() {
        _codeNoController.text =
            "$branchPrefix$codeDigits$formattedSequenceNumber";
      });
      _logger.info(
          'تم تغيير صيغة الكود، بدء الترقيم من 1: ${_codeNoController.text}');
      return;
    }

    // البحث عن آخر رقم تسلسلي للفرع الحالي
    int maxSequenceNumber = 0;

    // البحث في السجلات عن آخر رقم تسلسلي للفرع الحالي
    for (var record in records) {
      String codeNo = record['code_no'] ?? '';

      // التحقق من أن الكود يبدأ برمز الفرع الحالي متبوعًا بأرقام الكود
      String fullPrefix = '$branchPrefix$codeDigits';
      if (codeNo.startsWith(fullPrefix)) {
        // استخراج الرقم التسلسلي من الكود
        try {
          // نفترض أن الكود بالصيغة: {رمز الفرع}{أرقام الكود}{رقم تسلسلي}
          // حيث رمز الفرع هو حرفين، وأرقام الكود هي رقمين، و5 أرقام للتسلسل
          if (codeNo.length >= 9) {
            // 2 للرمز + 2 للأرقام + 5 للتسلسل
            String sequencePart =
                codeNo.substring(4); // نأخذ الجزء بعد {رمز الفرع}{أرقام الكود}
            int sequenceNumber = int.tryParse(sequencePart) ?? 0;

            if (sequenceNumber > maxSequenceNumber) {
              maxSequenceNumber = sequenceNumber;
            }
          }
        } catch (e) {
          _logger.severe('خطأ في استخراج الرقم التسلسلي: $e');
        }
      }
    }

    // زيادة الرقم التسلسلي بمقدار 1
    int nextSequenceNumber = maxSequenceNumber + 1;

    // تنسيق الرقم التسلسلي ليكون بطول 5 أرقام
    String formattedSequenceNumber =
        nextSequenceNumber.toString().padLeft(5, '0');

    // تنسيق الكود: {رمز الفرع}{أرقام الكود}{رقم تسلسلي}
    setState(() {
      _codeNoController.text =
          "$branchPrefix$codeDigits$formattedSequenceNumber";
    });
  }

  // دالة للتحقق مما إذا كانت صيغة الكود قد تغيرت
  Future<bool> _isCodeFormatChanged(
      String currentCodePrefix, List<Map<String, dynamic>> records) async {
    // إذا لم تكن هناك سجلات سابقة، فهذا تنسيق جديد
    if (records.isEmpty) {
      return false;
    }

    // البحث عن أحدث سجل بنفس بادئة الكود
    Map<String, dynamic>? latestRecordWithSamePrefix;
    DateTime latestDateTime = DateTime(1970);

    // استخدام حقل التاريخ للمقارنة إذا كان موجودًا
    for (var record in records) {
      String createdAt = record['created_at'] ?? '';

      if (createdAt.isNotEmpty) {
        try {
          DateTime recordDateTime = DateTime.parse(createdAt);
          if (recordDateTime.isAfter(latestDateTime)) {
            latestDateTime = recordDateTime;
            latestRecordWithSamePrefix = record;
          }
        } catch (e) {
          _logger.severe('خطأ في تحليل تاريخ السجل: $e');
        }
      }
    }

    // التحقق مما إذا كان أحدث سجل يبدأ بنفس البادئة
    if (latestRecordWithSamePrefix != null) {
      String codeNo = latestRecordWithSamePrefix['code_no'] ?? '';
      // التحقق من أن الكود يبدأ ببادئة مختلفة عن البادئة الحالية
      return !codeNo.startsWith(currentCodePrefix);
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    // تحديث حالة زر الحذف بناءً على حالة زر التحديث في الشاشة الرئيسية
    _checkUpdateButtonState();
    // إضافة مستمع للكيبورد للكشف عن CTRL+F
    return Focus(
      onKeyEvent: (FocusNode node, KeyEvent event) {
        // الكشف عن ضغطة CTRL+F
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.keyF &&
            (HardwareKeyboard.instance.isControlPressed ||
                HardwareKeyboard.instance.isMetaPressed)) {
          _logger.info('تم اكتشاف ضغطة CTRL+F في BasicInfo widget');
          _logger.info('التحقق من مصدر النافذة المنبثقة...');

          // طباعة معلومات عن الويدجت الحالي
          _logger.info('الويدجت الحالي: $runtimeType');
          _logger.info(
              'الويدجت الأب: ${context.findAncestorWidgetOfExactType<Scaffold>()?.runtimeType}');

          // التحقق من وجود مستمعات أخرى للكيبورد
          _logger.info(
              'هل يوجد KeyboardListener في الشجرة؟ ${context.findAncestorWidgetOfExactType<KeyboardListener>() != null}');

          // طباعة معلومات عن الحالة الحالية
          _logger.info('حالة _isLoading: $_isLoading');
          _logger.info('عدد العناصر في _savedCodes: ${_savedCodes.length}');

          return KeyEventResult.handled; // منع انتشار الحدث
        }
        return KeyEventResult.ignored; // السماح بانتشار الحدث
      },
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Card(
              elevation: 4.r,
              margin: EdgeInsets.all(6.r),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // المجموعة الأولى من الحقول
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // حقل Truck No
                          Row(
                            children: [
                              // تقليل حجم Truck No ليكون بنفس حجم Code No
                              Expanded(
                                child: TextField(
                                  controller: _truckNoController,
                                  decoration: InputDecoration(
                                    labelText: 'Truck No',
                                    border: const OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 12.h),
                                  ),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14.sp,
                                  ),
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  onChanged: _validateTruckNo,
                                ),
                              ),
                              // إضافة مساحة فارغة في نهاية الصف للمساواة مع زر الحذف في Code No
                              const IconButton(
                                icon: Icon(Icons.arrow_forward,
                                    color: Colors.transparent),
                                onPressed: null,
                              ),
                            ],
                          ),
                          SizedBox(height: 12.h),

                          // حقل Code No مع زر الحذف
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _codeNoController,
                                  decoration: InputDecoration(
                                    labelText: 'Code No',
                                    border: const OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 12.h),
                                    filled: true,
                                    fillColor: const Color(0xFFF5F5F5),
                                  ),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14.sp,
                                  ),
                                  readOnly: true,
                                ),
                              ),
                              // زر الحذف
                              IconButton(
                                icon: Icon(
                                  Icons.delete,
                                  color: _isDeleteEnabled
                                      ? Colors.red
                                      : Colors.grey,
                                ),
                                tooltip: _isDeleteEnabled
                                    ? 'Delete Record'
                                    : 'Cannot delete until record is loaded',
                                onPressed:
                                    _isDeleteEnabled ? _deleteRecord : null,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    SizedBox(width: 16.w),

                    // المجموعة الثانية من الحقول
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // صف التاريخ: تاريخ حفظ السجل وتاريخ اليوم
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // حقل تاريخ حفظ السجل
                              Expanded(
                                flex: 1,
                                child: TextField(
                                  controller: _dateController,
                                  decoration: InputDecoration(
                                    labelText: 'Record Date',
                                    border: const OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 12.h),
                                    filled: true,
                                    fillColor: const Color(0xFFF5F5F5),
                                    labelStyle: TextStyle(fontSize: 12.sp),
                                  ),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  readOnly: true,
                                ),
                              ),
                              SizedBox(width: 8.w),
                              // حقل تاريخ اليوم بتصميم جديد أفقي مع أيقونة
                              Expanded(
                                flex: 1,
                                child: Container(
                                  height: 48.h,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFF4295D1), // أزرق واضح
                                        Color(
                                            0xFF4295D1), // نفس اللون للتقليل من التدرج
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(4.r),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.blue.withAlpha(40),
                                        spreadRadius: 1.r,
                                        blurRadius: 3.r,
                                        offset: Offset(0, 1.h),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: const Color(0xFF3F8BC2),
                                      width: 1.0.w,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 4.h),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.calendar_today,
                                          color: Colors.white,
                                          size: 20.sp,
                                        ),
                                        SizedBox(width: 8.w),
                                        Text(
                                          DateFormat('yyyy-MM-dd')
                                              .format(DateTime.now()),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.bold,
                                            letterSpacing: 0.5,
                                            shadows: [
                                              Shadow(
                                                blurRadius: 1.0.r,
                                                color: const Color(0x80FFFFFF),
                                                offset: Offset(0, 1.h),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12.h),

                          // قائمة الأكواد المخزنة - تم استبدالها بزر البحث وجملة تعليمات البحث
                          Row(
                            children: [
                              // نص تعليمات البحث داخل مربع مع أيقونة البحث
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    // استخدام الدالة الساكنة للوصول إلى دالة البحث
                                    HomeScreen.showSearch(context);
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 12.h, horizontal: 16.w),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.black),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.search,
                                          color: Colors.blue,
                                          size: 22.sp,
                                        ),
                                        SizedBox(width: 10.w),
                                        Text(
                                          'Or press CTRL + F to search',
                                          style: TextStyle(
                                            color: Colors.grey.shade700,
                                            fontSize: 14.sp,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  // Function to initialize a new code when starting the application
  void _initializeNewCode() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logger.info('بدء تهيئة BasicInfo widget');

      // تحميل الأكواد المحفوظة أولاً
      _loadSavedCodes();

      // تحديد ما إذا كنا قادمين من شاشة تسجيل الدخول
      try {
        // التحقق من الطريق الحالي
        final route = ModalRoute.of(context);
        if (route != null && route.settings.name == '/home') {
          _logger.info('تم تحديد أننا جئنا من شاشة تسجيل الدخول');
        }
      } catch (e) {
        _logger.warning('خطأ في تحديد الطريق: $e');
      }

      // توليد كود جديد مع تعيين isInitialLoad إلى true لمنع ظهور الإشعارات
      // نضمن تمرير isInitialLoad = true سواء كنا في التحميل الأولي أو قادمين من شاشة تسجيل الدخول
      generateNewCode(isInitialLoad: true).then((_) {
        // التحقق من أن الـ widget لا يزال مثبتًا قبل المتابعة
        if (!mounted) return;

        // طباعة معلومات عن شجرة الويدجت
        _logger.info('تم توليد كود جديد بنجاح');
        _logger.info('التحقق من وجود مستمعات للكيبورد في الشجرة...');

        // التحقق من شجرة الويدجت بعد تأخير قصير
        Future.delayed(const Duration(milliseconds: 500), () {
          // التحقق من أن الـ widget لا يزال مثبتًا قبل استخدام context
          if (!mounted) return;

          final scaffold = context.findAncestorWidgetOfExactType<Scaffold>();
          _logger.info('الويدجت الأب Scaffold: ${scaffold != null}');

          // التحقق من وجود Focus في الشجرة
          final focusParent = context.findAncestorWidgetOfExactType<Focus>();
          _logger.info('هل يوجد Focus في الشجرة؟ ${focusParent != null}');

          // التحقق من وجود KeyboardListener في الشجرة
          final keyboardListener =
              context.findAncestorWidgetOfExactType<KeyboardListener>();
          _logger.info(
              'هل يوجد KeyboardListener في الشجرة؟ ${keyboardListener != null}');

          // تم إزالة التحقق من زر الحذف لأنه تم إزالة الزر
        });
      }).catchError((error) {
        // معالجة الخطأ
        if (!mounted) return;

        _logger.severe('خطأ في توليد كود جديد أثناء بدء التطبيق: $error');

        // عرض إشعار خطأ فقط إذا حدث خطأ حقيقي
        UiHelper.showNotification(
          context,
          messageEn: 'Error in generating new code: $error',
          isError: true,
          durationSeconds: 4,
        );
      });
    });
  }
}
