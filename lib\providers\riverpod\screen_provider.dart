import 'package:flutter_riverpod/flutter_riverpod.dart';

/// نموذج يمثل حالة الشاشة
class ScreenState {
  final int selectedIndex;

  ScreenState({this.selectedIndex = 0});

  ScreenState copyWith({int? selectedIndex}) {
    return ScreenState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
    );
  }
}

/// مزود حالة الشاشة المحددة
class ScreenNotifier extends StateNotifier<ScreenState> {
  ScreenNotifier() : super(ScreenState());

  /// تعيين مؤشر الشاشة المحددة
  void setSelectedIndex(int index) {
    state = state.copyWith(selectedIndex: index);
  }
}

/// مزود Riverpod للشاشة
final screenProvider =
    StateNotifierProvider<ScreenNotifier, ScreenState>((ref) {
  return ScreenNotifier();
});
