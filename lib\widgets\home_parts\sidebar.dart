import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../providers/riverpod/user_provider.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';

class Sidebar extends ConsumerStatefulWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const Sidebar({
    super.key,
    required this.selectedIndex,
    required this.onItemSelected,
  });

  @override
  ConsumerState<Sidebar> createState() => _SidebarState();
}

class _SidebarState extends ConsumerState<Sidebar> {
  File? _profileImage;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
  }

  // دالة لتحميل صورة الملف الشخصي المحفوظة
  Future<void> _loadProfileImage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final imagePath = prefs.getString('profile_image_path');

      if (imagePath != null) {
        final file = File(imagePath);
        if (await file.exists()) {
          setState(() {
            _profileImage = file;
          });
        }
      }
    } catch (e) {
      // خطأ في تحميل صورة الملف الشخصي
    }
  }

  // دالة لاختيار صورة من المعرض
  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          // نسخ الصورة إلى مجلد التطبيق للحفظ الدائم
          final appDir = await getApplicationDocumentsDirectory();
          final fileName =
              'profile_${DateTime.now().millisecondsSinceEpoch}.jpg';
          final savedImage = await File(path).copy('${appDir.path}/$fileName');

          // حفظ مسار الصورة في التفضيلات المشتركة
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('profile_image_path', savedImage.path);

          setState(() {
            _profileImage = savedImage;
          });
        }
      }
    } catch (e) {
      // خطأ في اختيار الصورة
    }
  }

  // دالة لحذف صورة الملف الشخصي
  Future<void> _deleteProfileImage() async {
    try {
      // عرض مربع حوار للتأكيد
      final bool? confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Delete Profile Image'),
          content:
              const Text('Are you sure you want to delete the profile image?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirm == true && _profileImage != null) {
        // حذف الصورة من التخزين إذا كانت موجودة
        try {
          await _profileImage!.delete();
        } catch (e) {
          // خطأ في حذف ملف الصورة
        }

        // حذف المسار من التفضيلات المشتركة
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('profile_image_path');

        // تحديث واجهة المستخدم
        setState(() {
          _profileImage = null;
        });
      }
    } catch (e) {
      // خطأ في حذف صورة الملف الشخصي
    }
  }

  // دالة لعرض نافذة معاينة الصورة مع خيارات الحذف والتغيير
  void _showImagePreview() {
    if (_profileImage == null) {
      // إذا لم تكن هناك صورة، استدعاء دالة اختيار صورة مباشرة
      _pickImage();
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Container(
          width: 500,
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 30),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عنوان النافذة
              Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: Text(
                  'Profile Image',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),

              // الصورة بحجم أكبر
              Container(
                constraints: const BoxConstraints(
                  maxHeight: 400,
                  maxWidth: 400,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(50),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Image.file(
                    _profileImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              const SizedBox(height: 30),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // زر الحذف
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context); // إغلاق نافذة المعاينة
                      _deleteProfileImage(); // استدعاء دالة الحذف
                    },
                    icon:
                        const Icon(Icons.delete, color: Colors.white, size: 22),
                    label: const Text(
                      'Delete',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),

                  // زر اختيار صورة أخرى
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context); // إغلاق نافذة المعاينة
                      _pickImage(); // استدعاء دالة اختيار صورة
                    },
                    icon: const Icon(Icons.photo_library, size: 22),
                    label: const Text(
                      'Change Image',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider);

    return SizedBox(
      width: 250.w,
      child: Material(
        color: Colors.white,
        child: Column(
          children: [
            // الجزء العلوي القابل للتمرير
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  Container(
                    height: 150.h,
                    padding: EdgeInsets.all(16.r),
                    alignment: Alignment.center,
                    decoration:
                        BoxDecoration(color: Theme.of(context).primaryColor),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.local_shipping_outlined,
                            size: 45.sp,
                            color: Colors.white,
                          ),
                          SizedBox(height: 8.h),
                          Flexible(
                            child: Text(
                              'EUKnet Company',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Flexible(
                            child: Text(
                              'International Transport',
                              style: TextStyle(
                                  color: Colors.white70, fontSize: 11.sp),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // قسم معلومات المستخدم - تصميم جديد
                  Container(
                    padding:
                        EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.grey.shade50,
                          Colors.grey.shade100,
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(13),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // صورة المستخدم مع إمكانية التغيير
                        GestureDetector(
                          onTap: _showImagePreview,
                          child: Container(
                            width: 90.w,
                            height: 90.h,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withAlpha(77),
                                  blurRadius: 12,
                                  spreadRadius: 2,
                                ),
                              ],
                              image: _profileImage != null
                                  ? DecorationImage(
                                      image: FileImage(_profileImage!),
                                      fit: BoxFit.cover,
                                    )
                                  : null,
                            ),
                            child: _profileImage == null
                                ? Icon(
                                    Icons.person,
                                    size: 50.sp,
                                    color: Theme.of(context).primaryColor,
                                  )
                                : null,
                          ),
                        ),
                        SizedBox(height: 16.h),

                        // اسم المستخدم مع الصلاحية
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: user.username,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18.sp,
                                  color: Colors.black,
                                ),
                              ),
                              TextSpan(
                                text: ' - ${_getRoleDisplayName(user.role)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16.sp,
                                  color: _getRoleColor(user.role),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 4.h),

                        // البريد الإلكتروني (افتراضي)
                        Text(
                          '${user.username.toLowerCase()}@euknet.com',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        SizedBox(height: 12.h),

                        // الفرع
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 8.h),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Theme.of(context).primaryColor.withAlpha(179),
                                Theme.of(context).primaryColor,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withAlpha(77),
                                blurRadius: 8,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.location_on,
                                size: 16.sp,
                                color: Colors.white,
                              ),
                              SizedBox(width: 6.w),
                              Text(
                                user.branch,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 10.h),

                  // Menu Items
                  _buildMenuItem(
                    context,
                    index: 0,
                    title: 'Home',
                    icon: Icons.home_outlined,
                  ),
                  _buildMenuItem(
                    context,
                    index: 1,
                    title: 'Reports',
                    icon: Icons.bar_chart_outlined,
                  ),
                  // إظهار خيار Admin فقط للأدمن
                  if (user.role == 'admin')
                    _buildMenuItem(
                      context,
                      index: 2,
                      title: 'Admin',
                      icon: Icons.admin_panel_settings_outlined,
                    ),
                  _buildMenuItem(
                    context,
                    index: user.role == 'admin' ? 3 : 2,
                    title: 'Settings',
                    icon: Icons.settings_outlined,
                  ),
                ],
              ),
            ),

            // زر تسجيل الخروج - ثابت في الأسفل
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: ListTile(
                leading: const Icon(Icons.logout, color: Colors.red),
                title: const Text(
                  'Logout',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.red.withAlpha(76)),
                ),
                onTap: () {
                  // عرض مربع حوار للتأكيد
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Logout Confirmation'),
                      content: const Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Are you sure you want to logout?',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context); // إغلاق مربع الحوار
                            Navigator.pushReplacementNamed(context,
                                '/login'); // الانتقال إلى شاشة تسجيل الدخول
                          },
                          child: const Text('Logout',
                              style: TextStyle(color: Colors.red)),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  // دالة لإرجاع اسم الصلاحية المعروض
  String _getRoleDisplayName(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Admin';
      case 'manager':
        return 'Manager';
      case 'user':
        return 'User';
      default:
        return 'User';
    }
  }

  // دالة لإرجاع لون الصلاحية
  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red.shade600;
      case 'manager':
        return Colors.orange.shade600;
      case 'user':
        return Colors.green.shade600;
      default:
        return Colors.green.shade600;
    }
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required int index,
    required String title,
    required IconData icon,
  }) {
    final isSelected = widget.selectedIndex == index;
    final primaryColor = Theme.of(context).primaryColor;
    final textColor = isSelected ? primaryColor : Colors.black87;

    // تحديد لون مخصص لكل أيقونة بناءً على النوع
    Color iconColor;
    if (isSelected) {
      iconColor = primaryColor;
    } else {
      switch (title.toLowerCase()) {
        case 'home':
          iconColor = Colors.green.shade600;
          break;
        case 'reports':
          iconColor = Colors.blue.shade600;
          break;
        case 'admin':
          iconColor = Colors.red.shade600;
          break;
        case 'settings':
          iconColor = Colors.orange.shade600;
          break;
        default:
          iconColor = Colors.grey.shade600;
      }
    }

    return ListTile(
      selected: isSelected,
      leading: Icon(icon, color: iconColor),
      title: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      onTap: () => widget.onItemSelected(index),
    );
  }
}
