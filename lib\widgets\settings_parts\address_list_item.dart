import 'package:flutter/material.dart';

class AddressListItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback? onDelete;

  const AddressListItem({
    super.key,
    required this.title,
    required this.subtitle,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: onDelete != null
            ? IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: onDelete,
              )
            : null,
      ),
    );
  }
}
