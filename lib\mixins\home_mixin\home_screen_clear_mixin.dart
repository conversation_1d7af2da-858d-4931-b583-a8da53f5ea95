import 'package:flutter/material.dart';

/// مزيج يحتوي على دوال مسح الحقول للشاشة الرئيسية

mixin HomeScreenClearMixin<T extends StatefulWidget> on State<T> {
  // الحصول على مراجع المكونات

  GlobalKey get basicInfoKey;

  GlobalKey get senderInfoKey;

  GlobalKey get receiverInfoKey;

  GlobalKey get weightInfoKey;

  GlobalKey get costInfoKey;

  GlobalKey get notesKey;

  // متغيرات التحكم في حالة الأزرار

  bool get isSaveEnabled;

  set isSaveEnabled(bool value);

  bool get isUpdateEnabled;

  set isUpdateEnabled(bool value);

  // متغير لتخزين قيمة For each 1 Kg

  double get forEachKgValue;

  set forEachKgValue(double value);

  // متغير لتخزين قيمة Minimum price

  double get minimumPriceValue;

  set minimumPriceValue(double value);

  // دالة توليد كود جديد

  Future<void> generateNewCode();

  // دالة تستدعى عند تغيير الوزن الحقيقي
  void onRealWeightChanged();

  // دالة مسح الحقول

  void clearFields({bool loadDefaultTruckNo = true}) {
    try {
      // لا نقوم بتعيين قيمة For each 1 Kg إلى صفر
      // forEachKgValue = 0.0;

      // مسح حقول BasicInfo
      _clearBasicInfo(loadDefaultTruckNo);

      // مسح حقول SenderInfo
      _clearSenderInfo();

      // مسح حقول ReceiverInfo
      _clearReceiverInfo();

      // تحديث قيمة Minimum price قبل مسح الحقول
      final receiverInfoState = receiverInfoKey.currentState;
      if (receiverInfoState != null) {
        try {
          // محاولة الحصول على قيمة Minimum price
          minimumPriceValue = (receiverInfoState as dynamic).getMinimumPrice();
        } catch (e) {
          // إذا لم تكن الدالة موجودة، نستخدم القيمة الافتراضية
          minimumPriceValue = 35000.0;
        }
      }

      // مسح حقول WeightInfo
      _clearWeightInfo();

      // مسح حقول CostInfo - يجب أن يكون بعد مسح WeightInfo
      _clearCostInfo();

      // مسح حقل الملاحظات
      _clearNotes();

      // إعادة تفعيل زر الحفظ وتعطيل زر التحديث
      setState(() {
        isSaveEnabled = true;
        isUpdateEnabled = false;
      });

      // تحميل آخر رقم شاحنة في النهاية للتأكد من عرضه فقط إذا كان مطلوبًا
      if (loadDefaultTruckNo) {
        Future.delayed(const Duration(milliseconds: 200), () async {
          final basicInfoState = basicInfoKey.currentState;
          if (basicInfoState != null) {
            try {
              await (basicInfoState as dynamic).loadDefaultTruckNo();
              debugPrint('تم تحميل آخر رقم شاحنة بنجاح في نهاية clearFields');
            } catch (e) {
              debugPrint('خطأ في تحميل آخر رقم شاحنة في نهاية clearFields: $e');
            }
          }
        });
      }
    } catch (e) {
      // تم إزالة عرض رسالة الخطأ هنا
      debugPrint('خطأ أثناء مسح الحقول: $e');
    }
  }

  // مسح حقول BasicInfo

  void _clearBasicInfo(bool loadDefaultTruckNo) {
    final basicInfoState = basicInfoKey.currentState;

    if (basicInfoState != null) {
      // إذا كان loadDefaultTruckNo = true، فهذا يعني أننا ننشئ كود جديد حقيقي
      // إذا كان loadDefaultTruckNo = false، فهذا يعني أننا نفتح كود موجود للعرض
      if (loadDefaultTruckNo) {
        // استدعاء دالة توليد كود جديد وعرضه في حقل Code No فقط للكود الجديد
        generateNewCode().then((_) async {
          // إضافة تأخير قصير لضمان اكتمال عملية مسح الحقول
          await Future.delayed(const Duration(milliseconds: 100));

          // تحميل آخر رقم شاحنة بعد توليد الكود الجديد
          try {
            await (basicInfoState as dynamic).loadDefaultTruckNo();
            debugPrint('تم تحميل آخر رقم شاحنة بنجاح في Clear');
          } catch (e) {
            debugPrint('خطأ في تحميل آخر رقم شاحنة: $e');
          }
        }).catchError((error) {
          // تسجيل الخطأ في وحدة التحكم فقط
          debugPrint('خطأ في توليد كود جديد: $error');
        });
      } else {
        // عند فتح كود موجود للعرض، فقط امسح الحقول دون توليد كود جديد
        try {
          (basicInfoState as dynamic).setCodeNo('');
          (basicInfoState as dynamic).setTruckNo('');
          // لا نعين تاريخ اليوم هنا لأن البيانات ستُحمل من قاعدة البيانات
          (basicInfoState as dynamic).setDate('');
          debugPrint('تم مسح حقول BasicInfo للعرض دون توليد كود جديد');
        } catch (e) {
          debugPrint('خطأ في مسح حقول BasicInfo للعرض: $e');
        }
      }
    }
  }

  // مسح حقول SenderInfo

  void _clearSenderInfo() {
    final senderInfoState = senderInfoKey.currentState;

    if (senderInfoState != null) {
      try {
        (senderInfoState as dynamic).setSenderName('');

        (senderInfoState as dynamic).setSenderPhone('');

        // تفريغ قيمة حقل sender id

        try {
          (senderInfoState as dynamic).setSenderId('');
        } catch (e) {
          // تجاهل الخطأ
        }

        (senderInfoState as dynamic).setGoodsDescription('');

        // مسح العناصر المختارة في قائمة البضائع

        (senderInfoState as dynamic).clearSelectedItems();

        // مسح مسار صورة الهوية

        (senderInfoState as dynamic).setSenderIdImagePath('');

        // مسح نوع الهوية

        (senderInfoState as dynamic).setSenderIdType('');
      } catch (e) {
        // تجاهل الخطأ
      }
    }
  }

  // مسح حقول ReceiverInfo

  void _clearReceiverInfo() {
    try {
      final receiverInfoState = receiverInfoKey.currentState;

      if (receiverInfoState != null) {
        try {
          (receiverInfoState as dynamic).setReceiverName('');

          (receiverInfoState as dynamic).setReceiverPhone('');

          // إعادة تعيين الدولة والمدينة إلى القيم الافتراضية

          (receiverInfoState as dynamic)
              .setCountryAndCity('Germany', 'Duisburg');

          // لا نقوم بإعادة تعيين قيمة Exchange Rate إلى القيمة الافتراضية
          // سيتم تحميل سعر الصرف من قاعدة البيانات للدولة المختارة
        } catch (e) {
          // تجاهل الخطأ
        }

        // مسح حقول العنوان إذا كانت مفعلة

        try {
          final addressInfoState =
              (receiverInfoState as dynamic).getAddressInfoState();

          if (addressInfoState != null) {
            (addressInfoState as dynamic).resetAllFields();
          }

          // إعادة تعيين مربعات الاختيار إلى الوضع الافتراضي (غير مفعل)

          (receiverInfoState as dynamic).setAddressInfoVisible(false);
        } catch (e) {
          // تجاهل الخطأ
        }

        // مسح حقول PriceInfo

        try {
          final priceInfoState =
              (receiverInfoState as dynamic).getPriceInfoState();

          if (priceInfoState != null) {
            (priceInfoState as dynamic).setDoorToDoorPrice(0.0);

            // لا نقوم بإعادة تعيين قيمة Exchange Rate إلى القيمة الافتراضية
            // سيتم تحميل سعر الصرف من قاعدة البيانات للدولة المختارة
            (priceInfoState as dynamic).clearPriceFields();
          }
        } catch (e) {
          // تجاهل الخطأ
        }
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // مسح حقول WeightInfo

  void _clearWeightInfo() {
    try {
      final weightInfoState = weightInfoKey.currentState;

      if (weightInfoState != null) {
        // تفريغ حقول Box No و Pallet No و Real weight KG

        (weightInfoState as dynamic).setBoxNo(0);

        (weightInfoState as dynamic).setPalletNo(0);

        (weightInfoState as dynamic).setRealWeight(0.0);

        (weightInfoState as dynamic).setAdditionalKg(0.0);

        // إعادة خيار الأبعاد إلى الوضع الافتراضي (غير مفعل)

        (weightInfoState as dynamic).setUseDimensions(false);

        // لا نقوم باستدعاء دالة onRealWeightChanged لضمان عدم تحديث قيمة post sub cost
        // نريد أن تبقى قيمة post sub cost صفر بعد الضغط على زر Clear
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // مسح حقول CostInfo

  void _clearCostInfo() {
    try {
      final costInfoState = costInfoKey.currentState;

      if (costInfoState != null) {
        // استدعاء دالة resetFields الموجودة في ملف cost_info.dart
        (costInfoState as dynamic).resetFields();

        // لا نقوم بتحديث قيمة Post sub cost بعد مسح الحقول
        // نريد أن تبقى قيمة post sub cost صفر بعد الضغط على زر Clear
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // مسح حقل الملاحظات

  void _clearNotes() {
    try {
      final notesState = notesKey.currentState;

      if (notesState != null) {
        (notesState as dynamic).setNotes('');
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
