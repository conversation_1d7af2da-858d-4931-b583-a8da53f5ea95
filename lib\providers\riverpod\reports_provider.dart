import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/database_helper.dart';
import '../../utils/constants.dart';
import 'database_provider.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

/// State class for reports
class ReportsState {
  final Map<String, dynamic> data;
  final List<Map<String, dynamic>> rawData; // إضافة البيانات الخام
  final DateTime? startDate;
  final DateTime? endDate;
  final String? truckNo;
  final bool isLoading;
  final String? error;

  ReportsState({
    required this.data,
    required this.rawData,
    this.startDate,
    this.endDate,
    this.truckNo,
    this.isLoading = false,
    this.error,
  });

  ReportsState copyWith({
    Map<String, dynamic>? data,
    List<Map<String, dynamic>>? rawData,
    DateTime? startDate,
    DateTime? endDate,
    String? truckNo,
    bool? isLoading,
    String? error,
  }) {
    return ReportsState(
      data: data ?? this.data,
      rawData: rawData ?? this.rawData,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      truckNo: truckNo ?? this.truckNo,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  factory ReportsState.initial() {
    // Set default date range to last 30 days
    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 30));

    return ReportsState(
      data: {},
      rawData: [],
      startDate: startDate,
      endDate: now,
      isLoading: true,
    );
  }
}

/// Notifier for reports data
class ReportsNotifier extends StateNotifier<ReportsState> {
  final DatabaseHelper _dbHelper;
  final Logger _logger = Logger('ReportsNotifier');

  ReportsNotifier(this._dbHelper) : super(ReportsState.initial()) {
    loadReportData();
  }

  /// Loads report data with current filters
  Future<void> loadReportData() async {
    _logger.info('بدء تحميل بيانات التقرير...');
    _logger.info('حالة الفلتر الحالية: ${state.toString()}');

    // تعيين حالة التحميل
    state = state.copyWith(isLoading: true, error: null);

    try {
      // بناء شروط الاستعلام
      String whereClause = '';
      List<dynamic> whereArgs = [];

      // إضافة شروط التاريخ إذا كانت محددة
      if (state.startDate != null && state.endDate != null) {
        _logger.info('شروط التاريخ: date BETWEEN ? AND ?');
        _logger.info(
            'قيم التاريخ: [${DateFormat('yyyy-MM-dd').format(state.startDate!)}, ${DateFormat('yyyy-MM-dd').format(state.endDate!.add(const Duration(days: 1)))}]');

        whereClause += 'date BETWEEN ? AND ?';
        whereArgs.add(DateFormat('yyyy-MM-dd').format(state.startDate!));
        whereArgs.add(DateFormat('yyyy-MM-dd')
            .format(state.endDate!.add(const Duration(days: 1))));
      }

      _logger.info('قيمة truckNo الحالية: ${state.truckNo}');

      // إضافة شرط رقم الشاحنة فقط إذا كان محددًا (ليس null)
      if (state.truckNo != null) {
        _logger.info('شرط الشاحنة: truck_no = ${state.truckNo}');

        if (whereClause.isNotEmpty) {
          whereClause += ' AND ';
        }
        whereClause += 'truck_no = ?';
        whereArgs.add(state.truckNo);
        _logger.info('تمت إضافة شرط الشاحنة: truck_no = ${state.truckNo}');
      } else {
        _logger.info(
            'لم تتم إضافة شرط الشاحنة لأن truckNo هو: null (جميع الشاحنات)');
      }

      _logger.info('الاستعلام النهائي: $whereClause, $whereArgs');

      // استرجاع البيانات من قاعدة البيانات
      final codeData = await _getFilteredCodeData(whereClause, whereArgs);

      // طباعة عدد السجلات المسترجعة
      _logger.info('عدد السجلات المسترجعة: ${codeData.length}');
      if (codeData.isNotEmpty) {
        _logger.info('نموذج من البيانات: ${codeData.first}');
      }

      // حساب الإحصائيات
      final statistics = _calculateReportStatistics(codeData);

      // طباعة الإحصائيات المحسوبة
      _logger.info('الإحصائيات المحسوبة: $statistics');

      // تحديث الحالة بالبيانات الجديدة
      state = state.copyWith(
        data: statistics,
        rawData: codeData,
        isLoading: false,
        error: null,
      );

      _logger.info('تم تحديث الحالة بنجاح مع ${codeData.length} سجل');
    } catch (e, stackTrace) {
      _logger.severe('حدث خطأ أثناء تحميل البيانات: $e', e, stackTrace);

      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحميل البيانات: ${e.toString()}',
      );
    }
  }

  /// Gets filtered code data from database
  Future<List<Map<String, dynamic>>> _getFilteredCodeData(
    String whereClause,
    List<dynamic> whereArgs,
  ) async {
    final db = await _dbHelper.database;

    try {
      _logger.info('استعلام قاعدة البيانات:');
      _logger.info('جدول: ${DbConstants.tableCodeData}');
      _logger.info('شرط: $whereClause');
      _logger.info('قيم الشرط: $whereArgs');

      List<Map<String, dynamic>> result;

      if (whereClause.isEmpty) {
        result = await db.query(DbConstants.tableCodeData);
      } else {
        result = await db.query(
          DbConstants.tableCodeData,
          where: whereClause,
          whereArgs: whereArgs,
        );
      }

      _logger.info('تم استرجاع ${result.length} سجل');

      // طباعة أول سجل للتحقق
      if (result.isNotEmpty) {
        _logger.info('نموذج من البيانات المسترجعة:');
        _logger.info('${result.first}');
      }

      return result;
    } catch (e) {
      _logger.severe('خطأ في استعلام قاعدة البيانات: $e');
      return [];
    }
  }

  /// Calculates report statistics from code data
  Map<String, dynamic> _calculateReportStatistics(
    List<Map<String, dynamic>> codeData,
  ) {
    // طباعة للتحقق من البيانات المدخلة
    _logger.info('حساب الإحصائيات لـ ${codeData.length} سجل');

    // Count unique truck numbers
    final Set<String> uniqueTruckNumbers = {};
    for (final data in codeData) {
      if (data['truck_no'] != null && data['truck_no'].toString().isNotEmpty) {
        uniqueTruckNumbers.add(data['truck_no'].toString());
      }
    }

    // طباعة أرقام الشاحنات الفريدة
    _logger.info('أرقام الشاحنات الفريدة: $uniqueTruckNumbers');

    // Count boxes and pallets
    int totalBoxes = 0;
    int totalPallets = 0;
    double totalRealWeight = 0;
    double totalPaidAmount = 0;
    double totalUnpaidAmount = 0;

    // مجاميع إضافية لكل عمود
    Map<String, dynamic> columnTotals = {};

    // تحديد الأعمدة التي نريد حساب مجموعها
    final columnsToSum = [
      'box_no',
      'pallet_no',
      'real_weight_kg',
      'total_paid',
      'unpaid_eur',
      'price_per_kg',
      'price_per_box',
      'price_per_pallet',
    ];

    // تهيئة مجاميع الأعمدة
    for (final column in columnsToSum) {
      columnTotals[column] = 0.0;
    }

    for (final data in codeData) {
      // Count boxes - من عمود box_no
      if (data['box_no'] != null) {
        final boxCount = (data['box_no'] as num).toInt();
        totalBoxes += boxCount;
        _logger.info('إضافة $boxCount صندوق، المجموع: $totalBoxes');
      }

      // Count pallets - من عمود pallet_no
      if (data['pallet_no'] != null) {
        final palletCount = (data['pallet_no'] as num).toInt();
        totalPallets += palletCount;
        _logger.info('إضافة $palletCount منصة، المجموع: $totalPallets');
      }

      // Sum real weight - من عمود real_weight_kg
      if (data['real_weight_kg'] != null) {
        final weight = (data['real_weight_kg'] as num).toDouble();
        totalRealWeight += weight;
        _logger.info('إضافة $weight كغم، المجموع: $totalRealWeight');
      }

      // Sum total paid amount - من عمود total_paid
      if (data['total_paid'] != null) {
        final paid = (data['total_paid'] as num).toDouble();
        totalPaidAmount += paid;
        _logger.info('إضافة $paid دينار مدفوع، المجموع: $totalPaidAmount');
      }

      // Sum unpaid amount in EUR - من عمود unpaid_eur
      if (data['unpaid_eur'] != null) {
        final unpaid = (data['unpaid_eur'] as num).toDouble();
        totalUnpaidAmount += unpaid;
        _logger
            .info('إضافة $unpaid يورو غير مدفوع، المجموع: $totalUnpaidAmount');
      }

      // حساب مجموع كل عمود
      for (final column in columnsToSum) {
        if (data[column] != null) {
          final value = (data[column] as num).toDouble();
          columnTotals[column] = columnTotals[column] + value;
          _logger.info(
              'إضافة $value لعمود $column، المجموع: ${columnTotals[column]}');
        }
      }
    }

    // طباعة مجاميع الأعمدة النهائية
    _logger.info('مجاميع الأعمدة النهائية: $columnTotals');

    // Return calculated statistics
    return {
      'totalAllTrucks': uniqueTruckNumbers.length,
      'uniqueTruckNumbers': uniqueTruckNumbers.toList(),
      'totalAllBoxes': totalBoxes,
      'totalAllPallets': totalPallets,
      'totalAllWeight': totalRealWeight,
      'totalAllPaid': totalPaidAmount,
      'totalAllUnpaid': totalUnpaidAmount,
      'totalRecords': codeData.length,
      'columnTotals': columnTotals, // إضافة مجاميع الأعمدة
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// Updates date range filter
  void updateDateRange(DateTime startDate, DateTime endDate) {
    _logger.info(
        'تحديث نطاق التاريخ: ${startDate.toIso8601String()} إلى ${endDate.toIso8601String()}');

    // تأكد من أن التاريخ الجديد مختلف عن التاريخ الحالي
    if (state.startDate?.toIso8601String() == startDate.toIso8601String() &&
        state.endDate?.toIso8601String() == endDate.toIso8601String()) {
      _logger.info('نطاق التاريخ لم يتغير، تخطي التحديث');
      return;
    }

    state = state.copyWith(
      startDate: startDate,
      endDate: endDate,
    );
    loadReportData();
  }

  /// Updates the truck filter
  void updateTruckFilter(String? truckNo) {
    _logger.info('تحديث فلتر الشاحنة: ${truckNo ?? "جميع الشاحنات"}');
    _logger.info('قيمة truckNo الحالية: ${state.truckNo}');

    // إذا كان truckNo هو "جميع الشاحنات" أو فارغ، نضع قيمة null
    // هذا سيؤدي إلى عدم إضافة شرط الشاحنة في الاستعلام
    final newTruckNo =
        (truckNo == null || truckNo.isEmpty || truckNo == "جميع الشاحنات")
            ? null
            : truckNo;

    _logger.info('قيمة newTruckNo بعد التعديل: $newTruckNo');

    // التحقق مما إذا كان الفلتر الجديد مختلفًا عن الفلتر الحالي
    if (state.truckNo != newTruckNo) {
      _logger.info('تغيير قيمة truckNo من ${state.truckNo} إلى $newTruckNo');
      state = state.copyWith(truckNo: newTruckNo);
      loadReportData();
    } else {
      _logger.info('رقم الشاحنة لم يتغير، تخطي التحديث');
    }
  }

  /// Refreshes report data with current filters
  Future<void> refreshReportData() async {
    await loadReportData();
  }

  /// Gets all unique truck numbers for filtering
  Future<List<String>> getAllTruckNumbers() async {
    try {
      final db = await _dbHelper.database;
      final result = await db.query(
        DbConstants.tableCodeData,
        columns: ['truck_no'],
        groupBy: 'truck_no',
        orderBy: 'truck_no ASC',
      );

      return result
          .map((row) => row['truck_no'] as String? ?? '')
          .where((truckNo) => truckNo.isNotEmpty)
          .toList();
    } catch (e) {
      _logger.severe('خطأ في الحصول على أرقام الشاحنات: $e');
      return [];
    }
  }

  /// الحصول على أسماء الأعمدة في جدول code_data
  Future<List<String>> getTableColumns() async {
    try {
      final db = await _dbHelper.database;
      final result =
          await db.rawQuery('PRAGMA table_info(${DbConstants.tableCodeData})');

      return result.map((row) => row['name'] as String).toList();
    } catch (e) {
      _logger.severe('خطأ في الحصول على أسماء الأعمدة: $e');
      return [];
    }
  }
}

/// Provider for reports state
final reportsProvider =
    StateNotifierProvider<ReportsNotifier, ReportsState>((ref) {
  final dbHelper = ref.watch(databaseProvider);
  return ReportsNotifier(dbHelper);
});

/// Provider for truck numbers
final truckNumbersProvider = FutureProvider<List<String>>((ref) async {
  final dbHelper = ref.watch(databaseProvider);
  final notifier = ReportsNotifier(dbHelper);
  return await notifier.getAllTruckNumbers();
});

/// Provider for table columns
final tableColumnsProvider = FutureProvider<List<String>>((ref) async {
  final dbHelper = ref.watch(databaseProvider);
  final notifier = ReportsNotifier(dbHelper);
  return await notifier.getTableColumns();
});

/// Provider for chart data
final chartDataProvider = Provider<List<Map<String, dynamic>>>((ref) {
  final reportsState = ref.watch(reportsProvider);

  if (reportsState.isLoading || reportsState.data.isEmpty) {
    return [];
  }

  return [
    {
      'name': 'المبلغ المدفوع (د.ع)',
      'amount': reportsState.data['totalAllPaid'] ?? 0,
    },
    {
      'name': 'المبلغ غير المدفوع (€)',
      'amount': reportsState.data['totalAllUnpaid'] ?? 0,
    },
  ];
});
