import 'package:flutter/material.dart';

/// مزيج يحتوي على تعريف المتغيرات المطلوبة لمعلومات التكلفة
mixin CostInfoStateMixin<T extends StatefulWidget> on State<T> {
  // متغير لتخزين حالة خيار التأمين
  bool _useInsurance = false;

  // متغير لتخزين حالة خيار تحويل المبلغ غير المدفوع إلى المدفوع
  bool _transferUnpaidToPaid = false;

  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _insurancePercentController =
      TextEditingController();
  final TextEditingController _goodsValueController = TextEditingController();
  final TextEditingController _insuranceAmountController =
      TextEditingController();
  final TextEditingController _exportDocController = TextEditingController();
  final TextEditingController _boxPackingCostController =
      TextEditingController();
  final TextEditingController _doorToDoorCostController =
      TextEditingController();
  final TextEditingController _postSubCostController = TextEditingController();
  final TextEditingController _discountAmountController =
      TextEditingController();
  final TextEditingController _totalPostCostController =
      TextEditingController();
  final TextEditingController _totalPaidController = TextEditingController();
  final TextEditingController _unpaidAmountController = TextEditingController();
  final TextEditingController _totalCostEURController = TextEditingController();
  final TextEditingController _unpaidEURController = TextEditingController();

  // الحصول على متحكمات النصوص
  TextEditingController get insurancePercentController =>
      _insurancePercentController;
  TextEditingController get goodsValueController => _goodsValueController;
  TextEditingController get insuranceAmountController =>
      _insuranceAmountController;
  TextEditingController get exportDocController => _exportDocController;
  TextEditingController get boxPackingCostController =>
      _boxPackingCostController;
  TextEditingController get doorToDoorCostController =>
      _doorToDoorCostController;
  TextEditingController get postSubCostController => _postSubCostController;
  TextEditingController get discountAmountController =>
      _discountAmountController;
  TextEditingController get totalPostCostController => _totalPostCostController;
  TextEditingController get totalPaidController => _totalPaidController;
  TextEditingController get unpaidAmountController => _unpaidAmountController;
  TextEditingController get totalCostEURController => _totalCostEURController;
  TextEditingController get unpaidEURController => _unpaidEURController;

  // الحصول على حالة خيارات التأمين وتحويل المبلغ غير المدفوع
  bool get useInsurance => _useInsurance;
  bool get transferUnpaidToPaid => _transferUnpaidToPaid;

  // تعيين حالة خيارات التأمين وتحويل المبلغ غير المدفوع
  set useInsurance(bool value) => _useInsurance = value;
  set transferUnpaidToPaid(bool value) => _transferUnpaidToPaid = value;

  // دالة لتحويل المبلغ غير المدفوع إلى المدفوع أو العكس
  void handleTransferUnpaidToPaid(bool? value) {
    setState(() {
      _transferUnpaidToPaid = value ?? false;

      if (_transferUnpaidToPaid) {
        // تحويل المبلغ غير المدفوع إلى المدفوع
        double unpaidAmount =
            double.tryParse(_unpaidAmountController.text.replaceAll(',', '')) ??
                0;
        double currentPaid =
            double.tryParse(_totalPaidController.text.replaceAll(',', '')) ?? 0;

        // إضافة المبلغ غير المدفوع إلى المدفوع
        _totalPaidController.text =
            formatNumberWithCommas(currentPaid + unpaidAmount);
        _unpaidAmountController.text = formatNumberWithCommas(0);
        _unpaidEURController.text = '0.00';

        // طباعة تشخيصية
        debugPrint('تم تحويل المبلغ غير المدفوع إلى المدفوع');
        debugPrint('المبلغ المدفوع الجديد: ${_totalPaidController.text}');
        debugPrint(
            'المبلغ غير المدفوع الجديد: ${_unpaidAmountController.text}');
        debugPrint(
            'المبلغ غير المدفوع باليورو الجديد: ${_unpaidEURController.text}');
      } else {
        // تحويل المبلغ المدفوع إلى غير مدفوع
        double totalPaid =
            double.tryParse(_totalPaidController.text.replaceAll(',', '')) ?? 0;

        // نقل المبلغ المدفوع إلى غير المدفوع
        _unpaidAmountController.text = formatNumberWithCommas(totalPaid);
        _totalPaidController.text = formatNumberWithCommas(0);

        // حساب المبلغ غير المدفوع باليورو
        double exchangeRate =
            getExchangeRate(); // استخدام دالة الحصول على سعر الصرف
        debugPrint('سعر الصرف المستخدم: $exchangeRate');

        double unpaidEUR = totalPaid / exchangeRate;
        debugPrint('المبلغ غير المدفوع باليورو (محسوب): $unpaidEUR');

        _unpaidEURController.text = unpaidEUR.toStringAsFixed(2);
        debugPrint(
            'المبلغ غير المدفوع باليورو (معروض): ${_unpaidEURController.text}');
      }
    });
  }

  // دالة مساعدة لتنسيق الأرقام بالفواصل
  String formatNumberWithCommas(double value) {
    try {
      // تنسيق الرقم بالفواصل
      String result = '';

      // التعامل مع الجزء الصحيح
      String valueStr = value.toInt().toString();
      int length = valueStr.length;

      for (int i = 0; i < length; i++) {
        if (i > 0 && (length - i) % 3 == 0) {
          result += ',';
        }
        result += valueStr[i];
      }

      // إضافة الجزء العشري إذا كان موجودًا
      double fraction = value - value.toInt();
      if (fraction > 0) {
        String decimal =
            fraction.toStringAsFixed(2).substring(1); // إزالة الصفر في البداية
        result += decimal;
      }

      return result;
    } catch (e) {
      return value.toString();
    }
  }

  // الحصول على سعر الصرف من PriceInfo
  double getExchangeRate() {
    double exchangeRate = 0.0;
    try {
      // محاولة الحصول على سعر الصرف من الأب
      final context = this.context;
      final homeScreenState = context.findAncestorStateOfType<State>();
      if (homeScreenState != null) {
        debugPrint('وجدت حالة الشاشة الرئيسية');

        // محاولة الوصول إلى receiverInfoKey بطريقة أكثر أمانًا
        dynamic receiverInfoKey;
        try {
          if (homeScreenState.toString().contains('_HomeScreenState')) {
            // التحقق أولاً مما إذا كانت حالة الشاشة الرئيسية هي _HomeScreenState
            receiverInfoKey = (homeScreenState as dynamic).receiverInfoKey;
            debugPrint('وجدت receiverInfoKey بشكل مباشر: $receiverInfoKey');
          } else {
            debugPrint(
                'الحالة ليست _HomeScreenState: ${homeScreenState.runtimeType}');
          }
        } catch (e) {
          debugPrint('خطأ في الوصول إلى receiverInfoKey: $e');
        }

        if (receiverInfoKey != null) {
          final receiverInfoState = receiverInfoKey.currentState;
          if (receiverInfoState != null) {
            debugPrint('وجدت حالة معلومات المستلم');

            dynamic priceInfoState;
            try {
              priceInfoState =
                  (receiverInfoState as dynamic).getPriceInfoState();
              debugPrint('وجدت حالة معلومات السعر: $priceInfoState');
            } catch (e) {
              debugPrint('خطأ في الحصول على حالة معلومات السعر: $e');
            }

            if (priceInfoState != null) {
              try {
                exchangeRate = (priceInfoState as dynamic).getExchangeRate();
                debugPrint('سعر الصرف من PriceInfo: $exchangeRate');
              } catch (e) {
                debugPrint('خطأ في الحصول على سعر الصرف: $e');
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ عام في الحصول على سعر الصرف: $e');
    }

    // استخدام قيمة افتراضية إذا كان سعر الصرف صفر
    if (exchangeRate <= 0) {
      exchangeRate = 1309.0; // القيمة الافتراضية المستخدمة في التطبيق
      debugPrint('استخدام سعر الصرف الافتراضي: $exchangeRate');
    }

    return exchangeRate;
  }

  @override
  void initState() {
    super.initState();

    // تعيين قيم افتراضية
    _insurancePercentController.text = '6';
    _goodsValueController.text = '0';

    // باقي الحقول ستتم تهيئتها في دالة resetFields
  }

  @override
  void dispose() {
    // التخلص من متحكمات النصوص
    _insurancePercentController.dispose();
    _goodsValueController.dispose();
    _insuranceAmountController.dispose();
    _exportDocController.dispose();
    _boxPackingCostController.dispose();
    _doorToDoorCostController.dispose();
    _postSubCostController.dispose();
    _discountAmountController.dispose();
    _totalPostCostController.dispose();
    _totalPaidController.dispose();
    _unpaidAmountController.dispose();
    _totalCostEURController.dispose();
    _unpaidEURController.dispose();

    super.dispose();
  }
}
