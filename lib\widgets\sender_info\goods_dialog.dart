import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/item_data.dart';
import '../../utils/constants.dart';
import '../../services/goods_data_service.dart';

/// نافذة منبثقة لإدارة قائمة البضائع
class GoodsDialog extends StatefulWidget {
  final List<ItemData> allItems;
  final List<ItemData> selectedItems;
  final Function(List<ItemData>, List<ItemData>, String) onSave;
  final Function(BuildContext, StateSetter, List<ItemData>) onImportFromExcel;
  final Function(BuildContext, List<ItemData>) onExportToExcel;

  const GoodsDialog({
    super.key,
    required this.allItems,
    required this.selectedItems,
    required this.onSave,
    required this.onImportFromExcel,
    required this.onExportToExcel,
  });

  @override
  State<GoodsDialog> createState() => _GoodsDialogState();
}

class _GoodsDialogState extends State<GoodsDialog> {
  final TextEditingController _itemArController = TextEditingController();
  final TextEditingController _itemEnController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  late List<ItemData> _dialogItems;
  late List<ItemData> _filteredItems;

  @override
  void initState() {
    super.initState();
    _initializeItems();
  }

  @override
  void dispose() {
    _itemArController.dispose();
    _itemEnController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // تهيئة قوائم العناصر
  void _initializeItems() {
    _dialogItems = [];

    // إضافة العناصر الافتراضية
    // العناصر الآن مرتبة حسب تكرار الاستخدام (من الأكثر اختيارًا إلى الأقل)
    // بفضل تعديل استعلام getAllGoodsData في قاعدة البيانات
    for (var item in widget.allItems) {
      // نسخ العنصر مع إعادة تعيين حالة الاختيار
      _dialogItems.add(ItemData(
        id: item.id,
        nameAr: item.nameAr,
        nameEn: item.nameEn,
        isSelected:
            widget.selectedItems.any((element) => element.id == item.id),
        quantity: widget.selectedItems.any((element) => element.id == item.id)
            ? widget.selectedItems
                .firstWhere((element) => element.id == item.id)
                .quantity
            : 1,
        weight: widget.selectedItems.any((element) => element.id == item.id)
            ? widget.selectedItems
                .firstWhere((element) => element.id == item.id)
                .weight
            : 0.0,
      ));
    }

    // تهيئة قائمة العناصر المعروضة بكل العناصر في البداية
    _filteredItems = List.from(_dialogItems);
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;

    return PopScope(
      // منع إغلاق النافذة بزر الرجوع دون حفظ التغييرات
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        // حفظ العناصر المستوردة فقط دون تحديدها
        widget.onSave(_dialogItems, [], '');
        // إغلاق النافذة بشكل آمن
        Navigator.of(context).pop();
      },
      child: Dialog(
        // تعيين حجم النافذة المنبثقة ليكون 75% من عرض الشاشة
        insetPadding: EdgeInsets.symmetric(
          horizontal: screenSize.width * 0.125, // 12.5% من كل جانب = 75% عرض
          vertical: 24,
        ),
        child: Container(
          width: screenSize.width * 0.75,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // العنوان
              const Text(
                SenderInfoStrings.goodsList,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // المحتوى
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // حقول إدخال العناصر الجديدة
                    _buildAddItemRow(),
                    const SizedBox(height: 8),

                    // حقل البحث
                    _buildSearchField(),
                    const SizedBox(height: 8),

                    // قائمة العناصر
                    Expanded(
                      child: _buildItemsList(),
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  // بناء صف إضافة عنصر جديد
  Widget _buildAddItemRow() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _itemArController,
            decoration: const InputDecoration(
              labelText: SenderInfoStrings.nameAr,
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextField(
            controller: _itemEnController,
            decoration: const InputDecoration(
              labelText: SenderInfoStrings.nameEn,
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            ),
            // إضافة خاصية لقبول النص الإنجليزي فقط
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\s]')),
            ],
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          icon: const Icon(Icons.add_circle, color: Colors.white),
          label: const Text(SenderInfoStrings.add,
              style: TextStyle(color: Colors.white)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
          ),
          onPressed: _addNewItem,
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          icon: const Icon(Icons.upload_file, color: Colors.white),
          label: const Text(SenderInfoStrings.import,
              style: TextStyle(color: Colors.white)),
          onPressed: () async {
            // استيراد من Excel
            await widget.onImportFromExcel(context, setState, _dialogItems);
            // تحديث القائمة المعروضة بعد الاستيراد
            setState(() {
              _filterItems(_searchController.text);
            });
            // عرض رسالة للمستخدم بنجاح عملية الاستيراد في نفس النافذة المنبثقة
            // ملاحظة: رسالة النجاح تظهر الآن في SenderInfoExcelMixin._showImportResult
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          icon: const Icon(Icons.download, color: Colors.white),
          label: const Text(SenderInfoStrings.export,
              style: TextStyle(color: Colors.white)),
          onPressed: () async {
            // تصدير إلى Excel
            await widget.onExportToExcel(context, _dialogItems);
            // ملاحظة: رسالة النجاح تظهر الآن في SenderInfoExcelMixin._exportToExcel
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  // بناء حقل البحث
  Widget _buildSearchField() {
    return Row(
      children: [
        // زر تحديد الكل
        ElevatedButton.icon(
          icon: const Icon(Icons.select_all, size: 18),
          label: const Text("Select All"),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
          ),
          onPressed: () {
            setState(() {
              // التحقق مما إذا كانت جميع العناصر المعروضة محددة بالفعل
              bool areAllSelected =
                  _filteredItems.every((item) => item.isSelected);

              // إذا كانت جميع العناصر محددة، قم بإلغاء تحديد الكل، وإلا قم بتحديد الكل
              for (var item in _filteredItems) {
                item.isSelected = !areAllSelected;

                // تحديث العنصر في القائمة الأصلية أيضًا
                final originalIndex =
                    _dialogItems.indexWhere((element) => element.id == item.id);
                if (originalIndex != -1) {
                  _dialogItems[originalIndex].isSelected = !areAllSelected;
                }
              }
            });
          },
        ),
        const SizedBox(width: 8),
        // حقل البحث
        Expanded(
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: SenderInfoStrings.search,
              prefixIcon: const Icon(Icons.search),
              border: const OutlineInputBorder(),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              // إضافة زر لمسح النص عند النقر عليه
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          // إعادة عرض جميع العناصر بعد مسح النص
                          _filterItems('');
                        });
                      },
                    )
                  : null,
            ),
            onChanged: (value) {
              // تحديث واجهة المستخدم لإظهار أو إخفاء زر المسح
              setState(() {});
              // تطبيق البحث على القائمة
              _filterItems(value);
            },
            // إضافة خصائص لتحسين تجربة المستخدم
            enableSuggestions: false,
            autocorrect: false,
            textInputAction: TextInputAction.search,
          ),
        ),
      ],
    );
  }

  // بناء قائمة العناصر
  Widget _buildItemsList() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Checkbox(
              value: item.isSelected,
              onChanged: (value) {
                setState(() {
                  item.isSelected = value ?? false;

                  // تحديث العنصر في القائمة الأصلية أيضًا
                  final originalIndex = _dialogItems
                      .indexWhere((element) => element.id == item.id);
                  if (originalIndex != -1) {
                    _dialogItems[originalIndex].isSelected = value ?? false;
                  }
                });
              },
            ),
            title: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text('${item.nameAr} / ${item.nameEn}'),
                ),
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      const Text(SenderInfoStrings.quantity),
                      SizedBox(
                        width: 50,
                        child: TextField(
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          controller: TextEditingController(
                              text: item.quantity.toString()),
                          onChanged: (value) {
                            setState(() {
                              item.quantity = int.tryParse(value) ?? 1;

                              // تحديث العنصر في القائمة الأصلية أيضًا
                              final originalIndex = _dialogItems.indexWhere(
                                  (element) => element.id == item.id);
                              if (originalIndex != -1) {
                                _dialogItems[originalIndex].quantity =
                                    item.quantity;
                              }
                            });
                          },
                          decoration: const InputDecoration(
                            isDense: true,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 4, vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      const Text(SenderInfoStrings.weight),
                      SizedBox(
                        width: 50,
                        child: TextField(
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*')),
                          ],
                          controller: TextEditingController(
                              text: item.weight.toString()),
                          onChanged: (value) {
                            setState(() {
                              item.weight = double.tryParse(value) ?? 0.0;

                              // تحديث العنصر في القائمة الأصلية أيضًا
                              final originalIndex = _dialogItems.indexWhere(
                                  (element) => element.id == item.id);
                              if (originalIndex != -1) {
                                _dialogItems[originalIndex].weight =
                                    item.weight;
                              }
                            });
                          },
                          decoration: const InputDecoration(
                            isDense: true,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 4, vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // زر التعديل
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  onPressed: () {
                    // عرض نافذة تعديل اسم العنصر
                    _showEditItemNameDialog(item);
                  },
                ),
                // زر الحذف
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () async {
                    try {
                      // حذف العنصر من قاعدة البيانات
                      final goodsDataService = GoodsDataService();
                      await goodsDataService.deleteGoodsData(item.id);

                      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
                      if (!mounted) return;

                      setState(() {
                        // حذف العنصر من القائمة الأصلية أولاً
                        final itemId = item.id;
                        _dialogItems
                            .removeWhere((element) => element.id == itemId);
                        // ثم حذفه من القائمة المعروضة
                        _filteredItems.removeAt(index);
                      });
                    } catch (e) {
                      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
                      if (!mounted) return;

                      // استخدام BuildContext بشكل آمن بعد التحقق من mounted
                      _showErrorSnackBar('Error deleting item: $e');
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.add_task, color: Colors.white),
          label: const Text(SenderInfoStrings.addAndExit),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          onPressed: _saveAndExit,
        ),
        const SizedBox(width: 16),
        TextButton(
          onPressed: () {
            // حفظ العناصر المستوردة فقط دون تحديدها
            widget.onSave(_dialogItems, [], '');
            // إغلاق النافذة بشكل آمن
            _safelyPopContext();
          },
          child: const Text(SenderInfoStrings.close),
        ),
      ],
    );
  }

  // إضافة عنصر جديد
  void _addNewItem() async {
    if (_itemArController.text.isEmpty || _itemEnController.text.isEmpty) {
      // عرض رسالة تنبيهية إذا كان أحد الحقول فارغًا
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Warning'),
          content: const Text(SenderInfoStrings.enterItemNameBoth),
          actions: [
            TextButton(
              onPressed: () => _safelyPopContext(),
              child: const Text(SenderInfoStrings.close),
            ),
          ],
        ),
      );
      return;
    }

    // التحقق من عدم وجود العنصر مسبقًا
    bool isDuplicate = _dialogItems.any((item) =>
        item.nameAr.toLowerCase() == _itemArController.text.toLowerCase() ||
        item.nameEn.toLowerCase() == _itemEnController.text.toLowerCase());

    if (isDuplicate) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Warning'),
          content: const Text(SenderInfoStrings.itemAlreadyExists),
          actions: [
            TextButton(
              onPressed: () => _safelyPopContext(),
              child: const Text(SenderInfoStrings.close),
            ),
          ],
        ),
      );
      return;
    }

    try {
      // إضافة العنصر إلى قاعدة البيانات
      final goodsDataService = GoodsDataService();
      final id = await goodsDataService.insertGoodsData(
        _itemArController.text,
        _itemEnController.text,
      );

      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      setState(() {
        // إضافة عنصر جديد إلى القائمة
        final newItem = ItemData(
          id: id,
          nameAr: _itemArController.text,
          nameEn: _itemEnController.text,
          isSelected: false,
          quantity: 1,
          weight: 0.0,
        );
        _dialogItems.add(newItem);

        // إضافة العنصر الجديد إلى القائمة المعروضة أيضًا
        if (_searchController.text.isEmpty ||
            newItem.nameAr
                .toLowerCase()
                .contains(_searchController.text.toLowerCase()) ||
            newItem.nameEn
                .toLowerCase()
                .contains(_searchController.text.toLowerCase())) {
          _filteredItems.add(newItem);
        }

        _itemArController.clear();
        _itemEnController.clear();
      });
    } catch (e) {
      // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text('Error adding item: $e'),
          actions: [
            TextButton(
              onPressed: () => _safelyPopContext(),
              child: const Text(SenderInfoStrings.close),
            ),
          ],
        ),
      );
    }
  }

  // تصفية العناصر بناءً على نص البحث
  void _filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        // إذا كان البحث فارغًا، عرض جميع العناصر
        _filteredItems = List.from(_dialogItems);
      } else {
        // تصفية العناصر التي تطابق نص البحث
        _filteredItems = _dialogItems.where((item) {
          return item.nameAr.toLowerCase().contains(query.toLowerCase()) ||
              item.nameEn.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  // عرض نافذة تعديل اسم العنصر
  void _showEditItemNameDialog(ItemData item) {
    final TextEditingController arNameController =
        TextEditingController(text: item.nameAr);
    final TextEditingController enNameController =
        TextEditingController(text: item.nameEn);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(SenderInfoStrings.editItemName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: arNameController,
              decoration: const InputDecoration(
                labelText: "Arabic Name",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: enNameController,
              decoration: const InputDecoration(
                labelText: "English Name",
                border: OutlineInputBorder(),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\s]')),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => _safelyPopContext(),
            child: const Text(SenderInfoStrings.cancel),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من عدم ترك الحقول فارغة
              if (arNameController.text.isEmpty ||
                  enNameController.text.isEmpty) {
                _safelyPopContext();
                _showErrorSnackBar(SenderInfoStrings.fillAllFields);
                return;
              }

              try {
                // تحديث البيانات في قاعدة البيانات
                final goodsDataService = GoodsDataService();
                await goodsDataService.updateGoodsData(
                  item.id,
                  arNameController.text,
                  enNameController.text,
                );

                // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
                if (!mounted) return;

                // تحديث اسم العنصر في واجهة المستخدم
                setState(() {
                  // إنشاء عنصر جديد بنفس البيانات ولكن مع الاسم الجديد
                  final updatedItem = ItemData(
                    id: item.id,
                    nameAr: arNameController.text,
                    nameEn: enNameController.text,
                    isSelected: item.isSelected,
                    quantity: item.quantity,
                    weight: item.weight,
                  );

                  // استبدال العنصر القديم بالعنصر الجديد في القائمة الأصلية
                  final index = _dialogItems
                      .indexWhere((element) => element.id == item.id);
                  if (index != -1) {
                    _dialogItems[index] = updatedItem;
                  }

                  // تحديث القائمة المعروضة أيضًا
                  // إذا كان العنصر المحدث يطابق معايير البحث، نحدثه في القائمة المعروضة
                  // وإلا نزيله من القائمة المعروضة
                  final filteredIndex = _filteredItems
                      .indexWhere((element) => element.id == item.id);
                  if (filteredIndex != -1) {
                    if (_searchController.text.isEmpty ||
                        updatedItem.nameAr
                            .toLowerCase()
                            .contains(_searchController.text.toLowerCase()) ||
                        updatedItem.nameEn
                            .toLowerCase()
                            .contains(_searchController.text.toLowerCase())) {
                      _filteredItems[filteredIndex] = updatedItem;
                    } else {
                      _filteredItems.removeAt(filteredIndex);
                    }
                  } else if (_searchController.text.isEmpty ||
                      updatedItem.nameAr
                          .toLowerCase()
                          .contains(_searchController.text.toLowerCase()) ||
                      updatedItem.nameEn
                          .toLowerCase()
                          .contains(_searchController.text.toLowerCase())) {
                    _filteredItems.add(updatedItem);
                  }
                });

                // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
                if (!mounted) return;

                // استخدام دالة مساعدة للتنقل بشكل آمن
                _safelyPopContext();
              } catch (e) {
                // التحقق من أن الـ widget لا يزال في شجرة واجهة المستخدم
                if (!mounted) return;

                _showErrorSnackBar('Error updating item: $e');
              }
            },
            child: const Text(SenderInfoStrings.save),
          ),
        ],
      ),
    );
  }

  // حفظ التغييرات والخروج
  void _saveAndExit() {
    // تحديث قائمة العناصر المختارة
    final selectedItems =
        _dialogItems.where((item) => item.isSelected).toList();

    // إنشاء وصف البضائع بناءً على القيم المختارة
    final goodsDescription = selectedItems.map((item) {
      // إذا كانت القيم الافتراضية (العدد = 1 والوزن = 0)، نعرض اسم العنصر فقط
      if (item.quantity == 1 && item.weight == 0.0) {
        return item.nameAr;
      }
      // إذا تم تغيير العدد فقط
      else if (item.quantity != 1 && item.weight == 0.0) {
        return '${item.nameAr} (${item.quantity})';
      }
      // إذا تم تغيير الوزن فقط
      else if (item.quantity == 1 && item.weight != 0.0) {
        return '${item.nameAr} (${item.weight} كغم)';
      }
      // إذا تم تغيير كلاهما
      else {
        return '${item.nameAr} (${item.quantity}, ${item.weight} كغم)';
      }
    }).join(', ');

    // استدعاء دالة الحفظ
    widget.onSave(_dialogItems, selectedItems, goodsDescription);

    // إغلاق النافذة بشكل آمن
    _safelyPopContext();
  }

  // دالة مساعدة لعرض رسائل الخطأ بشكل آمن
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: Colors.red.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.error,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد 3 ثواني
    Future.delayed(const Duration(seconds: 3), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // تجاهل الخطأ إذا كان العنصر تم حذفه بالفعل
      }
    });
  }

  // دالة مساعدة للتنقل بشكل آمن
  void _safelyPopContext() {
    if (!mounted) return;
    Navigator.pop(context);
  }
}
