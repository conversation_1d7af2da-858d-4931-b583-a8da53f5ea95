import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/constants.dart';
import '../../utils/ui_helper.dart';

/// مزيج يحتوي على دوال بناء واجهة المستخدم لمكون معلومات المرسل
mixin SenderInfoUiMixin<T extends StatefulWidget> on State<T> {
  // الحصول على المتحكمات النصية
  TextEditingController get _senderNameController;
  TextEditingController get _senderPhoneController;
  TextEditingController get _senderIdController;
  TextEditingController get _goodsDescriptionController;

  // الحصول على متغيرات الحالة
  String get _senderIdImagePath;

  // الحصول على دوال الأحداث
  void _showIdTypeDialog();
  void _showPhoneSearchDialog();
  void _showGoodsDescriptionDialog();

  // بناء واجهة المستخدم الرئيسية
  Widget buildSenderInfoUI() {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              SenderInfoStrings.senderInformation,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            // حقل اسم المرسل
            _buildSenderNameField(),
            const SizedBox(height: 8),

            // حقل هاتف المرسل
            _buildSenderPhoneField(),
            const SizedBox(height: 8),

            // حقل هوية المرسل مع زر لاختيار نوع الهوية وصورة الهوية
            _buildSenderIdField(),
            const SizedBox(height: 8),

            // حقل وصف البضائع
            _buildGoodsDescriptionField(),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  // بناء حقل اسم المرسل
  Widget _buildSenderNameField() {
    return SizedBox(
      height: 60, // زيادة ارتفاع الحقل لراحة أكبر
      child: TextField(
        controller: _senderNameController,
        decoration: const InputDecoration(
          labelText: SenderInfoStrings.senderName,
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 18),
        ),
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold, // جعل النص غامقًا
        ),
        // إضافة ميزة تحويل النص إلى أحرف كبيرة عند التغيير
        onChanged: (value) =>
            UiHelper.convertToUpperCase(_senderNameController),
      ),
    );
  }

  // بناء حقل هاتف المرسل
  Widget _buildSenderPhoneField() {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 60, // زيادة ارتفاع الحقل لراحة أكبر
            child: TextField(
              controller: _senderPhoneController,
              decoration: const InputDecoration(
                labelText: SenderInfoStrings.senderPhone,
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                hintText:
                    SenderInfoStrings.enterDigits, // إضافة تلميح داخل الحقل
              ),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold, // جعل النص غامقًا
                color: _senderPhoneController.text.length < 11
                    ? Colors.red
                    : null, // تغيير لون النص إلى أحمر إذا كان عدد الأرقام أقل من 11
              ),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(
                    11), // تحديد الطول إلى 11 رقمًا فقط
              ],
              // تغيير لون النص بناءً على عدد الأرقام
              onChanged: (value) => setState(() {}),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showPhoneSearchDialog,
          tooltip: 'Search by Phone Number',
        ),
      ],
    );
  }

  // بناء حقل هوية المرسل
  Widget _buildSenderIdField() {
    return SizedBox(
      height: 60, // زيادة ارتفاع الحقل لراحة أكبر
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _senderIdController,
              decoration: const InputDecoration(
                labelText: SenderInfoStrings.senderId,
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 18),
              ),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.more_vert,
              // تغيير لون الأيقونة بناءً على وجود صورة الهوية
              color: _senderIdImagePath.isNotEmpty ? Colors.green : Colors.red,
            ),
            onPressed: _showIdTypeDialog,
            tooltip: 'Select ID Type and Image',
          ),
        ],
      ),
    );
  }

  // بناء حقل وصف البضائع
  Widget _buildGoodsDescriptionField() {
    return TextField(
      controller: _goodsDescriptionController,
      decoration: const InputDecoration(
        labelText: SenderInfoStrings.goodsDescription,
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      ),
      maxLines: 2,
      // عند النقر على الحقل، عرض نافذة حوار مباشرة
      onTap: _showGoodsDescriptionDialog,
      readOnly: true, // جعل الحقل للقراءة فقط لمنع ظهور لوحة المفاتيح
    );
  }
}
