import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../providers/riverpod/user_provider.dart';
import '../utils/constants.dart';
import '../utils/theme.dart';
import '../utils/ui_helper.dart';
import '../services/database_helper.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _passwordFocusNode = FocusNode();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // قائمة الفروع
  List<String> _branches = [];
  // قاموس لتخزين معرفات الفروع
  Map<String, int> _branchIdMap = {};

  // قائمة المستخدمين
  List<String> _usernames = [];
  String _selectedUsername = '';
  String _selectedBranch = '';

  bool _isPasswordVisible = false; // حالة ظهور كلمة المرور
  bool _isLoading = false; // حالة تحميل تسجيل الدخول

  @override
  void initState() {
    super.initState();
    // تطبيق وضع ملء الشاشة عند تهيئة شاشة تسجيل الدخول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

      // تحميل الفروع أولا ثم بقية البيانات في ترتيب صحيح
      _loadBranches().then((_) {
        _loadLastLoginUser().then((_) {
          // وضع التركيز على حقل كلمة المرور بعد تحميل البيانات
          if (_passwordFocusNode.canRequestFocus) {
            _passwordFocusNode.requestFocus();
          }
        });
      });
    });
  }

  // دالة لتحميل أسماء المستخدمين
  Future<void> _loadUsernames({int? branchId}) async {
    try {
      List<Map<String, dynamic>> users = [];

      // إذا تم تحديد فرع، قم بتحميل المستخدمين الخاصين بهذا الفرع فقط
      if (branchId != null) {
        users = await _databaseHelper.getUsersByBranch(branchId);
      } else {
        // قم بتحميل جميع المستخدمين إذا لم يتم تحديد فرع (مثل التهيئة الأولية)
        users = await _databaseHelper.getUsers();
      }

      setState(() {
        _usernames = users.map((user) => user['name'] as String).toList();

        // إضافة المستخدم الافتراضي (admin) فقط إذا لم يكن هناك مستخدمين أو فروع
        // إذا كان هناك فرع محدد ومستخدمين في هذا الفرع، لا نظهر حساب الادمن
        if (_usernames.isEmpty) {
          if (!_usernames.contains(AppConstants.defaultUsername)) {
            _usernames.add(AppConstants.defaultUsername);
          }
        }

        // تأكد من عدم وجود قيم مكررة في قائمة المستخدمين
        _usernames = _usernames.toSet().toList();

        // تعيين المستخدم الأول كقيمة مبدئية إذا كانت القائمة غير فارغة
        // ولكن تجنب إعادة تعيين المستخدم إذا كان محدد مسبقاً (من آخر تسجيل دخول)
        if (_usernames.isNotEmpty && _selectedUsername.isEmpty) {
          _selectedUsername = _usernames.first;
        } else if (_usernames.isNotEmpty &&
            !_usernames.contains(_selectedUsername)) {
          // إذا كان المستخدم المحدد غير موجود في القائمة الجديدة، اختر الأول
          _selectedUsername = _usernames.first;
        }
      });
    } catch (e) {
      debugPrint('خطأ في تحميل أسماء المستخدمين: $e');
    }
  }

  // دالة لتحميل الفروع من قاعدة البيانات
  Future<void> _loadBranches() async {
    try {
      final branchesData = await _databaseHelper.getBranches();
      if (branchesData.isNotEmpty) {
        setState(() {
          _branches =
              branchesData.map((branch) => branch['name'] as String).toList();

          // بناء قاموس يربط اسم الفرع بمعرف الفرع
          _branchIdMap = {};
          for (var branch in branchesData) {
            _branchIdMap[branch['name'] as String] = branch['id'] as int;
          }

          // تأكد من عدم وجود قيم مكررة في قائمة الفروع
          _branches = _branches.toSet().toList();

          // تعيين الفرع الأول كقيمة مبدئية إذا كانت القائمة غير فارغة
          if (_branches.isNotEmpty) {
            _selectedBranch = _branches.first;
            // تحميل المستخدمين المرتبطين بالفرع المحدد
            if (_branchIdMap.containsKey(_selectedBranch)) {
              _loadUsernames(branchId: _branchIdMap[_selectedBranch]);
            }
          } else {
            _selectedBranch = '';
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الفروع: $e');
    }
  }

  // دالة لتحميل معلومات آخر مستخدم قام بتسجيل الدخول
  Future<void> _loadLastLoginUser() async {
    try {
      final lastLoginUser = await _databaseHelper.getLastLoginUser();
      if (lastLoginUser != null) {
        // تعيين اسم الفرع إذا كان موجودًا في القائمة
        if (lastLoginUser['branch_id'] != null &&
            lastLoginUser.containsKey('branch_name')) {
          final branchName = lastLoginUser['branch_name'] as String;
          if (_branches.contains(branchName)) {
            setState(() {
              _selectedBranch = branchName;
            });

            // تحميل المستخدمين المرتبطين بالفرع المحدد
            if (_branchIdMap.containsKey(_selectedBranch)) {
              await _loadUsernames(branchId: _branchIdMap[_selectedBranch]);

              // بعد تحميل المستخدمين، تعيين اسم المستخدم إذا كان موجودًا في القائمة
              final username = lastLoginUser['username'] as String;
              if (_usernames.contains(username)) {
                setState(() {
                  _selectedUsername = username;
                });
              }
            }
          }
        } else {
          // إذا لم يكن هناك فرع محدد، تحميل جميع المستخدمين
          await _loadUsernames();

          // تعيين اسم المستخدم إذا كان موجودًا في القائمة
          final username = lastLoginUser['username'] as String;
          if (_usernames.contains(username)) {
            setState(() {
              _selectedUsername = username;
            });
          }
        }
      } else {
        // إذا لم يكن هناك بيانات آخر تسجيل دخول، تحميل المستخدمين المرتبطين بالفرع المحدد الحالي
        if (_selectedBranch.isNotEmpty &&
            _branchIdMap.containsKey(_selectedBranch)) {
          _loadUsernames(branchId: _branchIdMap[_selectedBranch]);
        }
      }

      // دائماً تفريغ حقل كلمة المرور لأمان إضافي
      _passwordController.clear();
    } catch (e) {
      debugPrint('خطأ في تحميل معلومات آخر مستخدم قام بتسجيل الدخول: $e');
    }
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  // دالة تسجيل الدخول
  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // التحقق من بيانات المستخدم في قاعدة البيانات
        final user = await _databaseHelper.authenticateUser(
          _selectedUsername,
          _passwordController.text.trim(),
        );

        // التحقق من أن الـ widget لا يزال مثبتًا في الشجرة قبل استخدام context
        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        if (user != null) {
          // حفظ معلومات آخر مستخدم قام بتسجيل الدخول
          await _databaseHelper.saveLastLoginUser(
              _selectedUsername, user['branch_id'] as int?);

          // تعيين الفرع الحالي في DatabaseHelper (وضع المستخدم العادي)
          _databaseHelper.setCurrentBranch(_selectedBranch, isAdmin: false);
          debugPrint(
              'تم تعيين الفرع الحالي في DatabaseHelper (مستخدم عادي): $_selectedBranch');

          // تحديث معلومات المستخدم
          ref.read(userProvider.notifier).setUserInfo(
                user['name'],
                _selectedBranch,
                role: user['role'],
              );

          // الانتقال إلى الشاشة الرئيسية
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/home');
          }
        } else {
          // التحقق من المستخدم الافتراضي
          if (_selectedUsername == AppConstants.defaultUsername &&
              _passwordController.text == AppConstants.defaultPassword) {
            // حفظ معلومات آخر مستخدم قام بتسجيل الدخول
            await _databaseHelper.saveLastLoginUser(_selectedUsername, null);

            // تعيين وضع الأدمن في DatabaseHelper للوصول لقاعدة البيانات الرئيسية
            _databaseHelper.setCurrentBranch(_selectedBranch, isAdmin: true);
            debugPrint('تم تعيين وضع الأدمن - الوصول لقاعدة البيانات الرئيسية');

            // تحديث معلومات المستخدم
            ref.read(userProvider.notifier).setUserInfo(
                  _selectedUsername,
                  _selectedBranch,
                  role: 'admin',
                );

            // الانتقال إلى الشاشة الرئيسية
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/home');
            }
          } else {
            // عرض رسالة خطأ
            if (mounted) {
              UiHelper.showNotification(
                context,
                messageEn:
                    "Login failed. Please check your username and password.",
                isError: true,
                durationSeconds: 4,
              );
            }
          }
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        // عرض رسالة خطأ
        if (mounted) {
          UiHelper.showNotification(
            context,
            messageEn: "Error during login: $e",
            isError: true,
            durationSeconds: 4,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      // منع العودة من شاشة تسجيل الدخول
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        // إعادة تطبيق وضع ملء الشاشة عند الضغط على زر العودة
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      },
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withAlpha(150),
              ],
            ),
          ),
          child: Center(
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 450),
                child: Padding(
                  padding: const EdgeInsets.all(UIConstants.largePadding),
                  child: _buildLoginCard(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة تسجيل الدخول
  Widget _buildLoginCard() {
    return Card(
      elevation: 8,
      shape: AppBorders.cardShape,
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.largePadding),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildLogo(),
              const SizedBox(height: UIConstants.largePadding),
              // خيار تسجيل الدخول للأدمن (يظهر دائماً)
              _buildAdminLoginSection(),

              // فاصل إذا كانت هناك فروع ومستخدمين
              if (_branches.isNotEmpty && _usernames.isNotEmpty) ...[
                const SizedBox(height: UIConstants.defaultPadding),
                const Divider(thickness: 1),
                const SizedBox(height: UIConstants.defaultPadding),
                const Text(
                  'Branch User Login',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: UIConstants.defaultPadding),
              ],

              // عرض واجهة تسجيل الدخول العادية إذا كانت هناك فروع ومستخدمين
              if (_branches.isNotEmpty && _usernames.isNotEmpty) ...[
                _buildBranchDropdown(),
                const SizedBox(height: UIConstants.defaultPadding),
                _buildUsernameField(),
                const SizedBox(height: UIConstants.defaultPadding),
                _buildPasswordField(),
                const SizedBox(height: UIConstants.largePadding),
                _buildLoginButton(),
              ] else ...[
                const Text(
                  'No branches or users configured yet',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.caption,
                ),
              ],
              const SizedBox(height: UIConstants.defaultPadding),
              if (_branches.isNotEmpty && _usernames.isNotEmpty)
                _buildForgotPasswordButton(),
            ],
          ),
        ),
      ),
    );
  }

  // بناء شعار الشركة
  Widget _buildLogo() {
    return const Column(
      children: [
        Icon(
          Icons.local_shipping_outlined,
          size: 70,
          color: AppColors.primary,
        ),
        SizedBox(height: UIConstants.smallPadding),
        Text(
          AppConstants.appName,
          style: AppTextStyles.heading,
        ),
        SizedBox(height: 6),
        Text(
          'International Transport',
          style: AppTextStyles.caption,
        ),
      ],
    );
  }

  // بناء قائمة اختيار الفرع
  Widget _buildBranchDropdown() {
    // تأكد من أن القيمة المحددة موجودة في القائمة
    if (_selectedBranch.isNotEmpty && !_branches.contains(_selectedBranch)) {
      _selectedBranch = _branches.isNotEmpty ? _branches.first : '';
    }

    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Branch',
        prefixIcon: Icon(Icons.business),
      ),
      value: _selectedBranch.isNotEmpty ? _selectedBranch : null,
      items: _branches.map((branch) {
        return DropdownMenuItem<String>(
          value: branch,
          child: Text(branch),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedBranch = value;
          });

          // عند تغيير الفرع، قم بتحديث قائمة المستخدمين المرتبطين بهذا الفرع
          if (_branchIdMap.containsKey(value)) {
            _loadUsernames(branchId: _branchIdMap[value]);
          }
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select a branch';
        }
        return null;
      },
    );
  }

  // بناء حقل اسم المستخدم
  Widget _buildUsernameField() {
    // تأكد من أن القيمة المحددة موجودة في القائمة
    if (_selectedUsername.isNotEmpty &&
        !_usernames.contains(_selectedUsername)) {
      _selectedUsername = _usernames.isNotEmpty ? _usernames.first : '';
    }

    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Username',
        prefixIcon: Icon(Icons.person),
      ),
      value: _selectedUsername.isNotEmpty ? _selectedUsername : null,
      items: _usernames.map((username) {
        return DropdownMenuItem<String>(
          value: username,
          child: Text(username),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedUsername = value;
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select a username';
        }
        return null;
      },
    );
  }

  // بناء حقل كلمة المرور
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      focusNode: _passwordFocusNode,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Password',
        prefixIcon: const Icon(Icons.lock),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        return null;
      },
      // إضافة خاصية للتعامل مع مفتاح Enter
      onFieldSubmitted: (value) {
        // تنفيذ تسجيل الدخول عند الضغط على مفتاح Enter
        if (!_isLoading) {
          _login();
        }
      },
      // إضافة خاصية textInputAction لتعيين زر الإجراء في لوحة المفاتيح إلى "تم"
      textInputAction: TextInputAction.done,
    );
  }

  // بناء زر تسجيل الدخول
  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      height: 45,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _login,
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.5,
                  color: Colors.white,
                ),
              )
            : const Text(
                'Login',
                style: TextStyle(fontSize: 16),
              ),
      ),
    );
  }

  // بناء زر نسيت كلمة المرور
  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: () {
        _showForgotPasswordDialog();
      },
      child: const Text('Forgot Password?'),
    );
  }

  // بناء قسم تسجيل الدخول للأدمن
  Widget _buildAdminLoginSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Expanded(
          child: Text(
            'Admin Access',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        // أيقونة الأدمن المحسنة
        Container(
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _showAdminLoginDialog,
              borderRadius: BorderRadius.circular(12),
              child: const Padding(
                padding: EdgeInsets.all(12),
                child: Icon(
                  Icons.admin_panel_settings,
                  color: AppColors.primary,
                  size: 28,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // عرض نافذة تسجيل دخول الأدمن
  void _showAdminLoginDialog() {
    final adminPasswordController = TextEditingController();
    bool isAdminPasswordVisible = false;
    bool isAdminLoading = false;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: const Row(
                children: [
                  Icon(
                    Icons.admin_panel_settings,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Admin Login',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Enter admin password to access main database',
                    style: AppTextStyles.caption,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: UIConstants.defaultPadding),
                  TextFormField(
                    controller: adminPasswordController,
                    obscureText: !isAdminPasswordVisible,
                    autofocus: true,
                    decoration: InputDecoration(
                      labelText: 'Admin Password',
                      prefixIcon: const Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isAdminPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                        ),
                        onPressed: () {
                          setDialogState(() {
                            isAdminPasswordVisible = !isAdminPasswordVisible;
                          });
                        },
                      ),
                    ),
                    onFieldSubmitted: (value) {
                      if (!isAdminLoading) {
                        setDialogState(() => isAdminLoading = true);
                        _performAdminLogin(
                          dialogContext,
                          adminPasswordController.text.trim(),
                        ).then((_) {
                          if (context.mounted) {
                            setDialogState(() => isAdminLoading = false);
                          }
                        });
                      }
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: isAdminLoading
                      ? null
                      : () => Navigator.of(dialogContext).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAdminLoading
                      ? null
                      : () {
                          setDialogState(() => isAdminLoading = true);
                          _performAdminLogin(
                            dialogContext,
                            adminPasswordController.text.trim(),
                          ).then((_) {
                            if (context.mounted) {
                              setDialogState(() => isAdminLoading = false);
                            }
                          });
                        },
                  child: isAdminLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Login'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // تنفيذ تسجيل دخول الأدمن
  Future<void> _performAdminLogin(
    BuildContext dialogContext,
    String password,
  ) async {
    if (password.isEmpty) {
      UiHelper.showNotification(
        context,
        messageEn: "Please enter admin password",
        isError: true,
        durationSeconds: 3,
      );
      return;
    }

    try {
      // التحقق من كلمة المرور الافتراضية للأدمن
      if (password == AppConstants.defaultPassword) {
        // حفظ معلومات آخر مستخدم قام بتسجيل الدخول
        await _databaseHelper.saveLastLoginUser(
            AppConstants.defaultUsername, null);

        // تعيين وضع الأدمن في DatabaseHelper للوصول لقاعدة البيانات الرئيسية
        _databaseHelper.setCurrentBranch(AppConstants.defaultBranch,
            isAdmin: true);
        debugPrint('تم تعيين وضع الأدمن - الوصول لقاعدة البيانات الرئيسية');

        // تحديث معلومات المستخدم
        ref.read(userProvider.notifier).setUserInfo(
              AppConstants.defaultUsername,
              AppConstants.defaultBranch,
              role: 'admin',
            );

        // إغلاق النافذة المنبثقة
        if (dialogContext.mounted) {
          Navigator.of(dialogContext).pop();
        }

        // الانتقال إلى الشاشة الرئيسية
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/home');
        }
      } else {
        // كلمة مرور خاطئة
        if (mounted) {
          UiHelper.showNotification(
            context,
            messageEn: "Invalid admin password. Please try again.",
            isError: true,
            durationSeconds: 4,
          );
        }
      }
    } catch (e) {
      // عرض رسالة خطأ
      if (mounted) {
        UiHelper.showNotification(
          context,
          messageEn: "Error during admin login: $e",
          isError: true,
          durationSeconds: 4,
        );
      }
    }
  }

  // بناء زر تسجيل الدخول كمسؤول (احتفظ به للتوافق مع باقي الكود)

  // عرض نافذة نسيان كلمة المرور
  void _showForgotPasswordDialog() {
    final usernameEmailController = TextEditingController();
    bool isLoading = false;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: const Row(
                children: [
                  Icon(
                    Icons.help_outline,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Password Recovery',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Enter your username or email address to recover your password:',
                    style: AppTextStyles.caption,
                  ),
                  const SizedBox(height: UIConstants.defaultPadding),
                  TextFormField(
                    controller: usernameEmailController,
                    autofocus: true,
                    decoration: const InputDecoration(
                      labelText: 'Username or Email',
                      hintText: 'Enter username or email',
                      prefixIcon: Icon(Icons.person_search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setDialogState(() {
                        // تحديث حالة الزر عند تغيير النص
                      });
                    },
                    onFieldSubmitted: (value) {
                      if (!isLoading && value.trim().isNotEmpty) {
                        setDialogState(() => isLoading = true);
                        _performPasswordRecovery(
                          dialogContext,
                          usernameEmailController.text.trim(),
                        ).then((_) {
                          if (context.mounted) {
                            setDialogState(() => isLoading = false);
                          }
                        });
                      }
                    },
                  ),
                  const SizedBox(height: UIConstants.smallPadding),
                  const Text(
                    'Note: For security reasons, admin access is required to view or reset passwords.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () => Navigator.of(dialogContext).pop(),
                  child: const Text('Cancel'),
                ),
                ValueListenableBuilder<TextEditingValue>(
                  valueListenable: usernameEmailController,
                  builder: (context, value, child) {
                    return ElevatedButton(
                      onPressed: isLoading || value.text.trim().isEmpty
                          ? null
                          : () {
                              setDialogState(() => isLoading = true);
                              _performPasswordRecovery(
                                dialogContext,
                                usernameEmailController.text.trim(),
                              ).then((_) {
                                if (context.mounted) {
                                  setDialogState(() => isLoading = false);
                                }
                              });
                            },
                      child: isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text('Recover Password'),
                    );
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  // تنفيذ استرداد كلمة المرور
  Future<void> _performPasswordRecovery(
    BuildContext dialogContext,
    String usernameOrEmail,
  ) async {
    if (usernameOrEmail.isEmpty) {
      UiHelper.showNotification(
        context,
        messageEn: "Please enter username or email",
        isError: true,
        durationSeconds: 3,
      );
      return;
    }

    try {
      // البحث عن المستخدم بالاسم أو البريد الإلكتروني
      final user = await _searchUserByUsernameOrEmail(usernameOrEmail);

      if (user == null) {
        if (mounted) {
          UiHelper.showNotification(
            context,
            messageEn: "User not found. Please check your username or email.",
            isError: true,
            durationSeconds: 4,
          );
        }
        return;
      }

      // إغلاق نافذة البحث
      if (dialogContext.mounted) {
        Navigator.of(dialogContext).pop();
      }

      // عرض نافذة خيارات استرداد كلمة المرور
      if (mounted) {
        _showPasswordRecoveryOptions(user);
      }
    } catch (e) {
      if (mounted) {
        UiHelper.showNotification(
          context,
          messageEn: "Error during password recovery: $e",
          isError: true,
          durationSeconds: 4,
        );
      }
    }
  }

  // تنظيف المستخدمين المكررين والافتراضيين
  Future<void> _cleanupDuplicateUsers(Database db) async {
    try {
      final defaultUsers = await db.query(
        'users',
        where: 'password = ? OR length(email) <= 2',
        whereArgs: ['12345'],
      );

      for (var user in defaultUsers) {
        // التحقق من وجود مستخدم آخر بنفس الاسم لكن ببيانات أفضل
        final betterUsers = await db.query(
          'users',
          where: 'name = ? AND id != ? AND length(email) > 2 AND password != ?',
          whereArgs: [user['name'], user['id'], '12345'],
        );

        if (betterUsers.isNotEmpty) {
          await db.delete('users', where: 'id = ?', whereArgs: [user['id']]);
        }
      }
    } catch (e) {
      // تجاهل الأخطاء في التنظيف
    }
  }

  // البحث عن المستخدم بواسطة اسم المستخدم أو البريد الإلكتروني
  Future<Map<String, dynamic>?> _searchUserByUsernameOrEmail(
      String query) async {
    try {
      final db = await _databaseHelper.database;

      // حذف المستخدم الافتراضي إذا وُجد
      await _cleanupDuplicateUsers(db);

      // البحث بالاسم أولاً
      var results = await db.query(
        'users',
        where: 'name = ?',
        whereArgs: [query],
        limit: 1,
      );

      // إذا لم يتم العثور على النتيجة، البحث بالبريد الإلكتروني
      if (results.isEmpty) {
        results = await db.query(
          'users',
          where: 'email = ?',
          whereArgs: [query],
          limit: 1,
        );
      }

      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      return null;
    }
  }

  // عرض خيارات استرداد كلمة المرور
  void _showPasswordRecoveryOptions(Map<String, dynamic> user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(
                Icons.account_circle,
                color: AppColors.primary,
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                'User Found',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User: ${user['name']}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Email: ${user['email'] ?? 'Not provided'}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Role: ${user['role']}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: UIConstants.defaultPadding),
              const Text(
                'Choose an option to recover the password:',
                style: AppTextStyles.caption,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _showCurrentPassword(user);
              },
              icon: const Icon(Icons.visibility, size: 18),
              label: const Text('Show Current Password'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _showResetPasswordDialog(user);
              },
              icon: const Icon(Icons.lock_reset, size: 18),
              label: const Text('Reset Password'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  // عرض كلمة المرور الحالية (يتطلب تصريح المسؤول)
  void _showCurrentPassword(Map<String, dynamic> user) {
    _requestAdminPermission(
      'View Current Password',
      'Enter admin password to view the current password for user "${user['name']}"',
      () {
        // عرض كلمة المرور الحالية
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.visibility, color: Colors.blue),
                SizedBox(width: 8),
                Text('Current Password'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'User: ${user['name']}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      SelectableText(
                        'Password: ${user['password']}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontFamily: 'monospace',
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Please note this password down securely.',
                  style: TextStyle(
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض نافذة إعادة تعيين كلمة المرور
  void _showResetPasswordDialog(Map<String, dynamic> user) {
    _requestAdminPermission(
      'Reset Password',
      'Enter admin password to reset the password for user "${user['name']}"',
      () {
        final newPasswordController = TextEditingController();
        final confirmPasswordController = TextEditingController();
        bool isNewPasswordVisible = false;
        bool isConfirmPasswordVisible = false;

        showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState) => AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.lock_reset, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('Reset Password'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Reset password for: ${user['name']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: newPasswordController,
                    obscureText: !isNewPasswordVisible,
                    decoration: InputDecoration(
                      labelText: 'New Password',
                      prefixIcon: const Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isNewPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                        ),
                        onPressed: () {
                          setState(() {
                            isNewPasswordVisible = !isNewPasswordVisible;
                          });
                        },
                      ),
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: confirmPasswordController,
                    obscureText: !isConfirmPasswordVisible,
                    decoration: InputDecoration(
                      labelText: 'Confirm New Password',
                      prefixIcon: const Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isConfirmPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                        ),
                        onPressed: () {
                          setState(() {
                            isConfirmPasswordVisible =
                                !isConfirmPasswordVisible;
                          });
                        },
                      ),
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _performPasswordReset(
                      context,
                      user,
                      newPasswordController.text,
                      confirmPasswordController.text,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Reset Password'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // تنفيذ إعادة تعيين كلمة المرور
  Future<void> _performPasswordReset(
    BuildContext context,
    Map<String, dynamic> user,
    String newPassword,
    String confirmPassword,
  ) async {
    // التحقق من صحة المدخلات
    if (newPassword.isEmpty || confirmPassword.isEmpty) {
      UiHelper.showNotification(
        context,
        messageEn: "Please fill in both password fields",
        isError: true,
        durationSeconds: 3,
      );
      return;
    }

    if (newPassword != confirmPassword) {
      UiHelper.showNotification(
        context,
        messageEn: "Passwords do not match",
        isError: true,
        durationSeconds: 3,
      );
      return;
    }

    if (newPassword.length < 3) {
      UiHelper.showNotification(
        context,
        messageEn: "Password must be at least 3 characters long",
        isError: true,
        durationSeconds: 3,
      );
      return;
    }

    try {
      // تحديث كلمة المرور في قاعدة البيانات
      await _databaseHelper.updateUser({
        'id': user['id'],
        'password': newPassword,
      });

      // إغلاق النافذة
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة نجاح
      if (context.mounted) {
        UiHelper.showNotification(
          context,
          messageEn: "Password reset successfully for user '${user['name']}'",
          isError: false,
          durationSeconds: 4,
        );
      }
    } catch (e) {
      if (context.mounted) {
        UiHelper.showNotification(
          context,
          messageEn: "Error resetting password: $e",
          isError: true,
          durationSeconds: 4,
        );
      }
    }
  }

  // طلب إذن المسؤول
  void _requestAdminPermission(
      String title, String message, VoidCallback onSuccess) {
    final adminPasswordController = TextEditingController();
    bool isAdminPasswordVisible = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.admin_panel_settings, color: Colors.red),
              SizedBox(width: 8),
              Text('Admin Permission Required'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                message,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: adminPasswordController,
                obscureText: !isAdminPasswordVisible,
                autofocus: true,
                decoration: InputDecoration(
                  labelText: 'Admin Password',
                  prefixIcon: const Icon(Icons.admin_panel_settings),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isAdminPasswordVisible
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        isAdminPasswordVisible = !isAdminPasswordVisible;
                      });
                    },
                  ),
                  border: const OutlineInputBorder(),
                ),
                onFieldSubmitted: (value) {
                  if (value == AppConstants.defaultPassword) {
                    Navigator.of(context).pop();
                    onSuccess();
                  } else {
                    UiHelper.showNotification(
                      context,
                      messageEn: "Invalid admin password",
                      isError: true,
                      durationSeconds: 3,
                    );
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (adminPasswordController.text ==
                    AppConstants.defaultPassword) {
                  Navigator.of(context).pop();
                  onSuccess();
                } else {
                  UiHelper.showNotification(
                    context,
                    messageEn: "Invalid admin password",
                    isError: true,
                    durationSeconds: 3,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Authorize'),
            ),
          ],
        ),
      ),
    );
  }
}
