import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
// لدعم تحويل base64

/// فئة مسؤولة عن إنشاء تصميم ملصق المكتب (بدون رمز بريدي)
class OfficeLabel {
  /// مفتاح لحفظ لون حد الجدول في الإعدادات
  static const String _tableBorderColorKey = 'table_border_color';

  /// الحصول على لون حد الجدول من الإعدادات
  static Future<PdfColor> _getTableBorderColor() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colorValue = prefs.getInt(_tableBorderColorKey);

      if (colorValue != null) {
        // تحويل القيمة المحفوظة إلى PdfColor
        final r = (colorValue >> 16) & 0xFF;
        final g = (colorValue >> 8) & 0xFF;
        final b = colorValue & 0xFF;
        return PdfColor.fromInt(0xFF000000 | (r << 16) | (g << 8) | b);
      }
    } catch (e) {
      debugPrint('خطأ في قراءة لون حد الجدول: $e');
    }

    // اللون الافتراضي (أسود)
    return PdfColors.black;
  }

  /// حفظ لون حد الجدول في الإعدادات
  static Future<void> setTableBorderColor(int colorValue) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_tableBorderColorKey, colorValue);
      debugPrint('تم حفظ لون حد الجدول: $colorValue');
    } catch (e) {
      debugPrint('خطأ في حفظ لون حد الجدول: $e');
    }
  }

  /// إنشاء ملصق مكتبي
  static Future<Uint8List> generate({
    required String companyName,
    required Map<String, dynamic> shipmentData,
    bool forPreview = false, // معامل جديد للمعاينة
  }) async {
    debugPrint('======== بدء إنشاء office label ========');
    debugPrint('وضع المعاينة: $forPreview');
    debugPrint('اسم الشركة: $companyName');
    debugPrint('عدد عناصر البيانات: ${shipmentData.length}');

    // تحميل الخطوط العربية والإنجليزية
    final Map<String, pw.Font> fonts = await _loadFonts();
    debugPrint('تم تحميل ${fonts.length} خطوط');

    // تحميل الصور المطلوبة
    final Map<String, Uint8List> images = await _loadImages();
    debugPrint('تم تحميل ${images.length} صور');

    // الحصول على لون حد الجدول من الإعدادات
    final PdfColor tableBorderColor = await _getTableBorderColor();

    // استخراج صورة العلم
    final Uint8List countryFlagImage =
        await _extractCountryFlagImage(shipmentData);
    debugPrint('حجم صورة العلم: ${countryFlagImage.length} بايت');

    // إنشاء المستند
    final pdf = pw.Document();

    // حساب عدد النسخ
    int totalCopies = _calculateTotalCopies(shipmentData);
    debugPrint('عدد النسخ المطلوبة: $totalCopies');

    // في وضع المعاينة، إنشاء نسخة واحدة فقط
    int copyCountToGenerate = forPreview ? 1 : totalCopies;
    debugPrint('عدد النسخ المراد إنشاؤها: $copyCountToGenerate');

    // إنشاء النسخ المطلوبة
    for (int copyIndex = 1; copyIndex <= copyCountToGenerate; copyIndex++) {
      debugPrint('إنشاء النسخة رقم: $copyIndex');
      pdf.addPage(
        pw.Page(
          textDirection: pw.TextDirection.ltr,
          pageFormat: PdfPageFormat.a6,
          margin: const pw.EdgeInsets.all(15), // هوامش 15 من جميع الجهات
          theme: pw.ThemeData.withFont(
            base: fonts['regular']!,
            bold: fonts['bold']!,
          ),
          build: (pw.Context context) {
            return pw.Column(
              children: [
                // صورة الهيدر فقط
                _buildHeaderWithContact(images),

                pw.SizedBox(height: 10),

                // المحتوى الرئيسي
                pw.Expanded(
                  child: pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // هامش جانبي إضافي من اليسار - مقلل
                      pw.SizedBox(width: 4),

                      // المستطيل الرئيسي مع معلومات الشحنة على اليسار
                      pw.Expanded(
                        flex: 3,
                        child: _buildMainInfoBox(shipmentData, fonts, copyIndex,
                            totalCopies, tableBorderColor),
                      ),

                      pw.SizedBox(width: 6),

                      // مستطيل Agent City على اليمين
                      pw.Expanded(
                        flex: 1,
                        child: pw.Padding(
                          padding: const pw.EdgeInsets.only(right: 4),
                          child: _buildAgentCityBox(
                              shipmentData, fonts, images, tableBorderColor),
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 10),

                // صورة علم الدولة مع التاريخ
                _buildFlagWithDate(countryFlagImage, fonts, shipmentData),

                // هامش سفلي إضافي
                pw.SizedBox(height: 6),
              ],
            );
          },
        ),
      );
    }

    // حفظ المستند
    return pdf.save();
  }

  /// تحميل الخطوط
  static Future<Map<String, pw.Font>> _loadFonts() async {
    final Map<String, pw.Font> fonts = {};

    try {
      // تحميل خط Arial العادي
      final regularFontData = await rootBundle.load('assets/fonts/arial.ttf');
      fonts['regular'] = pw.Font.ttf(regularFontData);

      // تحميل خط Arial الغامق
      final boldFontData = await rootBundle.load('assets/fonts/arial_bold.ttf');
      fonts['bold'] = pw.Font.ttf(boldFontData);

      final kurdishFontData = await rootBundle
          .load('assets/fonts/NotoSansArabic_Condensed-SemiBold.ttf');
      fonts['kurdish'] = pw.Font.ttf(kurdishFontData);

      // تعيين الخطوط الإضافية
      fonts['cairo'] = fonts['bold']!;
      fonts['arial_bold'] = fonts['bold']!;
    } catch (e) {
      debugPrint('خطأ في تحميل الخطوط: $e');
      // استخدام الخطوط الافتراضية في حالة الخطأ
      fonts['regular'] = pw.Font.helvetica();
      fonts['bold'] = pw.Font.helveticaBold();
      fonts['cairo'] = pw.Font.helveticaBold();
      fonts['arial_bold'] = pw.Font.helveticaBold();
      fonts['kurdish'] = pw.Font.helvetica(); // خط احتياطي للكردي
    }

    return fonts;
  }

  /// تحميل الصور المطلوبة
  static Future<Map<String, Uint8List>> _loadImages() async {
    final Map<String, Uint8List> images = {};

    try {
      final ByteData data = await rootBundle.load('assets/images/header.png');
      images['assets/images/header.png'] = data.buffer.asUint8List();
      debugPrint(
          'تم تحميل صورة الهيدر: assets/images/header.png - حجم: ${images['assets/images/header.png']!.length} بايت');
    } catch (e) {
      debugPrint('خطأ في تحميل صورة الهيدر: $e');
      // إنشاء صورة بديلة بسيطة
      images['assets/images/header.png'] = Uint8List(0);
    }

    try {
      final ByteData insuranceData =
          await rootBundle.load('assets/images/insurance.png');
      images['assets/images/insurance.png'] =
          insuranceData.buffer.asUint8List();
      debugPrint(
          'تم تحميل صورة التأمين: assets/images/insurance.png - حجم: ${images['assets/images/insurance.png']!.length} بايت');
    } catch (e) {
      debugPrint('خطأ في تحميل صورة التأمين: $e');
      // إنشاء صورة بديلة بسيطة
      images['assets/images/insurance.png'] = Uint8List(0);
    }

    return images;
  }

  /// بناء الهيدر مع صورة واحدة فقط
  static pw.Widget _buildHeaderWithContact(Map<String, Uint8List> images) {
    return pw.Center(
      child: _buildHeaderImage(images['assets/images/header.png']!, 250),
    );
  }

  /// بناء مستطيل Agent City
  static pw.Widget _buildAgentCityBox(
      Map<String, dynamic> data,
      Map<String, pw.Font> fonts,
      Map<String, Uint8List> images,
      PdfColor borderColor) {
    String branchName = _getBranchName(data);

    // التحقق من نسبة التأمين
    double insurancePercent = 0;
    if (data.containsKey('insurance_percent') &&
        data['insurance_percent'] != null) {
      insurancePercent =
          double.tryParse(data['insurance_percent'].toString()) ?? 0;
    }
    bool hasInsurance = insurancePercent > 0;

    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: borderColor, width: 1.5),
      ),
      child: pw.Column(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.SizedBox(height: 5),

          // كلمة Agent City
          pw.Text(
            'Agent City',
            style: pw.TextStyle(
              fontSize: 7,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.black,
            ),
            textAlign: pw.TextAlign.center,
          ),

          pw.SizedBox(height: 5),

          // اسم الفرع مكتوب عمودياً - كلمة كاملة
          pw.Expanded(
            flex: 2,
            child: pw.Center(
              child: pw.Transform.rotate(
                angle: -1.5708, // -90 degrees in radians
                child: pw.FittedBox(
                  fit: pw.BoxFit.scaleDown,
                  child: pw.Text(
                    branchName.toUpperCase(),
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 11,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.black,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ),
            ),
          ),

          // صورة التأمين
          if (hasInsurance) ...[
            pw.SizedBox(height: 3),
            pw.Expanded(
              flex: 3,
              child: pw.Center(
                child: _buildInsuranceImage(
                    images['assets/images/insurance.png']!),
              ),
            ),
            pw.SizedBox(height: 5),
          ],
        ],
      ),
    );
  }

  /// بناء صورة في الهيدر
  static pw.Widget _buildHeaderImage(Uint8List imageData, double size) {
    if (imageData.isEmpty) {
      return pw.Container(
        width: size,
        height: size,
        decoration: pw.BoxDecoration(
          color: PdfColors.grey300,
          border: pw.Border.all(color: PdfColors.grey500),
        ),
        child: pw.Center(
          child: pw.Text(
            'IMG',
            style: const pw.TextStyle(fontSize: 6, color: PdfColors.grey700),
          ),
        ),
      );
    }

    return pw.Container(
      width: size,
      height: size,
      child: pw.Image(pw.MemoryImage(imageData), fit: pw.BoxFit.contain),
    );
  }

  /// بناء صورة التأمين
  static pw.Widget _buildInsuranceImage(Uint8List imageData) {
    if (imageData.isEmpty) {
      return pw.Container(
        width: 10,
        height: 10,
        decoration: pw.BoxDecoration(
          color: PdfColors.grey300,
          border: pw.Border.all(color: PdfColors.grey500),
        ),
        child: pw.Center(
          child: pw.Text(
            'INS',
            style: const pw.TextStyle(fontSize: 8, color: PdfColors.grey700),
          ),
        ),
      );
    }

    return pw.Container(
      width: 110, // تحديد عرض ثابت أصغر
      height: 110, // تحديد ارتفاع ثابت أصغر
      child: pw.Image(pw.MemoryImage(imageData), fit: pw.BoxFit.contain),
    );
  }

  /// بناء المستطيل الرئيسي مع معلومات الشحنة
  static pw.Widget _buildMainInfoBox(
      Map<String, dynamic> data,
      Map<String, pw.Font> fonts,
      int copyIndex,
      int totalCopies,
      PdfColor borderColor) {
    return pw.Container(
      padding: const pw.EdgeInsets.fromLTRB(12, 12, 12, 4),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: borderColor, width: 1.5),
        borderRadius: pw.BorderRadius.circular(0),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Sender
          _buildInfoRow(
              'Sender:', data['sender_name']?.toString() ?? '', fonts),
          pw.SizedBox(height: 16),

          // Receiver
          _buildInfoRow(
              'Receiver:', data['receiver_name']?.toString() ?? '', fonts),
          pw.SizedBox(height: 16),

          // Phone no
          _buildInfoRow(
              'Phone no:', data['receiver_phone']?.toString() ?? '', fonts),
          pw.SizedBox(height: 16),

          // Item
          _buildItemRow(
              'Item:', data['goods_description']?.toString() ?? '', fonts),
          pw.SizedBox(height: 16),

          // Weight kg with unit info
          _buildWeightRow(data, fonts, copyIndex, totalCopies),
          pw.SizedBox(height: 16),

          // City
          _buildInfoRow(
              'City:', (data['city']?.toString() ?? '').toUpperCase(), fonts),
          pw.SizedBox(height: 16),

          // Country
          _buildInfoRow('Country:',
              (data['country']?.toString() ?? '').toUpperCase(), fonts),
          pw.SizedBox(height: 16),

          // Code
          _buildCodeRow(data, fonts),
        ],
      ),
    );
  }

  /// بناء صف Item مع التحكم في طول النص
  static pw.Widget _buildItemRow(
      String label, String value, Map<String, pw.Font> fonts) {
    // التحقق من وجود أحرف عربية في النص
    bool isArabicText = _containsArabic(value);

    // تحديد الحد الأقصى لطول النص (يمكن تعديله حسب الحاجة)
    String displayValue = value;
    if (value.length > 50) {
      displayValue = '${value.substring(0, 47)}...';
    }

    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.SizedBox(
          width: 50,
          child: pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: 8,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.Expanded(
          child: isArabicText
              ? pw.Directionality(
                  textDirection: pw.TextDirection.rtl,
                  child: pw.Text(
                    displayValue,
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 7,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    maxLines: 1, // الحد الأقصى لعدد الأسطر
                    overflow: pw.TextOverflow.clip, // قطع النص إذا تجاوز النص
                  ),
                )
              : pw.Text(
                  displayValue,
                  style: pw.TextStyle(
                    fontSize: 7,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  maxLines: 1, // الحد الأقصى لعدد الأسطر
                  overflow: pw.TextOverflow.clip, // قطع النص إذا تجاوز النص
                ),
        ),
      ],
    );
  }

  /// بناء صف معلومات عادي
  static pw.Widget _buildInfoRow(
      String label, String value, Map<String, pw.Font> fonts) {
    // التحقق من وجود أحرف عربية في النص
    bool isArabicText = _containsArabic(value);

    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.SizedBox(
          width: 50,
          child: pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: 8,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.Expanded(
          child: isArabicText
              ? pw.Directionality(
                  textDirection: pw.TextDirection.rtl,
                  child: pw.Text(
                    value,
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 9,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                )
              : pw.Text(
                  value,
                  style: pw.TextStyle(
                    fontSize: 9,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
        ),
      ],
    );
  }

  /// التحقق من وجود نص عربي
  static bool _containsArabic(String text) {
    if (text.isEmpty) return false;

    // فحص الأحرف العربية في النطاق Unicode
    for (int i = 0; i < text.length; i++) {
      int charCode = text.codeUnitAt(i);
      if ((charCode >= 0x0600 && charCode <= 0x06FF) || // Arabic
          (charCode >= 0x0750 && charCode <= 0x077F) || // Arabic Supplement
          (charCode >= 0x08A0 && charCode <= 0x08FF) || // Arabic Extended-A
          (charCode >= 0xFB50 &&
              charCode <= 0xFDFF) || // Arabic Presentation Forms-A
          (charCode >= 0xFE70 && charCode <= 0xFEFF)) {
        // Arabic Presentation Forms-B
        return true;
      }
    }
    return false;
  }

  /// بناء صف الوزن مع معلومات الوحدة
  static pw.Widget _buildWeightRow(Map<String, dynamic> data,
      Map<String, pw.Font> fonts, int copyIndex, int totalCopies) {
    String weight = data['total_weight_kg']?.toString() ??
        data['real_weight_kg']?.toString() ??
        '';

    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.SizedBox(
          width: 50,
          child: pw.Text(
            'Weight kg:',
            style: pw.TextStyle(
              fontSize: 8,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.Expanded(
          child: pw.Text(
            weight,
            style: pw.TextStyle(
              fontSize: 9,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.RichText(
          text: pw.TextSpan(
            style: pw.TextStyle(
              fontSize: 8,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
            children: [
              const pw.TextSpan(text: 'Unit no: '),
              pw.TextSpan(
                text: '$copyIndex',
                style: pw.TextStyle(
                  fontSize: 10, // حجم أكبر للقيمة
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              const pw.TextSpan(text: ' Of '),
              pw.TextSpan(
                text: '$totalCopies',
                style: pw.TextStyle(
                  fontSize: 10, // حجم أكبر للقيمة
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف الكود
  static pw.Widget _buildCodeRow(
      Map<String, dynamic> data, Map<String, pw.Font> fonts) {
    String codeNo = data['code_no']?.toString() ?? '';
    String truckNo = data['truck_no']?.toString() ?? '';

    // بناء الكود الكامل مع رقم الشاحنة مع علامة (-)
    String fullCode = codeNo;

    // إضافة رقم الشاحنة مع علامة (-) إذا وُجد
    if (truckNo.isNotEmpty && truckNo != '0' && truckNo != 'null') {
      fullCode = '$codeNo-$truckNo';
      debugPrint('✅ تم إضافة رقم الشاحنة: $fullCode');
    } else {
      debugPrint('❌ لا يوجد رقم شاحنة صحيح: "$truckNo"');
    }

    // تحديد حالة الدفع بناءً على unpaid_amount
    String paymentStatus = '';
    double unpaidAmount = 0;

    if (data.containsKey('unpaid_amount') && data['unpaid_amount'] != null) {
      unpaidAmount = double.tryParse(data['unpaid_amount'].toString()) ?? 0;
    }

    if (unpaidAmount == 0) {
      paymentStatus = 'All Paid';
    } else if (unpaidAmount > 0) {
      paymentStatus = 'Pay In EU';
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // صف الكود
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.SizedBox(
              width: 50,
              child: pw.Text(
                'Code:',
                style: pw.TextStyle(
                  fontSize: 8,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            pw.Expanded(
              child: pw.Text(
                fullCode,
                style: pw.TextStyle(
                  fontSize: 12, // حجم خط أكبر لرقم الكود
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.black,
                ),
                maxLines: 1,
                overflow: pw.TextOverflow.clip,
              ),
            ),
          ],
        ),

        // حالة الدفع فقط في صف جديد تحت الكود
        if (paymentStatus.isNotEmpty) ...[
          pw.SizedBox(
              height: 12), // زيادة المسافة بين الكود وحالة الدفع من 6 إلى 12
          pw.Row(
            children: [
              pw.SizedBox(
                  width: 50), // تقليل العرض أكثر لتحريك حالة الدفع إلى اليمين

              // حالة الدفع
              pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: pw.BoxDecoration(
                  color: unpaidAmount == 0 ? PdfColors.green : PdfColors.orange,
                  borderRadius: pw.BorderRadius.circular(3),
                ),
                child: pw.Text(
                  unpaidAmount == 0
                      ? paymentStatus.toUpperCase()
                      : paymentStatus,
                  style: pw.TextStyle(
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.black,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء صورة علم الدولة
  static pw.Widget _buildCountryFlag(Uint8List flagImage) {
    debugPrint('حجم صورة العلم المستلمة: ${flagImage.length} بايت');

    if (flagImage.isEmpty) {
      debugPrint('⚠️ صورة العلم فارغة - سيتم استخدام النص الافتراضي');
      return pw.Container(
        width: 130,
        height: 50,
        decoration: pw.BoxDecoration(
          color: PdfColors.blue100,
          borderRadius: pw.BorderRadius.circular(0),
        ),
        child: pw.Center(
          child: pw.Text(
            'EU',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
        ),
      );
    }

    debugPrint('✅ سيتم عرض صورة العلم');

    return pw.Container(
      width: 130,
      height: 50,
      child: pw.Image(
        pw.MemoryImage(flagImage),
        fit: pw.BoxFit.fill,
      ),
    );
  }

  /// بناء القياسات والوزن الإضافي
  static pw.Widget _buildDimensionsAndExtraWeight(
      Map<String, dynamic> data, Map<String, pw.Font> fonts) {
    // التحقق من وجود أبعاد صالحة
    bool hasDimensions = false;
    double height = 0, length = 0, width = 0;

    if (data.containsKey('height') && data['height'] != null) {
      height = double.tryParse(data['height'].toString()) ?? 0;
    }
    if (data.containsKey('length') && data['length'] != null) {
      length = double.tryParse(data['length'].toString()) ?? 0;
    }
    if (data.containsKey('width') && data['width'] != null) {
      width = double.tryParse(data['width'].toString()) ?? 0;
    }

    // التحقق من أن جميع الأبعاد أكبر من 0
    hasDimensions = height > 0 && length > 0 && width > 0;

    // الحصول على الوزن الإضافي
    double additionalWeight = 0;
    if (data.containsKey('additional_kg') && data['additional_kg'] != null) {
      additionalWeight = double.tryParse(data['additional_kg'].toString()) ?? 0;
    }

    // إذا لم توجد أبعاد أو وزن إضافي، إرجاع حاوية فارغة
    if (!hasDimensions && additionalWeight <= 0) {
      return pw.SizedBox.shrink();
    }

    return pw.Column(
      children: [
        // عرض الأبعاد إذا كانت متوفرة
        if (hasDimensions) ...[
          pw.Text(
            'Dimensions',
            style: pw.TextStyle(
              fontSize: 6,
              fontWeight: pw.FontWeight.normal,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 2),
          pw.Text(
            '${length.toStringAsFixed(0)}×${width.toStringAsFixed(0)}×${height.toStringAsFixed(0)}',
            style: pw.TextStyle(
              fontSize: 7,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.black,
            ),
          ),
        ],

        // مسافة بين الأبعاد والوزن الإضافي
        if (hasDimensions && additionalWeight > 0) pw.SizedBox(height: 5),

        // عرض الوزن الإضافي إذا كان متوفرًا
        if (additionalWeight > 0) ...[
          pw.Text(
            'Extra Weight',
            style: pw.TextStyle(
              fontSize: 6,
              fontWeight: pw.FontWeight.normal,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 2),
          pw.Text(
            '+${additionalWeight.toStringAsFixed(2)} kg',
            style: pw.TextStyle(
              fontSize: 7,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.red,
            ),
          ),
        ],
      ],
    );
  }

  /// بناء العلم مع التاريخ
  static pw.Widget _buildFlagWithDate(Uint8List flagImage,
      Map<String, pw.Font> fonts, Map<String, dynamic> data) {
    // الحصول على تاريخ اليوم
    final DateTime now = DateTime.now();
    final String currentDate = DateFormat('dd/MM/yyyy').format(now);

    // بناء القياسات والوزن الإضافي
    pw.Widget dimensionsWidget = _buildDimensionsAndExtraWeight(data, fonts);

    // التحقق من وجود محتوى في القياسات
    bool hasDimensionsContent = !_isDimensionsEmpty(data);

    return pw.Row(
      mainAxisAlignment: hasDimensionsContent
          ? pw.MainAxisAlignment.center
          : pw.MainAxisAlignment.start,
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        if (hasDimensionsContent) ...[
          // التاريخ على اليسار
          pw.Column(
            children: [
              pw.Text(
                'Date',
                style: pw.TextStyle(
                  fontSize: 6,
                  fontWeight: pw.FontWeight.normal,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 2),
              pw.Text(
                currentDate,
                style: pw.TextStyle(
                  fontSize: 7,
                  fontWeight: pw.FontWeight.normal,
                  color: PdfColors.black,
                ),
              ),
            ],
          ),

          pw.SizedBox(width: 15),

          // العلم في الوسط
          _buildCountryFlag(flagImage),

          pw.SizedBox(width: 15),

          // القياسات والوزن الإضافي على اليمين
          dimensionsWidget,
        ] else ...[
          // عندما لا توجد قياسات، ضع العناصر إلى اليسار
          pw.SizedBox(width: 20), // مسافة من اليسار

          // التاريخ
          pw.Column(
            children: [
              pw.Text(
                'Date',
                style: pw.TextStyle(
                  fontSize: 6,
                  fontWeight: pw.FontWeight.normal,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 2),
              pw.Text(
                currentDate,
                style: pw.TextStyle(
                  fontSize: 7,
                  fontWeight: pw.FontWeight.normal,
                  color: PdfColors.black,
                ),
              ),
            ],
          ),

          pw.SizedBox(width: 15),

          // العلم
          _buildCountryFlag(flagImage),

          pw.Spacer(), // يدفع العناصر إلى اليسار
        ],
      ],
    );
  }

  /// التحقق من أن القياسات فارغة
  static bool _isDimensionsEmpty(Map<String, dynamic> data) {
    // التحقق من وجود أبعاد صالحة
    bool hasDimensions = false;
    double height = 0, length = 0, width = 0;

    if (data.containsKey('height') && data['height'] != null) {
      height = double.tryParse(data['height'].toString()) ?? 0;
    }
    if (data.containsKey('length') && data['length'] != null) {
      length = double.tryParse(data['length'].toString()) ?? 0;
    }
    if (data.containsKey('width') && data['width'] != null) {
      width = double.tryParse(data['width'].toString()) ?? 0;
    }

    hasDimensions = height > 0 && length > 0 && width > 0;

    // الحصول على الوزن الإضافي
    double additionalWeight = 0;
    if (data.containsKey('additional_kg') && data['additional_kg'] != null) {
      additionalWeight = double.tryParse(data['additional_kg'].toString()) ?? 0;
    }

    return !hasDimensions && additionalWeight <= 0;
  }

  /// الحصول على اسم الفرع
  static String _getBranchName(Map<String, dynamic> data) {
    // البحث عن اسم الفرع في الحقول المختلفة
    List<String> branchFields = [
      'current_branch',
      'user_branch',
      'login_branch',
      'branch_name',
      'office_name',
      'sender_branch',
      'from_branch',
    ];

    for (String field in branchFields) {
      if (data.containsKey(field) &&
          data[field] != null &&
          data[field].toString().trim().isNotEmpty &&
          data[field] != 'null') {
        return data[field].toString().trim();
      }
    }

    return 'Baghdad'; // القيمة الافتراضية
  }

  /// حساب عدد النسخ المطلوبة
  static int _calculateTotalCopies(Map<String, dynamic> data) {
    int boxCount = 0;
    int palletCount = 0;

    if (data['box_no'] != null && data['box_no'].toString().isNotEmpty) {
      try {
        boxCount = int.parse(data['box_no'].toString());
      } catch (e) {
        debugPrint('خطأ في تحويل box_no: $e');
      }
    }

    if (data['pallet_no'] != null && data['pallet_no'].toString().isNotEmpty) {
      try {
        palletCount = int.parse(data['pallet_no'].toString());
      } catch (e) {
        debugPrint('خطأ في تحويل pallet_no: $e');
      }
    }

    int totalCopies = boxCount + palletCount;
    return totalCopies < 1 ? 1 : totalCopies;
  }

  /// استخراج صورة العلم
  static Future<Uint8List> _extractCountryFlagImage(
      Map<String, dynamic> data) async {
    try {
      debugPrint('======== استخراج صورة العلم لملصق المكتب ========');

      // الحصول على اسم الدولة المباشر
      String countryName = _getCountryNameFromData(data);
      debugPrint('اسم الدولة المستخرج: $countryName');

      // قائمة الأعلام المتوفرة فقط في مجلد office
      final Map<String, List<String>> availableFlags = {
        'germany': ['germany.jpg', 'germany.png', 'de.jpg', 'de.png'],
        'sweden': ['sweden.jpg', 'sweden.png', 'se.jpg', 'se.png'],
        'finland': ['finland.jpg', 'finland.png', 'fi.jpg', 'fi.png'],
        'netherlands': [
          'netherlands.jpg',
          'netherlands.png',
          'nl.jpg',
          'nl.png'
        ],
        'united kingdom': [
          'united-kingdom.jpg',
          'united-kingdom.png',
          'uk.jpg',
          'uk.png',
          'gb.jpg',
          'gb.png'
        ],
      };

      // البحث عن العلم المناسب
      String selectedCountry = '';
      for (String country in availableFlags.keys) {
        if (countryName.toLowerCase().contains(country) ||
            country.contains(countryName.toLowerCase())) {
          selectedCountry = country;
          debugPrint('تم العثور على الدولة: $selectedCountry');
          break;
        }
      }

      // إذا لم نجد الدولة، استخدم ألمانيا كافتراضي
      if (selectedCountry.isEmpty) {
        selectedCountry = 'germany';
        debugPrint('لم يتم العثور على الدولة، استخدام ألمانيا كافتراضي');
      }

      // محاولة تحميل العلم بأسماء مختلفة
      List<String> flagVariants =
          availableFlags[selectedCountry] ?? ['germany.jpg'];

      for (String flagName in flagVariants) {
        String flagPath = 'assets/images/countries/office/$flagName';
        try {
          final ByteData flagData = await rootBundle.load(flagPath);
          final Uint8List flagImage = flagData.buffer.asUint8List();
          debugPrint('✅ تم تحميل صورة العلم بنجاح من: $flagPath');
          return flagImage;
        } catch (e) {
          debugPrint('❌ فشل في تحميل العلم من: $flagPath');
          continue;
        }
      }

      // علم احتياطي نهائي - ألمانيا مضمونة
      debugPrint('محاولة تحميل علم ألمانيا كافتراضي مضمون...');
      try {
        final ByteData flagData =
            await rootBundle.load('assets/images/countries/office/germany.jpg');
        final Uint8List flagImage = flagData.buffer.asUint8List();
        debugPrint('✅ تم تحميل علم ألمانيا كافتراضي');
        return flagImage;
      } catch (e) {
        debugPrint('❌ فشل في تحميل علم ألمانيا: $e');
      }
    } catch (e) {
      debugPrint('خطأ في استخراج صورة العلم: $e');
    }

    return Uint8List(0);
  }

  /// الحصول على اسم الدولة من البيانات
  static String _getCountryNameFromData(Map<String, dynamic> data) {
    // طباعة جميع البيانات للتشخيص
    debugPrint('======== تحليل بيانات الدولة ========');
    data.forEach((key, value) {
      if (key.toLowerCase().contains('country') ||
          key.toLowerCase().contains('destination')) {
        debugPrint('  $key: "$value"');
      }
    });

    // قائمة الحقول التي قد تحتوي على اسم الدولة
    final List<String> countryFields = [
      'country',
      'destination_country',
      'to_country',
      'receiver_country',
      'delivery_country',
    ];

    for (String field in countryFields) {
      if (data.containsKey(field) &&
          data[field] != null &&
          data[field].toString().trim().isNotEmpty &&
          data[field] != 'null') {
        String countryValue = data[field].toString().trim();
        debugPrint('✅ وُجد اسم الدولة في $field: "$countryValue"');
        return countryValue;
      }
    }

    debugPrint('❌ لم يتم العثور على اسم الدولة، استخدام ألمانيا كافتراضي');
    return 'Germany';
  }
}
