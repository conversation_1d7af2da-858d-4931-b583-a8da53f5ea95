import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/riverpod/address_providers.dart';
import '../../providers/riverpod/address_database_provider.dart';
import '../../services/excel_import_service.dart';
import '../../utils/file_helper.dart';
import '../../widgets/settings_parts/paginated_address_list.dart';
import 'settings_variables_mixin.dart';
import 'address_import_export_mixin.dart';

/// Mixin يحتوي على الوظائف المتعلقة بعناوين ألمانيا
mixin GermanyAddressesMixin<T extends ConsumerStatefulWidget>
    on
        ConsumerState<T>,
        SettingsVariablesMixin<T>,
        AddressImportExportMixin<T> {
  // تحميل عناوين ألمانيا من قاعدة البيانات
  Future<void> loadGermanyAddresses() async {
    isLoadingGermany = true;

    try {
      final addressDbService = ref.read(addressDatabaseServiceProvider);
      final addresses = await addressDbService.getGermanyAddresses();

      if (mounted) {
        ref.read(germanyAddressesProvider.notifier).setAddresses(addresses);
      }
    } catch (e) {
      if (mounted) {
        showErrorMessage('Error loading Germany addresses: $e');
      }
    } finally {
      if (mounted) {
        isLoadingGermany = false;
      }
    }
  }

  // استيراد عناوين ألمانيا
  Future<void> importGermanyAddresses() async {
    // إعادة تعيين متغيرات التقدم
    updateImportProgress(
      progress: 0.0,
      processedRows: 0,
      totalRows: 0,
      stage: 'Reading File',
    );
    isImportingGermany = true;

    // عرض مربع حوار التقدم
    showImportProgressDialog('Importing Germany Addresses');

    try {
      // استيراد البيانات من ملف الإكسل
      final addresses = await ExcelImportService.importGermanyAddresses(
        onError: (errorMessage) {
          if (mounted) {
            // إغلاق مربع حوار التقدم
            Navigator.of(context).pop();
            showErrorMessage(errorMessage, forceShow: true);
          }
        },
        onProgress: (progress, processedRows, totalRows) {
          if (mounted) {
            updateProgressAndShowDialog(
              title: 'Importing Germany Addresses',
              progress: progress,
              processedRows: processedRows,
              totalRows: totalRows,
              stage: 'Reading File',
            );
          }
        },
      );

      if (mounted && addresses.isNotEmpty) {
        // تحديث المرحلة
        updateProgressAndShowDialog(
          title: 'Importing Germany Addresses',
          progress: 0.0,
          processedRows: 0,
          totalRows: addresses.length,
          stage: 'Saving Data to Database',
        );

        // حفظ العناوين في قاعدة البيانات
        final addressDbService = ref.read(addressDatabaseServiceProvider);
        final result = await addressDbService.saveGermanyAddresses(
          addresses,
          skipExisting: true, // تجاهل العناصر المكررة
          onProgress: (progress, processedItems, totalItems) {
            if (mounted) {
              updateProgressAndShowDialog(
                title: 'Importing Germany Addresses',
                progress: progress,
                processedRows: processedItems,
                totalRows: totalItems,
                stage: 'Saving Data to Database',
              );
            }
          },
        );

        // إغلاق مربع حوار التقدم
        if (mounted) {
          Navigator.of(context).pop();
        }

        // تحديث حالة التطبيق
        await loadGermanyAddresses(); // إعادة تحميل العناوين من قاعدة البيانات

        // استخراج الإحصائيات
        final success = result['success'] as bool;

        if (success) {
          final stats = result['stats'] as Map<String, dynamic>;
          final totalImported = stats['totalImported'] as int;
          final newItems = stats['newItems'] as int;
          final skippedItems = stats['skippedItems'] as int;

          // عرض مربع حوار الإحصائيات
          showImportStatsDialog(
            'Germany Addresses Import Statistics',
            totalImported,
            newItems,
            skippedItems,
          );
        } else {
          // عرض رسالة الخطأ
          showErrorMessage(
            'Error saving addresses to database: ${result['error']}',
            forceShow: true,
          );
        }
      } else if (mounted) {
        // إغلاق مربع حوار التقدم
        Navigator.of(context).pop();
        showErrorMessage('No addresses were imported', forceShow: true);
      }
    } catch (e) {
      if (mounted) {
        // إغلاق مربع حوار التقدم
        Navigator.of(context).pop();
        showErrorMessage('Error importing addresses: $e', forceShow: true);
      }
    } finally {
      if (mounted) {
        isImportingGermany = false;
      }
    }
  }

  // حذف عنوان ألمانيا
  Future<void> deleteGermanyAddress(int index) async {
    try {
      // حذف العنوان من الحالة
      ref.read(germanyAddressesProvider.notifier).removeAddress(index);

      // تحديث قاعدة البيانات
      final addressDbService = ref.read(addressDatabaseServiceProvider);
      final addresses = ref.read(germanyAddressesProvider);
      final result = await addressDbService.saveGermanyAddresses(
        addresses,
        skipExisting: false, // نريد استبدال جميع البيانات
      );

      final success = result['success'] as bool;

      if (mounted) {
        if (success) {
          showSuccessMessage('Address deleted successfully');
        } else {
          showErrorMessage('Error deleting address');
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorMessage('Error deleting address: $e');
      }
    }
  }

  // حذف جميع عناوين ألمانيا
  Future<void> clearAllGermanyAddresses() async {
    // التحقق من وجود عناوين للحذف
    final germanyAddresses = ref.read(germanyAddressesProvider);
    if (germanyAddresses.isEmpty) {
      showErrorMessage('No addresses to delete');
      return;
    }

    // عرض مربع حوار التأكيد
    if (!mounted) return;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Delete All Germany Addresses'),
          ],
        ),
        content: Text(
          'Are you sure you want to delete all ${germanyAddresses.length} Germany addresses?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // عرض مؤشر التحميل
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Deleting all addresses...'),
            ],
          ),
        ),
      );

      // حذف البيانات من قاعدة البيانات
      final addressDbService = ref.read(addressDatabaseServiceProvider);
      final success = await addressDbService.clearAllGermanyAddresses();

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success) {
        // تحديث الحالة المحلية
        ref.read(germanyAddressesProvider.notifier).clearAddresses();

        if (mounted) {
          showSuccessMessage('All Germany addresses deleted successfully');
        }
      } else {
        if (mounted) {
          showErrorMessage('Error deleting all addresses');
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة حدوث خطأ
      if (mounted) {
        Navigator.of(context).pop();
        showErrorMessage('Error deleting all addresses: $e');
      }
    }
  }

  // تصدير عناوين ألمانيا إلى ملف Excel
  Future<void> exportGermanyAddresses() async {
    final germanyAddresses = ref.read(germanyAddressesProvider);

    if (germanyAddresses.isEmpty) {
      showErrorMessage('No addresses to export');
      return;
    }

    try {
      // عرض مؤشر التحميل
      showExportDialog();

      // تحضير البيانات للتصدير
      final headers = ['Postal Code', 'City Name'];
      final data = germanyAddresses
          .map((address) => [
                address.postalCode,
                address.cityName,
              ])
          .toList();

      // تصدير البيانات
      final filePath = await FileHelper.exportToExcel(
        sheetName: 'Germany Addresses',
        headers: headers,
        data: data,
        fileName: 'Germany_Addresses.xlsx',
      );

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة نجاح
      if (mounted && filePath != null) {
        showSuccessMessage('Addresses exported successfully to: $filePath');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة حدوث خطأ
      if (mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة الخطأ
      showErrorMessage('Error exporting addresses: $e');
    }
  }

  // بناء محتوى قسم عناوين ألمانيا
  Widget buildGermanyAddressesTab() {
    final germanyAddresses = ref.watch(germanyAddressesProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Germany Full Address section
          const Text(
            'Germany Full Addresses',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Import Excel button for Germany
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: isImportingGermany ? null : importGermanyAddresses,
                icon: isImportingGermany
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.upload_file),
                label: Text(
                    isImportingGermany ? 'Importing...' : 'Import Excel File'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const SizedBox(width: 16),

              // Export Excel button for Germany
              ElevatedButton.icon(
                onPressed:
                    germanyAddresses.isEmpty ? null : exportGermanyAddresses,
                icon: const Icon(Icons.download_rounded),
                label: const Text('Export Excel File'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  backgroundColor: Colors.green,
                ),
              ),

              const SizedBox(width: 16),

              // Delete All button for Germany
              ElevatedButton.icon(
                onPressed:
                    germanyAddresses.isEmpty ? null : clearAllGermanyAddresses,
                icon: const Icon(Icons.delete_sweep),
                label: const Text('Delete All'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),

              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'Tip: Maximum number of rows in the imported file should not exceed 5000 rows',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Germany addresses list
          Expanded(
            child: Center(
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.75,
                child: isLoadingGermany
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : germanyAddresses.isEmpty
                        ? const Center(
                            child: Text('No addresses imported'),
                          )
                        : PaginatedAddressList<dynamic>(
                            items: germanyAddresses,
                            columns: const ['Postal Code', 'City Name'],
                            columnWidths: const [0.4, 0.6],
                            itemToValues: (item) => [
                              item.postalCode,
                              item.cityName,
                            ],
                            onDelete: deleteGermanyAddress,
                            isLoading: isLoadingGermany,
                            emptyMessage: 'No addresses imported',
                            searchHint: 'Search Germany addresses...',
                          ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
