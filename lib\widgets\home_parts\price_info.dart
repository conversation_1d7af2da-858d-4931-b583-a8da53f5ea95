import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../services/code_data_service.dart';
import '../../services/event_bus_service.dart';
import '../../services/data_preload_service.dart';
import 'dart:async';

class PriceInfo extends StatefulWidget {
  final String? selectedCountry;
  final String? selectedCity;
  final CodeDataService codeDataService;
  final Function(double)? onPostSubCostChanged;

  const PriceInfo({
    super.key,
    required this.selectedCountry,
    required this.selectedCity,
    required this.codeDataService,
    this.onPostSubCostChanged,
  });

  @override
  State<PriceInfo> createState() => _PriceInfoState();
}

class _PriceInfoState extends State<PriceInfo> {
  // إنشاء مسجل خاص بهذا الكلاس
  final Logger _logger = Logger('PriceInfo');

  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _doorToDoorPriceController =
      TextEditingController();
  final TextEditingController _forEachKgController = TextEditingController();
  final TextEditingController _exchangeRateController = TextEditingController();
  final TextEditingController _minimumPriceController = TextEditingController();

  // إضافة خدمة ناقل الأحداث
  final EventBusService _eventBus = EventBusService();
  final DataPreloadService _dataPreloadService = DataPreloadService();
  StreamSubscription? _eventSubscription;

  @override
  void initState() {
    super.initState();
    // تحديث الأسعار عند بدء التشغيل
    _updatePrices();

    // إضافة مستمع لحقل Exchange Rate
    _exchangeRateController.addListener(_notifyExchangeRateChanged);

    // الاستماع إلى أحداث تغيير الأسعار
    _listenToDataChanges();
  }

  @override
  void dispose() {
    _doorToDoorPriceController.dispose();
    _forEachKgController.dispose();
    _exchangeRateController.dispose();
    _minimumPriceController.dispose();
    _eventSubscription?.cancel();
    super.dispose();
  }

  // دالة للاستماع إلى أحداث تغيير البيانات
  void _listenToDataChanges() {
    _eventSubscription = _eventBus.eventStream.listen((event) {
      _logger.info('تم استلام حدث: ${event.type}');

      // التعامل مع الأحداث المختلفة
      switch (event.type) {
        case EventType.priceAdded:
        case EventType.priceUpdated:
        case EventType.priceDeleted:
          _logger.info('تم تغيير بيانات الأسعار، جاري إعادة التحميل...');
          _refreshDataAndUpdatePrices();
          break;
        case EventType.dataRefreshNeeded:
          _logger.info('مطلوب تحديث البيانات بالكامل، جاري إعادة التحميل...');
          _refreshDataAndUpdatePrices();
          break;
        case EventType.countryAdded:
          _logger.info('تمت إضافة دولة جديدة، جاري تحديث البيانات...');
          _refreshDataAndUpdatePrices();
          break;
        case EventType.countryUpdated:
          _logger.info('تم تحديث بيانات دولة، جاري تحديث البيانات...');
          // تحديث سعر الصرف إذا كانت الدولة المحدثة هي الدولة الحالية
          if (event.data != null &&
              event.data?['country'] == widget.selectedCountry) {
            _logger.info(
                'الدولة المحدثة هي الدولة الحالية، جاري تحديث سعر الصرف...');
            _logger.info('بيانات الحدث: ${event.data}');

            // تحديث سعر الصرف من البيانات المحدثة
            if (event.data?['exchange_rate'] != null) {
              double exchangeRate = 0.0;
              try {
                if (event.data?['exchange_rate'] is double) {
                  exchangeRate = event.data?['exchange_rate'];
                } else {
                  exchangeRate =
                      double.parse(event.data!['exchange_rate'].toString());
                }

                if (exchangeRate > 0) {
                  _logger.info('تحديث سعر الصرف إلى: $exchangeRate');
                  setState(() {
                    _exchangeRateController.text = exchangeRate.toString();
                  });

                  // إخطار بتغيير سعر الصرف
                  _notifyExchangeRateChanged();
                }
              } catch (e) {
                _logger.severe('خطأ في تحديث سعر الصرف: $e');
              }
            }

            // تحديث جميع الحقول بعد تحديث سعر الصرف
            _refreshDataAndUpdatePrices();
          }
          break;
        case EventType.countryDeleted:
          _logger.info('تم حذف دولة، جاري تحديث البيانات...');
          _refreshDataAndUpdatePrices();
          break;
        case EventType.cityAdded:
        case EventType.cityUpdated:
        case EventType.cityDeleted:
          _logger.info('تم تغيير بيانات المدن، جاري تحديث البيانات...');
          _refreshDataAndUpdatePrices();
          break;
      }
    });
  }

  /// إعادة تحميل البيانات وتحديث الأسعار
  Future<void> _refreshDataAndUpdatePrices() async {
    try {
      _logger.info('🔄 بدء إعادة تحميل بيانات الأسعار...');

      // إعادة تحميل البيانات في DataPreloadService
      await _dataPreloadService.refreshData();

      // تحديث الأسعار بعد تحميل البيانات الجديدة
      _updatePrices();

      _logger.info('✅ تم تحديث بيانات الأسعار بنجاح');
    } catch (e) {
      _logger.severe('❌ خطأ في إعادة تحميل بيانات الأسعار: $e');
      // في حالة الخطأ، حاول تحديث الأسعار من البيانات الموجودة
      _updatePrices();
    }
  }

  @override
  void didUpdateWidget(PriceInfo oldWidget) {
    super.didUpdateWidget(oldWidget);
    // تحديث الأسعار عند تغيير الدولة أو المدينة
    if (oldWidget.selectedCountry != widget.selectedCountry ||
        oldWidget.selectedCity != widget.selectedCity) {
      _updatePrices();
    }
  }

  // تحديث الأسعار بناءً على الدولة والمدينة المختارة
  void _updatePrices() {
    try {
      _logger.info(
          'بدء تحديث الأسعار للدولة: ${widget.selectedCountry}, المدينة: ${widget.selectedCity}');

      // تحديث الأسعار من الخدمة
      final prices = _dataPreloadService.getPricesByCountryAndCity(
        widget.selectedCountry ?? '',
        widget.selectedCity ?? '',
      );

      if (prices != null) {
        _logger.info('تم الحصول على بيانات الأسعار: $prices');

        // تحديث جميع حقول الأسعار أولاً من البيانات المحملة مسبقًا
        setState(() {
          _doorToDoorPriceController.text =
              prices['price_door_to_door']?.toString() ?? '0';
          _forEachKgController.text =
              prices['for_each_1_kg']?.toString() ?? '0';
          _minimumPriceController.text =
              prices['minimum_price']?.toString() ?? '0';
          _exchangeRateController.text =
              prices['exchange_rate']?.toString() ?? '0';
        });

        _logger.info('تم تحديث حقول الأسعار من البيانات المحملة مسبقًا');
        _logger.info('Door to Door Price: ${_doorToDoorPriceController.text}');
        _logger.info('For Each Kg: ${_forEachKgController.text}');
        _logger.info('Minimum Price: ${_minimumPriceController.text}');
        _logger.info('Exchange Rate: ${_exchangeRateController.text}');

        // ثم محاولة الحصول على سعر الصرف المحدث من قاعدة البيانات
        try {
          widget.codeDataService
              .getExchangeRateByCountry(widget.selectedCountry ?? '')
              .then((exchangeRate) {
            _logger.info(
                'تم الحصول على سعر الصرف من قاعدة البيانات: $exchangeRate');

            if (exchangeRate > 0) {
              // تحديث سعر الصرف في واجهة المستخدم فقط إذا كان مختلفًا
              double currentRate =
                  double.tryParse(_exchangeRateController.text) ?? 0.0;
              if (currentRate != exchangeRate) {
                _logger
                    .info('تحديث سعر الصرف من $currentRate إلى $exchangeRate');
                setState(() {
                  _exchangeRateController.text = exchangeRate.toString();
                });

                // إخطار بتغيير سعر الصرف
                _notifyExchangeRateChanged();
              } else {
                _logger.info('سعر الصرف لم يتغير: $currentRate');
              }
            }
          }).catchError((error) {
            _logger.severe(
                'خطأ في الحصول على سعر الصرف من قاعدة البيانات: $error');
          });
        } catch (e) {
          _logger.severe('خطأ في الحصول على سعر الصرف: $e');
        }
      } else {
        _logger
            .warning('لم يتم العثور على بيانات أسعار للدولة والمدينة المحددة');
        _resetPrices();
      }
    } catch (e) {
      _logger.severe('خطأ في تحديث الأسعار: $e');
      _resetPrices();
    }
  }

  // إعادة تعيين الأسعار إلى صفر
  void _resetPrices() {
    setState(() {
      // لا نستخدم قيم افتراضية بعد الآن لأن الأسعار إجبارية عند إضافة المدينة
      _doorToDoorPriceController.text = '0';
      _logger.warning(
          'تم تعيين قيمة Door to Door Price إلى صفر عند إعادة تعيين الأسعار');

      _forEachKgController.text = '0';
      _minimumPriceController.text = '0';

      // لا نقوم بإعادة تعيين سعر الصرف هنا لأنه يجب أن يأتي من الدولة المختارة
    });
  }

  // دالة لإخطار عند تغيير قيمة Exchange Rate
  void _notifyExchangeRateChanged() {
    // تسجيل قيمة سعر الصرف الجديدة
    _logger.info('Exchange Rate changed to: ${_exchangeRateController.text}');

    // تحديث الحسابات في CostInfo
    final homeScreenState = context.findAncestorStateOfType<State>();
    if (homeScreenState != null) {
      try {
        final costInfoState =
            (homeScreenState as dynamic)._costInfoKey.currentState;
        if (costInfoState != null) {
          // إعادة حساب المجاميع
          (costInfoState as dynamic)._calculateTotals();
        }
      } catch (e) {
        _logger.severe('خطأ في تحديث حسابات CostInfo: $e');
      }
    }
  }

  // دالة للحصول على قيمة Door to Door Price
  double getDoorToDoorPrice() {
    try {
      double price = double.tryParse(_doorToDoorPriceController.text) ?? 0.0;
      _logger.info('تم طلب قيمة Door to Door Price: $price');
      return price;
    } catch (e) {
      _logger.severe('خطأ في الحصول على قيمة Door to Door Price: $e');
      return 0.0; // قيمة صفر في حالة الخطأ
    }
  }

  // دالة عامة للحصول على سعر Minimum price
  double getMinimumPrice() {
    try {
      final rawValue = _minimumPriceController.text;
      final parsedValue = double.tryParse(rawValue) ?? 0.0;
      _logger.info(
          'طلب قيمة Minimum Price: $parsedValue من النص الخام: $rawValue');
      return parsedValue;
    } catch (e) {
      _logger.severe('خطأ في الحصول على قيمة Minimum Price: $e');
      return 0.0; // قيمة صفر في حالة الخطأ
    }
  }

  // دالة عامة للحصول على سعر الصرف
  double getExchangeRate() {
    return double.tryParse(_exchangeRateController.text) ?? 1309.0;
  }

  // دالة لتعيين قيمة سعر الصرف
  void setExchangeRate(double value) {
    setState(() {
      _exchangeRateController.text = value.toString();
    });
  }

  // دالة لمسح حقول For each 1 Kg و Exchange Rate
  void clearPriceFields() {
    setState(() {
      // إعادة تحميل الأسعار بدون تعيين قيمة افتراضية لسعر الصرف
      // سيتم تحميل سعر الصرف من قاعدة البيانات للدولة المحددة
      _updatePrices();
    });
  }

  // دالة للحصول على قيمة For each 1 Kg
  double getForEachKgPrice() {
    try {
      double price = double.tryParse(_forEachKgController.text) ?? 0.0;
      _logger.info(
          'طلب قيمة For each 1 Kg: $price من النص: ${_forEachKgController.text}');
      return price;
    } catch (e) {
      _logger.severe('خطأ في الحصول على قيمة For each 1 Kg: $e');
      return 0.0; // قيمة صفر في حالة الخطأ
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Price Information',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),

            // حقل Door to door price مع ارتفاع محسن
            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _doorToDoorPriceController,
                decoration: const InputDecoration(
                  labelText: 'Door to door price',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                readOnly: true,
              ),
            ),
            const SizedBox(height: 8),

            // حقل For each 1 Kg مع ارتفاع محسن
            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _forEachKgController,
                decoration: const InputDecoration(
                  labelText: 'For each 1 Kg',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                readOnly: true,
              ),
            ),
            const SizedBox(height: 8),

            // حقل Minimum price مع ارتفاع محسن
            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _minimumPriceController,
                decoration: const InputDecoration(
                  labelText: 'Minimum price',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                readOnly: true,
              ),
            ),
            const SizedBox(height: 8),

            // حقل Exchange Rate مع ارتفاع محسن
            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _exchangeRateController,
                decoration: const InputDecoration(
                  labelText: 'Exchange Rate',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                keyboardType: TextInputType.number,
                readOnly: true,
                onChanged: (value) {
                  // تحديث الحسابات عند تغيير سعر الصرف
                  _notifyExchangeRateChanged();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
