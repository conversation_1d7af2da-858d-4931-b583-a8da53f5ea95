import 'package:flutter/material.dart';

class AddressListRow extends StatelessWidget {
  final List<String> values;
  final List<double> columnWidths;
  final VoidCallback? onDelete;
  final bool isAlternate;

  const AddressListRow({
    super.key,
    required this.values,
    required this.columnWidths,
    this.onDelete,
    this.isAlternate = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: isAlternate ? Colors.grey.withAlpha(13) : Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor.withAlpha(128),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          ...List.generate(
            values.length,
            (index) => Expanded(
              flex: (columnWidths[index] * 100).toInt(),
              child: Text(
                values[index],
                style: const TextStyle(fontSize: 13),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          if (onDelete != null)
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red, size: 18),
              onPressed: onDelete,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              splashRadius: 20,
            ),
        ],
      ),
    );
  }
}
