import 'package:flutter/material.dart';
import '../../services/code_data_service.dart';
import '../../services/event_bus_service.dart';
import '../../services/data_preload_service.dart';

class CountriesTab extends StatefulWidget {
  final CodeDataService codeDataService;

  const CountriesTab({super.key, required this.codeDataService});

  @override
  State<CountriesTab> createState() => _CountriesTabState();
}

class _CountriesTabState extends State<CountriesTab> {
  List<Map<String, dynamic>> _countries = [];
  List<Map<String, dynamic>> _filteredCountries = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  final EventBusService _eventBus = EventBusService();

  @override
  void initState() {
    super.initState();
    debugPrint('=== تهيئة CountriesTab ===');
    debugPrint('CodeDataService is available: ${widget.codeDataService}');
    _loadCountries();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  Future<void> _loadCountries() async {
    try {
      debugPrint('=== Start loading countries ===');
      debugPrint('Calling getAllCountriesWithStatus...');

      final countriesFromDB =
          await widget.codeDataService.getAllCountriesWithStatus();
      debugPrint(
          'Retrieved ${countriesFromDB.length} countries from the database');

      // إنشاء نسخة قابلة للتعديل من القائمة
      final uniqueCountries = List<Map<String, dynamic>>.from(
          countriesFromDB.map((country) => Map<String, dynamic>.from(country)));

      // التحقق من صحة البيانات وإضافة سعر الصرف الافتراضي إذا لزم الأمر
      for (var country in uniqueCountries) {
        if (country['exchange_rate'] == null) {
          country['exchange_rate'] = 1309.0;
        }
      }

      // ترتيب الدول أبجدياً
      uniqueCountries.sort(
          (a, b) => (a['country'] as String).compareTo(b['country'] as String));

      debugPrint('العدد النهائي للدول الفريدة: ${uniqueCountries.length}');
      for (var country in uniqueCountries) {
        final isEnabled = (country['is_enabled'] as int) == 1;
        debugPrint(
            'دولة: ${country['country']}, سعر الصرف: ${country['exchange_rate']}, الحالة: ${isEnabled ? 'مفعلة' : 'معطلة'}');
      }

      setState(() {
        _countries = uniqueCountries;
        _filteredCountries = uniqueCountries;
        _isLoading = false;
      });

      debugPrint('تم تحديث واجهة المستخدم بـ ${_countries.length} دولة');
    } catch (e) {
      debugPrint('خطأ في تحميل الدول: $e');
      setState(() {
        _countries = [];
        _filteredCountries = [];
        _isLoading = false;
      });
      _showErrorDialog('Error loading countries: $e');
    }
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = _countries;
      } else {
        _filteredCountries = _countries
            .where((country) => country['country']
                .toString()
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  Future<void> _addCountry() async {
    _countryController.clear();
    final TextEditingController exchangeRateController =
        TextEditingController();
    exchangeRateController.text = '1309'; // Default exchange rate value

    final dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (context) => AlertDialog(
        title: const Text('Add Country'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _countryController,
              decoration: const InputDecoration(
                labelText: 'Country Name',
                hintText: 'Enter Country Name',
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: exchangeRateController,
              decoration: const InputDecoration(
                labelText: 'Exchange Rate',
                hintText: 'Enter Exchange Rate',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              // Check if the country name and exchange rate are entered
              if (_countryController.text.isEmpty) {
                _showErrorDialog('Please enter a country name');
                return;
              }

              final exchangeRate = double.tryParse(exchangeRateController.text);
              if (exchangeRate == null || exchangeRate <= 0) {
                _showErrorDialog(
                    'Please enter a valid exchange rate greater than zero');
                return;
              }

              try {
                // التحقق من وجود الدولة قبل إضافتها
                final countryName = _countryController.text.trim();
                debugPrint('محاولة إضافة دولة جديدة: $countryName');

                final existingCountries =
                    await widget.codeDataService.getAllUniqueCountries();
                debugPrint(
                    'عدد الدول الموجودة حالياً: ${existingCountries.length}');

                final duplicateCountry = existingCountries
                    .where((c) =>
                        c['country'].toString().toLowerCase() ==
                        countryName.toLowerCase())
                    .toList();

                if (duplicateCountry.isNotEmpty) {
                  debugPrint('الدولة موجودة بالفعل: $countryName');
                  _showErrorDialog(
                      'This country already exists in the database. Please edit the existing record instead of adding a new one.');
                  return;
                }

                // إنشاء سجل جديد للدولة
                final codeNo =
                    'COUNTRY_${DateTime.now().millisecondsSinceEpoch}';
                debugPrint('تم إنشاء كود للدولة الجديدة: $codeNo');

                // التأكد من أن سعر الصرف هو double
                double finalExchangeRate = exchangeRate;

                final newCountry = {
                  'code_no': codeNo,
                  'country': countryName,
                  'exchange_rate': finalExchangeRate,
                  'created_at': DateTime.now().toIso8601String(),
                  'updated_at': DateTime.now().toIso8601String(),
                };

                debugPrint('بيانات الدولة الجديدة: $newCountry');

                // حفظ الدولة الجديدة في قاعدة البيانات
                debugPrint('بدء حفظ الدولة في قاعدة البيانات...');
                final insertResult =
                    await widget.codeDataService.insertCodeData(newCountry);
                debugPrint('نتيجة الحفظ: $insertResult');

                if (insertResult <= 0) {
                  debugPrint('فشل في حفظ الدولة في قاعدة البيانات');
                  _showErrorDialog('Failed to save country to database');
                  return;
                }

                debugPrint('تم حفظ الدولة بنجاح في قاعدة البيانات');

                // تحديث DataPreloadService لإعادة تحميل البيانات
                try {
                  final dataPreloadService = DataPreloadService();
                  await dataPreloadService.refreshData();
                  debugPrint('تم تحديث DataPreloadService بنجاح');
                } catch (e) {
                  debugPrint('خطأ في تحديث DataPreloadService: $e');
                }

                // Send a notification about adding a new country
                _eventBus.fireEvent(
                    AppEvent(EventType.countryAdded, data: newCountry));

                if (context.mounted) {
                  Navigator.pop(context);
                  _loadCountries();
                  _showSuccessSnackBar('Country added successfully');
                }
              } catch (e) {
                debugPrint('خطأ في إضافة الدولة: $e');
                if (context.mounted) {
                  Navigator.pop(context);
                  _showErrorDialog('Error adding country: $e');
                }
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );

    // Dispose of the controller after closing the dialog
    exchangeRateController.dispose();
  }

  Future<void> _editCountry(Map<String, dynamic> country) async {
    _countryController.text = country['country'];
    final TextEditingController exchangeRateController =
        TextEditingController();
    final dialogContext = context;

    // Try to get the current exchange rate for the country
    try {
      final countryData = await widget.codeDataService
          .getCodeDataByCode(country['code_no'] ?? '');
      if (countryData != null && countryData['exchange_rate'] != null) {
        // Asegurar que el valor se convierta correctamente a string
        var exchangeRate = countryData['exchange_rate'];
        if (exchangeRate is int) {
          exchangeRate = exchangeRate.toDouble();
        }
        exchangeRateController.text = exchangeRate.toString();
        debugPrint(
            'Tipo de cambio obtenido: $exchangeRate (${exchangeRate.runtimeType})');
      } else {
        exchangeRateController.text =
            '1309'; // Default value if there is no exchange rate
        debugPrint(
            'No se encontró tipo de cambio, usando valor predeterminado: 1309');
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
      exchangeRateController.text = '1309'; // Default value in case of error
    }

    if (!context.mounted) return;

    await showDialog(
      context: dialogContext,
      builder: (context) => AlertDialog(
        title: const Text('Edit Country'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _countryController,
              decoration: const InputDecoration(
                labelText: 'Country Name',
                hintText: 'Enter Country Name',
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: exchangeRateController,
              decoration: const InputDecoration(
                labelText: 'Exchange Rate',
                hintText: 'Enter Exchange Rate',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              // Check if the country name and exchange rate are entered
              if (_countryController.text.isEmpty) {
                _showErrorDialog('Please enter a country name');
                return;
              }

              // التحقق من صحة سعر الصرف وتحويله
              double? exchangeRate;
              try {
                // تنظيف النص من أي أحرف غير رقمية واستبدال الفاصلة بالنقطة
                final cleanedText =
                    exchangeRateController.text.trim().replaceAll(',', '.');

                // محاولة تحويل النص إلى رقم
                exchangeRate = double.parse(cleanedText);

                if (exchangeRate <= 0) {
                  _showErrorDialog(
                      'Please enter a valid exchange rate greater than zero');
                  return;
                }
              } catch (e) {
                _showErrorDialog('Please enter a valid numeric exchange rate');
                return;
              }

              try {
                // التحقق من وجود الدولة قبل تحديثها
                final countryName = _countryController.text.trim();
                final currentCountryName = country['country'] ?? '';

                // إذا تم تغيير اسم الدولة، تحقق من عدم وجود دولة أخرى بنفس الاسم
                if (countryName.toLowerCase() !=
                    currentCountryName.toString().toLowerCase()) {
                  final existingCountries =
                      await widget.codeDataService.getAllUniqueCountries();
                  final duplicateCountry = existingCountries
                      .where((c) =>
                          c['country'].toString().toLowerCase() ==
                              countryName.toLowerCase() &&
                          c['code_no'] != country['code_no'])
                      .toList();

                  if (duplicateCountry.isNotEmpty) {
                    _showErrorDialog(
                        'This country already exists in the database. Please choose a different name.');
                    return;
                  }
                }

                // استخدام الوظيفة المحسنة لتحديث جميع السجلات دفعة واحدة
                final updateCount =
                    await widget.codeDataService.updateCountryData(
                  country['country'].toString(),
                  countryName,
                  exchangeRate,
                );

                // تحديث DataPreloadService لإعادة تحميل البيانات
                try {
                  final dataPreloadService = DataPreloadService();
                  await dataPreloadService.refreshData();
                  debugPrint('تم تحديث DataPreloadService بعد تحديث الدولة');
                } catch (e) {
                  debugPrint(
                      'خطأ في تحديث DataPreloadService بعد تحديث الدولة: $e');
                }

                // إرسال إشعار بتحديث الدولة
                _eventBus.fireEvent(AppEvent(EventType.countryUpdated, data: {
                  'country': countryName,
                  'exchange_rate': exchangeRate,
                  'updated_count': updateCount,
                }));

                if (context.mounted) {
                  Navigator.pop(context);
                  _loadCountries();
                  _showSuccessSnackBar(
                      'Updated $updateCount records successfully for country "$countryName"');
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.pop(context);
                  _showErrorDialog('Error updating country: $e');
                }
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );

    // التخلص من المتحكم بعد إغلاق النافذة
    exchangeRateController.dispose();
  }

  Future<void> _deleteCountry(Map<String, dynamic> country) async {
    final dialogContext = context;
    final countryName = country['country']?.toString() ?? '';

    await showDialog(
      context: dialogContext,
      builder: (context) => AlertDialog(
        title: const Text('Delete Country'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete "$countryName"?'),
            const SizedBox(height: 8),
            const Text(
              'This will delete ALL records (cities, prices, etc.) associated with this country.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                debugPrint('=== بدء حذف الدولة: $countryName ===');

                // استخدام الدالة الجديدة لحذف جميع السجلات المرتبطة بالدولة
                final deletedCount =
                    await widget.codeDataService.deleteCountryData(countryName);

                debugPrint('تم حذف $deletedCount سجل للدولة: $countryName');

                if (deletedCount > 0) {
                  // تحديث DataPreloadService لإعادة تحميل البيانات
                  try {
                    final dataPreloadService = DataPreloadService();
                    await dataPreloadService.refreshData();
                    debugPrint('تم تحديث DataPreloadService بعد حذف الدولة');
                  } catch (e) {
                    debugPrint(
                        'خطأ في تحديث DataPreloadService بعد حذف الدولة: $e');
                  }

                  // إرسال إشعار بحذف الدولة
                  _eventBus.fireEvent(
                      AppEvent(EventType.countryDeleted, data: country));

                  if (context.mounted) {
                    Navigator.pop(context);
                    _loadCountries();
                    _showSuccessSnackBar(
                        'Country "$countryName" deleted successfully ($deletedCount records)');
                  }
                } else {
                  if (context.mounted) {
                    Navigator.pop(context);
                    _showErrorDialog(
                        'No records found for country "$countryName"');
                  }
                }
              } catch (e) {
                debugPrint('خطأ في حذف الدولة: $e');
                if (context.mounted) {
                  Navigator.pop(context);
                  _showErrorDialog('Error deleting country: $e');
                }
              }
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleCountryStatus(Map<String, dynamic> country) async {
    final String countryName = country['country'];
    final bool isCurrentlyEnabled = country['is_enabled'] == 1;
    final bool newStatus = !isCurrentlyEnabled;

    // رسالة التأكيد
    final String action = newStatus ? 'Activate' : 'Deactivate';
    final String message = newStatus
        ? 'Do you want to activate the country "$countryName"?\n\nIt will be displayed in the normal user interface and allow adding new records for it.'
        : 'Do you want to deactivate the country "$countryName"?\n\nIt will be hidden from the normal user interface and prevent adding new records for it, but the existing records will remain saved.';

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('$action Country'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: newStatus ? Colors.blue : Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(action),
            ),
          ],
        );
      },
    );

    if (confirmed != true) return;

    try {
      debugPrint('=== بدء $action الدولة: $countryName ===');

      if (newStatus) {
        // تفعيل الدولة
        await widget.codeDataService.enableCountryData(countryName);
        debugPrint('Country "$countryName" activated successfully');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Country "$countryName" activated successfully'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // تعطيل الدولة
        await widget.codeDataService.disableCountryData(countryName);
        debugPrint('Country "$countryName" deactivated successfully');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Country "$countryName" deactivated successfully'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }

      // تحديث DataPreloadService
      await DataPreloadService().refreshData();
      debugPrint('تم تحديث DataPreloadService بعد $action الدولة');

      // إعادة تحميل قائمة الدول
      await _loadCountries();
      debugPrint('تم إعادة تحميل قائمة الدول بعد $action الدولة');
    } catch (e) {
      debugPrint('خطأ في $action الدولة: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to $action country: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل بدلاً من الأعلى
        left: 0,
        right: 0,
        child: Center(
          // وسط الشاشة أفقياً
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: Colors.green.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min, // العرض حسب المحتوى فقط
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    // بدلاً من Expanded لتجنب أخذ العرض الكامل
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد 3 ثواني مع أنيميشن
    Future.delayed(const Duration(seconds: 3), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // تجاهل الخطأ إذا كان العنصر تم حذفه بالفعل
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                // زر إضافة دولة جديدة
                ElevatedButton.icon(
                  icon: const Icon(Icons.add, color: Colors.white, size: 24),
                  label: const Text(
                    'Add Country',
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  onPressed: _addCountry,
                ),
                const SizedBox(width: 10),
                // زر إعادة تحميل البيانات
                ElevatedButton.icon(
                  icon:
                      const Icon(Icons.refresh, color: Colors.white, size: 24),
                  label: const Text(
                    'Reload',
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  onPressed: () {
                    debugPrint('Reload countries list based on user request');
                    _loadCountries();
                  },
                ),
                const SizedBox(width: 10),
                // حقل البحث
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'Search',
                      hintText: 'Search for a country',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: _filterCountries,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredCountries.isEmpty
                    ? const Center(child: Text('No countries found'))
                    : ListView.builder(
                        itemCount: _filteredCountries.length,
                        itemBuilder: (context, index) {
                          final country = _filteredCountries[index];
                          final isEnabled =
                              (country['is_enabled'] as int?) == 1;

                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 8.0, vertical: 4.0),
                            color: isEnabled ? null : Colors.grey.shade100,
                            child: ListTile(
                              title: Row(
                                children: [
                                  Icon(
                                    Icons.flag,
                                    size: 20,
                                    color:
                                        isEnabled ? Colors.blue : Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    country['country'] ?? '',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: isEnabled
                                          ? Colors.black
                                          : Colors.grey,
                                      decoration: isEnabled
                                          ? null
                                          : TextDecoration.lineThrough,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: isEnabled
                                          ? Colors.green.shade100
                                          : Colors.red.shade100,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: isEnabled
                                            ? Colors.green
                                            : Colors.red,
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      isEnabled ? 'Active' : 'Disabled',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                        color: isEnabled
                                            ? Colors.green.shade700
                                            : Colors.red.shade700,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.currency_exchange,
                                        size: 16,
                                        color: isEnabled
                                            ? Colors.green
                                            : Colors.grey,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'Exchange Rate: ${country['exchange_rate'] != null ? country['exchange_rate'].toString() : "Not specified"}',
                                        style: TextStyle(
                                          color: isEnabled
                                              ? Colors.blue
                                              : Colors.grey,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // زر تعطيل/تفعيل الدولة
                                  Transform.scale(
                                    scale: 0.8,
                                    child: Theme(
                                      data: Theme.of(context).copyWith(
                                        switchTheme: SwitchThemeData(
                                          trackOutlineColor:
                                              WidgetStateProperty.all(
                                                  Colors.transparent),
                                          trackOutlineWidth:
                                              WidgetStateProperty.all(0.0),
                                        ),
                                      ),
                                      child: Switch(
                                        value: country['is_enabled'] == 1,
                                        onChanged: (bool value) {
                                          _toggleCountryStatus(country);
                                        },
                                        activeColor: Colors.white,
                                        activeTrackColor: Colors.blue,
                                        inactiveThumbColor: Colors.white,
                                        inactiveTrackColor: Colors.red,
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.edit,
                                      color:
                                          isEnabled ? Colors.blue : Colors.grey,
                                    ),
                                    onPressed: isEnabled
                                        ? () => _editCountry(country)
                                        : null,
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red),
                                    onPressed: () => _deleteCountry(country),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
