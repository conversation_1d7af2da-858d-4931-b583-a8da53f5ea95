import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logging/logging.dart';

class WeightInfo extends StatefulWidget {
  final Function()? onRealWeightChanged;

  final Function(int)? onBoxNoChanged;

  final Function(int)? onPalletNoChanged;

  const WeightInfo({
    super.key,
    this.onRealWeightChanged,
    this.onBoxNoChanged,
    this.onPalletNoChanged,
  });

  @override
  State<WeightInfo> createState() => _WeightInfoState();
}

class _WeightInfoState extends State<WeightInfo> {
  // Create a logger for this class

  final Logger _logger = Logger('WeightInfo');

  // Define text controllers for each field

  final TextEditingController _boxNoController = TextEditingController();

  final TextEditingController _palletNoController = TextEditingController();

  final TextEditingController _realWeightController = TextEditingController();

  final TextEditingController _heightController = TextEditingController();

  final TextEditingController _lengthController = TextEditingController();

  final TextEditingController _widthController = TextEditingController();

  final TextEditingController _volumeWeightController = TextEditingController();

  final TextEditingController _additionalKgController = TextEditingController();

  final TextEditingController _totalWeightController = TextEditingController();

  // Dropdown values

  final List<int> _factorValues = [5000, 6000];

  int _selectedFactor = 5000; // Default value

  // Checkbox state

  bool _useDimensions = false;

  @override
  void initState() {
    super.initState();

    // Set default values

    _boxNoController.text = '0';

    _palletNoController.text = '0';

    _realWeightController.text = '0';

    _heightController.text = '0';

    _lengthController.text = '0';

    _widthController.text = '0';

    _additionalKgController.text = '0';

    _volumeWeightController.text = '0';

    _totalWeightController.text = '0';

    // Add listeners to fields that affect calculations

    _boxNoController.addListener(_onBoxNoChanged);

    _palletNoController.addListener(_onPalletNoChanged);

    _realWeightController.addListener(_onRealWeightChanged);

    _heightController.addListener(_calculateVolumeWeight);

    _lengthController.addListener(_calculateVolumeWeight);

    _widthController.addListener(_calculateVolumeWeight);

    _additionalKgController.addListener(_calculateTotalWeight);
  }

  @override
  void dispose() {
    // Dispose text controllers

    _boxNoController.dispose();

    _palletNoController.dispose();

    _realWeightController.dispose();

    _heightController.dispose();

    _lengthController.dispose();

    _widthController.dispose();

    _volumeWeightController.dispose();

    _additionalKgController.dispose();

    _totalWeightController.dispose();

    super.dispose();
  }

  // Calculate volume weight

  void _calculateVolumeWeight() {
    if (!_useDimensions) return;

    try {
      double height = double.tryParse(_heightController.text) ?? 0;

      double length = double.tryParse(_lengthController.text) ?? 0;

      double width = double.tryParse(_widthController.text) ?? 0;

      // Calculate volume weight: (length × width × height) ÷ factor

      double volumeWeight = (height * length * width) / _selectedFactor;

      _volumeWeightController.text = volumeWeight.toStringAsFixed(2);

      // Update UI immediately to display the result
      setState(() {});

      _calculateTotalWeight();
    } catch (e) {
      _volumeWeightController.text = '0';
    }
  }

  // Public function to calculate volume weight that can be called externally

  void calculateVolumeWeight() {
    _calculateVolumeWeight();
  }

  // Public function to calculate total weight that can be called externally
  double calculateTotalWeight() {
    double realWeight = double.tryParse(_realWeightController.text) ?? 0;
    double additionalKg = double.tryParse(_additionalKgController.text) ?? 0;
    double totalWeight = realWeight + additionalKg;

    _logger.info(
        'استدعاء دالة calculateTotalWeight من الخارج: Real weight = $realWeight, Additional weight = $additionalKg, Total weight = $totalWeight');

    return totalWeight;
  }

  // Calculate total weight internally
  void _calculateTotalWeight() {
    try {
      double realWeight = double.tryParse(_realWeightController.text) ?? 0;
      double additionalKg = double.tryParse(_additionalKgController.text) ?? 0;

      // Total weight = real weight + additional weight
      double totalWeight = realWeight + additionalKg;

      // جلب القيمة السابقة للوزن الإجمالي
      double previousTotalWeight =
          double.tryParse(_totalWeightController.text) ?? 0;

      // تحديث القيمة في الحقل فقط إذا كانت هناك تغييرات
      if (totalWeight != previousTotalWeight) {
        _totalWeightController.text = totalWeight.toStringAsFixed(2);

        // طباعة تشخيصية
        _logger.info(
            'تم تغيير الوزن الإجمالي من $previousTotalWeight إلى $totalWeight');
      } else {
        _logger.info('لم يتغير الوزن الإجمالي (لا يزال $totalWeight)');
      }

      // Update UI immediately to display the result
      setState(() {});

      _logger.info(
          'Calculate total weight: Real weight = $realWeight, Additional weight = $additionalKg, Total weight = $totalWeight');

      // Call external function to notify related fields of the change
      if (widget.onRealWeightChanged != null &&
          totalWeight != previousTotalWeight) {
        widget.onRealWeightChanged!();

        // طباعة تشخيصية
        _logger.info('تم استدعاء onRealWeightChanged');
      }

      // محاولة تحديث Door to Door Cost بطريقة أكثر أمانًا
      try {
        // البحث عن HomeScreen باستخدام context
        final context = this.context;

        // طريقة 1: محاولة العثور على _HomeScreenState مباشرة
        var homeScreenState = context
                    .findAncestorStateOfType<State>()
                    ?.runtimeType
                    .toString()
                    .contains('_HomeScreenState') ==
                true
            ? context.findAncestorStateOfType<State>()
            : null;

        if (homeScreenState != null) {
          _logger.info('تم العثور على _HomeScreenState مباشرة');
          try {
            // تحديث حسابات السعر فقط إذا كان الوزن قد تغير
            if (totalWeight != previousTotalWeight) {
              // تحديث Door to Door Cost
              (homeScreenState as dynamic).updateDoorToDoorCost(totalWeight);
              _logger.info(
                  'تم استدعاء updateDoorToDoorCost بنجاح مع الوزن: $totalWeight');

              // تأكيد استدعاء onRealWeightChanged
              (homeScreenState as dynamic).onRealWeightChanged();
              _logger.info(
                  'تم استدعاء onRealWeightChanged يدوياً للتأكد من تحديث حسابات السعر');
            } else {
              _logger.info('لم يتم تحديث Door to Door Cost لأن الوزن لم يتغير');
            }
            return;
          } catch (e) {
            _logger.severe('خطأ في استدعاء updateDoorToDoorCost مباشرة: $e');
          }
        }

        // طريقة 2: محاولة العثور على أي State يحتوي على دالة updateDoorToDoorCost
        BuildContext? currentContext = context;
        while (currentContext != null) {
          final state = currentContext.findAncestorStateOfType<State>();
          if (state == null) break;

          try {
            _logger.info(
                'محاولة استدعاء updateDoorToDoorCost من ${state.runtimeType}');

            // تحديث فقط إذا كان الوزن قد تغير
            if (totalWeight != previousTotalWeight) {
              (state as dynamic).updateDoorToDoorCost(totalWeight);
              _logger.info(
                  'تم استدعاء updateDoorToDoorCost بنجاح من ${state.runtimeType}');

              // تأكيد استدعاء onRealWeightChanged
              (state as dynamic).onRealWeightChanged();
              _logger.info(
                  'تم استدعاء onRealWeightChanged يدوياً من ${state.runtimeType}');
            }
            break;
          } catch (e) {
            _logger.info(
                'لا يمكن استدعاء updateDoorToDoorCost من ${state.runtimeType}');
            // استمر في البحث عن State أخرى
            currentContext = state.context;
          }
        }
      } catch (e) {
        _logger.severe('خطأ في محاولة تحديث Door to Door Cost: $e');
      }
    } catch (e) {
      _logger.severe('Error calculating total weight: $e');
      _totalWeightController.text = '0';
    }
  }

  // Function called when real weight changes

  void _onRealWeightChanged() {
    try {
      // Update total weight immediately
      _calculateTotalWeight();

      // No need to call setState here as it's already called in _calculateTotalWeight
      // No need to call widget.onRealWeightChanged here as it's already called in _calculateTotalWeight
    } catch (e) {
      _logger.severe('Error in _onRealWeightChanged function: $e');
    }
  }

  // Function called when box no value changes

  void _onBoxNoChanged() {
    // Call external function if it exists

    if (widget.onBoxNoChanged != null) {
      final boxNo = int.tryParse(_boxNoController.text) ?? 0;

      widget.onBoxNoChanged!(boxNo);
    }
  }

  // Function called when pallet no value changes

  void _onPalletNoChanged() {
    // Call external function if it exists
    if (widget.onPalletNoChanged != null) {
      final palletNo = int.tryParse(_palletNoController.text) ?? 0;
      widget.onPalletNoChanged!(palletNo);
    }
  }

  // Function to get box no value

  int getBoxNo() {
    return int.tryParse(_boxNoController.text) ?? 0;
  }

  // Function to get number of pallets

  int getPalletNo() {
    return int.tryParse(_palletNoController.text) ?? 0;
  }

  // Function to get real weight

  double getRealWeight() {
    return double.tryParse(_realWeightController.text) ?? 0;
  }

  // Function to check if dimensions are enabled

  bool getUseDimensions() {
    return _useDimensions;
  }

  // Function to get height

  double getHeight() {
    return double.tryParse(_heightController.text) ?? 0;
  }

  // Function to get length

  double getLength() {
    return double.tryParse(_lengthController.text) ?? 0;
  }

  // Function to get width

  double getWidth() {
    return double.tryParse(_widthController.text) ?? 0;
  }

  // Function to get volume weight

  double getVolumeWeight() {
    return double.tryParse(_volumeWeightController.text) ?? 0;
  }

  // Function to get selected factor

  int getSelectedFactor() {
    return _selectedFactor;
  }

  // Function to get additional weight

  double getAdditionalKg() {
    return double.tryParse(_additionalKgController.text) ?? 0;
  }

  // Function to get total weight

  double getTotalWeight() {
    return double.tryParse(_totalWeightController.text) ?? 0;
  }

  // Function to set box no

  void setBoxNo(int value) {
    if (value == 0) {
      _boxNoController.text = '';
    } else {
      _boxNoController.text = value.toString();
    }
  }

  // Function to set pallet no

  void setPalletNo(int value) {
    if (value == 0) {
      _palletNoController.text = '';
    } else {
      _palletNoController.text = value.toString();
    }
  }

  // Function to set real weight

  void setRealWeight(double value) {
    if (value == 0) {
      _realWeightController.text = '';
    } else {
      _realWeightController.text = value.toString();
    }

    // حساب الوزن الكلي بعد تعيين قيمة الوزن الحقيقي
    if (mounted) {
      // التحقق مما إذا كان يتم استدعاء هذه الدالة أثناء تهيئة الحقول
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _calculateTotalWeight();

          // ضمان تحديث Door to Door Cost
          try {
            final totalWeight = getTotalWeight();

            // البحث عن HomeScreen باستخدام context
            final context = this.context;
            var homeScreenState = context
                        .findAncestorStateOfType<State>()
                        ?.runtimeType
                        .toString()
                        .contains('_HomeScreenState') ==
                    true
                ? context.findAncestorStateOfType<State>()
                : null;

            if (homeScreenState != null) {
              (homeScreenState as dynamic).updateDoorToDoorCost(totalWeight);
              _logger.info(
                  'تم تحديث Door to Door Cost بعد تعيين الوزن الحقيقي مباشرة');
            }
          } catch (e) {
            _logger.severe(
                'خطأ في تحديث Door to Door Cost بعد تعيين الوزن الحقيقي: $e');
          }
        }
      });
    }
  }

  // Function to set use dimensions

  void setUseDimensions(bool value) {
    setState(() {
      _useDimensions = value;

      if (!_useDimensions) {
        // If dimensions are disabled, reset dimension fields to zero
        _heightController.text = '0';
        _lengthController.text = '0';
        _widthController.text = '0';
        _volumeWeightController.text = '0';
        _additionalKgController.text = '0'; // Reset additional kg

        // Recalculate total weight after resetting values
        _calculateTotalWeight();
      } else {
        // If dimensions are enabled, set default values (40 - 40 - 60)
        _heightController.text = '40';
        _lengthController.text = '40';
        _widthController.text = '60';

        // Recalculate volume weight
        _calculateVolumeWeight();
      }
    });
  }

  // Function to set additional kg

  void setAdditionalKg(double value) {
    if (value == 0) {
      _additionalKgController.text = '';
    } else {
      _additionalKgController.text = value.toString();
    }

    // Recalculate total weight immediately after setting additional kg
    if (mounted) {
      _calculateTotalWeight();
    }
  }

  // Function to set height

  void setHeight(double value) {
    if (value == 0) {
      _heightController.text = '';
    } else {
      _heightController.text = value.toString();
    }
  }

  // Function to set length

  void setLength(double value) {
    if (value == 0) {
      _lengthController.text = '';
    } else {
      _lengthController.text = value.toString();
    }
  }

  // Function to set width

  void setWidth(double value) {
    if (value == 0) {
      _widthController.text = '';
    } else {
      _widthController.text = value.toString();
    }
  }

  // Function to set selected factor

  void setSelectedFactor(int value) {
    if (_factorValues.contains(value)) {
      setState(() {
        _selectedFactor = value;
      });
    }
  }

  // دالة لإعادة تعيين جميع الحقول إلى قيمها الافتراضية
  void resetFields() {
    try {
      // إعادة تعيين قيم الحقول
      _boxNoController.text = '0';
      _palletNoController.text = '0';
      _realWeightController.text = '0';
      _heightController.text = '0';
      _lengthController.text = '0';
      _widthController.text = '0';
      _additionalKgController.text = '0';
      _volumeWeightController.text = '0';
      _totalWeightController.text = '0';

      // إعادة تعيين القيم الأخرى
      _selectedFactor = 5000; // القيمة الافتراضية
      _useDimensions = false;

      // إعادة حساب الأوزان
      _calculateVolumeWeight();
      _calculateTotalWeight();

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }

      _logger.info('تم إعادة تعيين جميع حقول الوزن');
    } catch (e) {
      _logger.severe('خطأ في إعادة تعيين حقول الوزن: $e');
    }
  }

  // دالة لمسح حقول الوزن المطلوبة بعد حذف السجل
  void clearWeightFields() {
    try {
      // مسح قيم الحقول المطلوبة
      _boxNoController.text = '0';
      _palletNoController.text = '0';
      _realWeightController.text = '0';

      // إعادة تعيين القيم الأخرى
      _useDimensions = false;

      // إعادة تعيين حقول الأبعاد
      _heightController.text = '0';
      _lengthController.text = '0';
      _widthController.text = '0';
      _additionalKgController.text = '0';
      _volumeWeightController.text = '0';
      _totalWeightController.text = '0';

      // إعادة حساب الأوزان
      _calculateVolumeWeight();
      _calculateTotalWeight();

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }

      _logger.info('تم مسح حقول الوزن المطلوبة بعد حذف السجل');
    } catch (e) {
      _logger.severe('خطأ في مسح حقول الوزن: $e');
    }
  }

  // Function to set total weight

  void setTotalWeight(double value) {
    if (value == 0) {
      _totalWeightController.text = '';
    } else {
      _totalWeightController.text = value.toString();
    }

    // تحديث Door to Door Cost بعد تعيين الوزن الكلي
    if (mounted) {
      // استدعاء updateDoorToDoorCost بالوزن الكلي الجديد
      try {
        final totalWeight = double.tryParse(_totalWeightController.text) ?? 0;

        // البحث عن HomeScreen باستخدام context
        final context = this.context;
        var homeScreenState = context
                    .findAncestorStateOfType<State>()
                    ?.runtimeType
                    .toString()
                    .contains('_HomeScreenState') ==
                true
            ? context.findAncestorStateOfType<State>()
            : null;

        if (homeScreenState != null) {
          (homeScreenState as dynamic).updateDoorToDoorCost(totalWeight);
          _logger
              .info('تم تحديث Door to Door Cost بعد تعيين الوزن الكلي مباشرة');
        }
      } catch (e) {
        _logger
            .severe('خطأ في تحديث Door to Door Cost بعد تعيين الوزن الكلي: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4.r,
      margin: EdgeInsets.symmetric(vertical: 0.h, horizontal: 0.w),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Weight Information',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),

            SizedBox(height: 12.h),

            // First row: Box No and Pallet No

            Row(
              children: [
                // Box No field مع ارتفاع محسن

                Expanded(
                  child: SizedBox(
                    height: 60, // زيادة ارتفاع الحقل لراحة أكبر
                    child: TextField(
                      controller: _boxNoController,
                      decoration: const InputDecoration(
                        labelText: 'Box No',
                        labelStyle: TextStyle(fontSize: 13),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          _boxNoController.text = '';
                        }
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Pallet No field مع ارتفاع محسن

                Expanded(
                  child: SizedBox(
                    height: 60, // زيادة ارتفاع الحقل لراحة أكبر
                    child: TextField(
                      controller: _palletNoController,
                      decoration: const InputDecoration(
                        labelText: 'Pallet No',
                        labelStyle: TextStyle(fontSize: 13),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          _palletNoController.text = '';
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Real Weight KG field مع ارتفاع محسن

            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _realWeightController,
                decoration: const InputDecoration(
                  labelText: 'Real Weight KG',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                onChanged: (value) {
                  if (value.isEmpty) {
                    _realWeightController.text = '';
                  }
                  // حساب الوزن الكلي مباشرة عند تغيير قيمة الوزن الحقيقي
                  _calculateTotalWeight();
                },
              ),
            ),

            const SizedBox(height: 8),

            // Third row: Dimension fields (H - L - W) with checkbox

            Row(
              children: [
                // Height H field مع ارتفاع محسن

                Expanded(
                  child: SizedBox(
                    height: 60, // زيادة ارتفاع الحقل لراحة أكبر
                    child: TextField(
                      controller: _heightController,
                      decoration: const InputDecoration(
                        labelText: 'H',
                        labelStyle: TextStyle(fontSize: 13),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (value) {
                        if (value.isEmpty) {
                          _heightController.text = '';
                        }
                      },
                      enabled: _useDimensions,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Length L field مع ارتفاع محسن

                Expanded(
                  child: SizedBox(
                    height: 60, // زيادة ارتفاع الحقل لراحة أكبر
                    child: TextField(
                      controller: _lengthController,
                      decoration: const InputDecoration(
                        labelText: 'L',
                        labelStyle: TextStyle(fontSize: 13),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (value) {
                        if (value.isEmpty) {
                          _lengthController.text = '';
                        }
                      },
                      enabled: _useDimensions,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Width W field مع ارتفاع محسن

                Expanded(
                  child: SizedBox(
                    height: 60, // زيادة ارتفاع الحقل لراحة أكبر
                    child: TextField(
                      controller: _widthController,
                      decoration: const InputDecoration(
                        labelText: 'W',
                        labelStyle: TextStyle(fontSize: 13),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (value) {
                        if (value.isEmpty) {
                          _widthController.text = '';
                        }
                      },
                      enabled: _useDimensions,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Checkbox to enable/disable dimension fields

                SizedBox(
                  width: 24,
                  height: 24,
                  child: Checkbox(
                    value: _useDimensions,
                    onChanged: (value) {
                      setUseDimensions(value ?? false);
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Factor (dropdown) field مع ارتفاع محسن

            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'Factor',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
                value: _selectedFactor,
                items: _factorValues.map((factor) {
                  return DropdownMenuItem<int>(
                    value: factor,
                    child: Text(factor.toString()),
                  );
                }).toList(),
                onChanged: _useDimensions
                    ? (value) {
                        setState(() {
                          _selectedFactor = value ?? 5000;

                          _calculateVolumeWeight();
                        });
                      }
                    : null,
              ),
            ),

            const SizedBox(height: 8),

            // Volume Weight field مع ارتفاع محسن

            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _volumeWeightController,
                decoration: const InputDecoration(
                  labelText: 'Volume Weight',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                readOnly: true,
              ),
            ),

            const SizedBox(height: 8),

            // Additional KG field مع ارتفاع محسن

            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _additionalKgController,
                decoration: const InputDecoration(
                  labelText: 'Additional KG',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                onChanged: (value) {
                  if (value.isEmpty) {
                    _additionalKgController.text = '';
                  }
                },
                enabled: _useDimensions,
              ),
            ),

            const SizedBox(height: 8),

            // Total Weight KG field مع ارتفاع محسن

            SizedBox(
              height: 60, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextField(
                controller: _totalWeightController,
                decoration: const InputDecoration(
                  labelText: 'Total Weight KG',
                  labelStyle: TextStyle(fontSize: 13),
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 18),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                readOnly: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
