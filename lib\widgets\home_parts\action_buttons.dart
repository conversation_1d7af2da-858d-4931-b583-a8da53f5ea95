import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ActionButtons extends StatelessWidget {
  final VoidCallback onSave;
  final VoidCallback onUpdate;
  final VoidCallback onClear;
  final VoidCallback onInPrint;
  final VoidCallback onPoPrint;
  final Map<String, dynamic> currentData;
  final BuildContext context;
  final bool isSaveEnabled;
  final bool isUpdateEnabled;

  const ActionButtons({
    super.key,
    required this.onSave,
    required this.onUpdate,
    required this.onClear,
    required this.onInPrint,
    required this.onPoPrint,
    required this.currentData,
    required this.context,
    this.isSaveEnabled = true,
    this.isUpdateEnabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 4.r,
            offset: Offset(0, -2.h),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Main buttons on the left
          Expanded(
            flex: 6,
            child: Row(
              children: [
                // Save button (first button on the left)
                Expanded(
                  flex: 1,
                  child: ElevatedButton.icon(
                    icon: Icon(Icons.save, color: Colors.white, size: 18.sp),
                    label: Text('Save',
                        style: TextStyle(color: Colors.white, fontSize: 12.sp)),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      backgroundColor: Colors.green,
                      disabledBackgroundColor: Colors.grey,
                    ),
                    onPressed: isSaveEnabled ? onSave : null,
                  ),
                ),

                SizedBox(width: 8.w),

                // Update button (second button)
                Expanded(
                  flex: 1,
                  child: ElevatedButton.icon(
                    icon: Icon(Icons.update, color: Colors.white, size: 18.sp),
                    label: Text('Update',
                        style: TextStyle(color: Colors.white, fontSize: 12.sp)),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      backgroundColor: Colors.blue,
                      disabledBackgroundColor: Colors.grey,
                    ),
                    onPressed: isUpdateEnabled ? onUpdate : null,
                  ),
                ),

                SizedBox(width: 8.w),

                // In Print button (third button)
                Expanded(
                  flex: 1,
                  child: ElevatedButton.icon(
                    icon: Icon(Icons.print, color: Colors.white, size: 18.sp),
                    label: Text('Make Label',
                        style: TextStyle(color: Colors.white, fontSize: 12.sp)),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      backgroundColor: Colors.purple,
                    ),
                    onPressed: onInPrint,
                  ),
                ),

                SizedBox(width: 8.w),

                // Po Print button (fourth button)
                Expanded(
                  flex: 1,
                  child: ElevatedButton.icon(
                    icon: Icon(Icons.print_outlined,
                        color: Colors.white, size: 18.sp),
                    label: Text('Make Invoice',
                        style: TextStyle(color: Colors.white, fontSize: 12.sp)),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      backgroundColor: Colors.teal,
                    ),
                    onPressed: onPoPrint,
                  ),
                ),
              ],
            ),
          ),

          // Space between button groups
          SizedBox(width: 16.w),

          // Clear button (on the right side)
          Expanded(
            flex: 1,
            child: ElevatedButton.icon(
              icon: Icon(Icons.clear_all, color: Colors.white, size: 24.sp),
              label: Text('Clear',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold)),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
                backgroundColor: Colors.red,
              ),
              onPressed: onClear,
            ),
          ),
        ],
      ),
    );
  }
}
