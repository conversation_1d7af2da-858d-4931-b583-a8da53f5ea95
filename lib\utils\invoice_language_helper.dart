import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مساعد إدارة لغات الفاتورة
class InvoiceLanguageHelper {
  static const String _languageKey = 'invoice_language';

  /// اللغات المدعومة
  static const String arabic = 'ar';
  static const String kurdish = 'ku';
  static const String english = 'en';

  /// الحصول على اللغة المحفوظة
  static Future<String> getSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_languageKey) ?? arabic; // العربية افتراضياً
    } catch (e) {
      return arabic;
    }
  }

  /// حفظ اللغة المختارة
  static Future<void> saveLanguage(String language) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, language);
    } catch (e) {
      debugPrint('خطأ في حفظ لغة الفاتورة: $e');
    }
  }

  /// الحصول على اتجاه النص حسب اللغة
  static TextDirection getTextDirection(String language) {
    // جميع اللغات تستخدم اتجاه من اليمين لليسار
    return TextDirection.rtl;
  }

  /// الحصول على اسم اللغة للعرض
  static String getLanguageDisplayName(String language) {
    switch (language) {
      case arabic:
        return 'العربية';
      case kurdish:
        return 'کوردی';
      case english:
        return 'English';
      default:
        return 'العربية';
    }
  }

  /// قائمة اللغات المتاحة
  static List<Map<String, String>> getAvailableLanguages() {
    return [
      {'code': arabic, 'name': 'العربية'},
      {'code': kurdish, 'name': 'کوردی'},
      {'code': english, 'name': 'English'},
    ];
  }
}

/// ترجمات نصوص الفاتورة
class InvoiceTranslations {
  static const Map<String, Map<String, String>> _translations = {
    // معلومات الشركة والفرع
    'company_name': {
      'ar': 'شركة ستيرس للنقل الدولي',
      'ku': 'کۆمپانیای ستێرس بۆ گواستنەوەی نێودەوڵەتی',
      'en': 'EUKnet Transport Company',
    },
    'branch': {
      'ar': 'الفرع',
      'ku': 'لق',
      'en': 'Branch',
    },
    'address': {
      'ar': 'العنوان',
      'ku': 'ناونیشان',
      'en': 'Address',
    },
    'phone': {
      'ar': 'الهاتف',
      'ku': 'تەلەفۆن',
      'en': 'Phone',
    },
    'date': {
      'ar': 'التاريخ',
      'ku': 'بەروار',
      'en': 'Date',
    },
    'code_no': {
      'ar': 'رقم الكود',
      'ku': 'ژمارەی کۆد',
      'en': 'Code No',
    },
    'truck_no': {
      'ar': 'رقم الشحنة',
      'ku': 'ژمارەی بارگە',
      'en': 'Truck No',
    },

    // معلومات المرسل
    'sender_info': {
      'ar': 'معلومات المرسل',
      'ku': 'زانیاری نێردەر',
      'en': 'Sender Information',
    },
    'sender_name': {
      'ar': 'اسم المرسل',
      'ku': 'ناوی نێردەر',
      'en': 'Sender Name',
    },
    'sender_phone': {
      'ar': 'رقم المرسل',
      'ku': 'ژمارەی نێردەر',
      'en': 'Sender Phone',
    },
    'sender_address': {
      'ar': 'عنوان المرسل',
      'ku': 'ناونیشانی نێردەر',
      'en': 'Sender Address',
    },

    // معلومات المستلم
    'receiver_info': {
      'ar': 'معلومات المستلم',
      'ku': 'زانیاری وەرگر',
      'en': 'Receiver Information',
    },
    'receiver_name': {
      'ar': 'اسم المستلم',
      'ku': 'ناوی وەرگر',
      'en': 'Receiver Name',
    },
    'receiver_phone': {
      'ar': 'رقم المستلم',
      'ku': 'ژمارەی وەرگر',
      'en': 'Receiver Phone',
    },
    'receiver_address': {
      'ar': 'عنوان المستلم',
      'ku': 'ناونیشانی وەرگر',
      'en': 'Receiver Address',
    },
    'city_name': {
      'ar': 'اسم المدينة',
      'ku': 'ناوی شار',
      'en': 'City Name',
    },
    'postal_code': {
      'ar': 'الرمز البريدي',
      'ku': 'کۆدی پۆست',
      'en': 'Postal Code',
    },
    'country': {
      'ar': 'الدولة',
      'ku': 'وڵات',
      'en': 'Country',
    },

    // تفاصيل التكلفة
    'cost_details': {
      'ar': 'تفاصيل تكلفة النقل',
      'ku': 'وردەکاری تێچووی گواستنەوە',
      'en': 'Transport Cost Details',
    },
    'transport_cost': {
      'ar': 'كلفة النقل',
      'ku': 'تێچووی گواستنەوە',
      'en': 'Transport Cost',
    },
    'box_packing_cost': {
      'ar': 'كلفة الكارتون الفارغ',
      'ku': 'تێچووی کارتۆنی بەتاڵ',
      'en': 'Empty Box Cost',
    },
    'customs_admin': {
      'ar': 'الكمرك والاداريات',
      'ku': 'گومرک و کاروباری',
      'en': 'Customs & Admin',
    },
    'door_to_door': {
      'ar': 'النقل الداخلي الى عنوان المستلم',
      'ku': 'گواستنەوەی ناوخۆیی بۆ ناونیشانی وەرگر',
      'en': 'Door to Door Delivery',
    },
    'insurance_cost': {
      'ar': 'كلفة التأمين',
      'ku': 'تێچووی بیمە',
      'en': 'Insurance Cost',
    },
    'total_cost': {
      'ar': 'الكلفة الكلية',
      'ku': 'کۆی گشتی تێچوو',
      'en': 'Total Cost',
    },
    'paid_amount': {
      'ar': 'المبلغ المدفوع',
      'ku': 'بڕی پارەی دراو',
      'en': 'Paid Amount',
    },
    'required_amount': {
      'ar': 'المبلغ المطلوب',
      'ku': 'بڕی پارەی پێویست',
      'en': 'Required Amount',
    },
    'required_eur': {
      'ar': 'المبلغ المطلوب دفعه في اوروبا',
      'ku': 'بڕی پارەی پێویست لە ئەوروپا',
      'en': 'Amount Required in Europe',
    },
    'iraqi_dinar': {
      'ar': 'دينار عراقي',
      'ku': 'دیناری عێراقی',
      'en': 'Iraqi Dinar',
    },

    // تفاصيل البضاعة
    'goods_details': {
      'ar': 'تفاصيل البضاعة',
      'ku': 'وردەکاری کاڵا',
      'en': 'Goods Details',
    },
    'goods_description': {
      'ar': 'تفاصيل البضاعة',
      'ku': 'وردەکاری کاڵا',
      'en': 'Goods Description',
    },
    'goods_value': {
      'ar': 'قيمة البضاعة',
      'ku': 'بەهایى کاڵا',
      'en': 'Goods Value',
    },
    'weight': {
      'ar': 'الوزن',
      'ku': 'کێش',
      'en': 'Weight',
    },
    'quantity': {
      'ar': 'عدد القطع',
      'ku': 'ژمارەی پارچە',
      'en': 'Quantity',
    },
    'dimensions': {
      'ar': 'الأبعاد',
      'ku': 'ئەندازە',
      'en': 'Dimensions',
    },
    'kg': {
      'ar': 'كغ',
      'ku': 'کیلۆگرام',
      'en': 'kg',
    },
    'cm': {
      'ar': 'سم',
      'ku': 'سم',
      'en': 'cm',
    },

    // معلومات الوكيل
    'agent_info': {
      'ar': 'معلومات الوكيل',
      'ku': 'زانیاری بريكار',
      'en': 'Agent Information',
    },

    // التأمين
    'insurance_question': {
      'ar': 'هل تم تأمين البضاعة؟',
      'ku': 'ئایا کاڵاکە بیمە کراوە؟',
      'en': 'Is the goods insured?',
    },
    'yes': {
      'ar': 'نعم',
      'ku': 'بەڵێ',
      'en': 'Yes',
    },
    'no': {
      'ar': 'لا',
      'ku': 'نەخێر',
      'en': 'No',
    },

    // الشروط والأحكام
    'terms_conditions': {
      'ar': 'الشروط والأحكام',
      'ku': 'مەرج و ڕێکخستنەکان',
      'en': 'Terms and Conditions',
    },
    'signature': {
      'ar': 'التوقيع',
      'ku': 'واژوو',
      'en': 'Signature',
    },
    'notes': {
      'ar': 'ملاحظات',
      'ku': 'تێبینی',
      'en': 'Notes',
    },
    'main_office_europe': {
      'ar': '(المكتب الرئيسي في أوروبا)',
      'ku': '(نووسینگەی سەرەکی لە ئەوروپا)',
      'en': '(Main Office in Europe)',
    },

    // الشروط والأحكام
    'term_1': {
      'ar':
          'شركة ستيرس (EUKnet) تقوم فقط بشحن ونقل البضائع والمواد المسموح بها قانوناً، وفي حال وجود أي مادة مخالفة أو غير مسموحة من الناحية القانونية داخل بضاعة المرسل، فإن المرسل يتحمل كافة الغرامات المالية والقانونية من الجهات المختصة، ولا تعيد للمرسل مبلغ كلفة النقل المدفوعة لـ شركة ستيرس، المرسل يتحمل كامل المسؤولية القانونية وليس شركة ستيرس (EUKnet).',
      'ku':
          'کۆمپانیای ستێرس (EUKnet) تەنها بارگواستنەوەی کاڵا و مادەکانی یاسایی دەکات، لە حاڵەتی بوونی هەر مادەیەکی دژە یاسا لە کاڵاکەی ناردەر، ناردەر بەرپرسیاری هەموو سزا مالی و یاساییەکان دەگرێتەوە، کۆمپانیا پارەی گواستنەوە ناگەڕێنێتەوە، ناردەر تەواوی بەرپرسیاری یاساییەکان دەگرێتەوە نەک کۆمپانیای ستێرس (EUKnet).',
      'en':
          'EUKnet Transport Company only ships and transports legally permitted goods and materials. In case of any illegal or prohibited material in the sender\'s goods, the sender bears all financial and legal penalties from the relevant authorities, and the transport cost paid to EUKnet will not be refunded. The sender bears full legal responsibility, not EUKnet Transport Company.',
    },
    'term_2': {
      'ar':
          'يجب على المرسل إعطاء عنوان ورقم هاتف المستلم بشكل صحيح وكامل، بخلاف ذلك فإن شركة ستيرس (EUKnet) ليست مسؤولة عن أي تأخير أو ضياع للمواد بسبب عنوان أو رقم هاتف خاطئ أو غير كامل',
      'ku':
          'ناردەر دەبێت ناونیشان و ژمارەی تەلەفۆنی وەرگر بە شێوەیەکی دروست و تەواو بدات، بەپێچەوانەوە کۆمپانیای ستێرس (EUKnet) بەرپرس نییە لە هیچ دواکەوتنێک یان ونبوونی کاڵاکان بەهۆی ناونیشان یان ژمارەی تەلەفۆنی هەڵە یان ناتەواوەوە',
      'en':
          'The sender must provide the recipient\'s address and phone number correctly and completely. Otherwise, EUKnet Transport Company is not responsible for any delay or loss of materials due to incorrect or incomplete address or phone number.',
    },
    'term_3': {
      'ar':
          'رقم الكود هو رقم سري (تعطيه الشركة للمرسل فقط) لا يجوز للمرسل اعطاءه لأي شخص عدا الشخص المستلم، في حال إعطاء رقم الكود لشخص غير مخول باستلام البضاعة، فإن الشركة لا تتحمل أي مسؤولية عن تسليم البضاعة لهذا الشخص.',
      'ku':
          'ژمارەی کۆد ژمارەیەکی نهێنییە (کۆمپانیا تەنها بە ناردەر دەیدات) ناردەر نابێت بیدات بە کەسێکی تر جگە لە وەرگر، لە حاڵەتی دانی ژمارەی کۆد بە کەسێکی نامۆڵەت بۆ وەرگرتنی کاڵاکە، کۆمپانیا هیچ بەرپرسیارێتییەک ناگرێتەوە بۆ دانی کاڵاکە بەو کەسە.',
      'en':
          'The code number is a secret number (given by the company to the sender only). The sender must not give it to anyone except the recipient. If the code number is given to an unauthorized person to receive the goods, the company bears no responsibility for delivering the goods to that person.',
    },
    'term_4_intro': {
      'ar':
          'في حال حدوث أي ضرر أو نقص في المواد فإن الشركة تقوم بالتعويض لصاحبها على الشكل التالي:',
      'ku':
          'لە حاڵەتی ڕوودانی هەر زیانێک یان کەمی لە کاڵاکان کۆمپانیا قەرەبووکردنەوە بۆ خاوەنەکەی دەکات بەم شێوەیە:',
      'en':
          'In case of any damage or shortage in the materials, the company compensates the owner as follows:',
    },
    'sender_signature': {
      'ar': 'توقيع المرسل',
      'ku': 'واژووی نێردەر',
      'en': 'Sender Signature',
    },
    'signature_agreement': {
      'ar':
          'توقيع المرسل على هذه الفاتورة يعتبر موافقة صريحة على كل ما ورد ذكره في النقاط أعلاه وينفي صفة الجهالة بها.',
      'ku':
          'واژووی ناردەر لەسەر ئەم پسوڵەیە وەک ڕازیبوونێکی ڕاشکاو لەسەر هەموو ئەوەی لە خاڵەکانی سەرەوە هاتووە دادەنرێت و نەزانی لێی دەسڕێتەوە.',
      'en':
          'The sender\'s signature on this invoice is considered explicit agreement to all the points mentioned above and negates any claim of ignorance.',
    },
    'prohibited_items': {
      'ar': 'المواد الممنوعة',
      'ku': 'کاڵا قەدەغەکان',
      'en': 'Prohibited Items',
    },
    'term_4a': {
      'ar':
          'العملاء الذين قاموا بالتأمين الإضافي على البضاعة المرسلة وذلك بدفع نسبة عن قيمة البضاعة فإن الشركة مسؤولة عن التعويض الكامل لصاحبها',
      'ku':
          'ئەو کڕیارانەی بیمەی زیادەیان لەسەر کاڵا ناردراوەکە کردووە بە پارەدانی ڕێژەیەک لە بەهای کاڵاکە، کۆمپانیا بەرپرسیاری قەرەبووکردنەوەی تەواوی خاوەنەکەی دەگرێتەوە',
      'en':
          'Customers who have purchased additional insurance on the shipped goods by paying a percentage of the goods value, the company is responsible for full compensation to the owner',
    },
    'term_4b_part1': {
      'ar':
          'العملاء الذين لم يدفعوا قيمة التأمين الإضافي على البضاعة المرسلة، ',
      'ku': 'ئەو کڕیارانەی بەهای بیمەی زیادەیان لەسەر کاڵا ناردراوەکە نەداوە، ',
      'en':
          'Customers who have not paid the additional insurance value on the shipped goods, ',
    },
    'term_4b_part2': {
      'ar':
          'فإن الشركة تعوض فقط أجور النقل لصاحبها وإعادة كلفة النقل ان كانت مستلمة الى صاحبها. علماً ان المواد القابلة للكسر مثل المواد الزجاجية وما شابه ذلك لا يتم التأمين عليها مطلقاً، وشركة ستيرس (EUKnet) لا تعوض الزبائن في حالة الكسر او الحاق الضرر بهذه المواد.',
      'ku':
          'کۆمپانیا تەنها کرێی گواستنەوە بۆ خاوەنەکەی قەرەبوو دەکاتەوە و گەڕاندنەوەی تێچووی گواستنەوە ئەگەر وەرگیرابێت بۆ خاوەنەکەی. زانیاری ئەوەی کە مادە شکاوەکان وەک مادە شووشەییەکان و هاوشێوەکانی ئەوان هەرگیز بیمە ناکرێن، کۆمپانیای ستێرس (EUKnet) کڕیاران قەرەبوو ناکاتەوە لە حاڵەتی شکان یان زیانگەیاندن بەم مادانە.',
      'en':
          'the company compensates only the transport fees to the owner and refunds the transport cost if received to the owner. Note that breakable materials such as glass materials and the like are never insured, and EUKnet Transport Company does not compensate customers in case of breakage or damage to these materials.',
    },
    'term_5': {
      'ar': 'يجب على المرسل التقيد بكافة التعليمات الموجهة من قبل موظفينا',
      'ku':
          'ناردەر دەبێت پابەندی هەموو ڕێنماییەکانی ئاراستەکراو لەلایەن کارمەندانمانەوە بێت',
      'en':
          'The sender must comply with all instructions directed by our staff',
    },
    'term_6': {
      'ar':
          'في حال وجود المواد الممنوعة (السجائر، معسل الاركيلة، الادوية، العملات والنقود، المجوهرات مثل الذهب والفضة والالماس...الخ، المشروبات الكحولية، الأسلحة والمواد العسكرية بأنواعها، المواد المخدرة، التحفيات والقطع الاثرية) واي مادة أخرى غير مسموح بها قانوناً، داخل البضاعة فإن الشخص المرسل يتحمل كافة التبعات القانونية وليس شركة ستيرس (EUKnet)',
      'ku':
          'لە حاڵەتی بوونی کاڵا قەدەغەکان (جگەرە، تەنباکووی نارگیلە، دەرمان، دراو و پارە، زێڕینە وەک زێڕ و زیو و ئەڵماس...هتد، خواردنەوە کحولییەکان، چەک و کەرەستەی سەربازی بە جۆرەکانی، مادە هۆشبەرەکان، یادگارییەکان و پارچە شوێنەوارییەکان) و هەر مادەیەکی تری قەدەغەکراوی یاسایی، لەناو کاڵاکەدا کەسی ناردەر هەموو دەرئەنجامە یاساییەکان دەگرێتەوە نەک کۆمپانیای ستێرس (EUKnet)',
      'en':
          'In case of prohibited materials (cigarettes, hookah tobacco, medicines, currencies and money, jewelry such as gold, silver and diamonds...etc, alcoholic beverages, weapons and military materials of all kinds, narcotics, antiques and archaeological pieces) and any other legally prohibited material inside the goods, the sender bears all legal consequences, not EUKnet Transport Company',
    },
    'term_7': {
      'ar':
          'شركة ستيرس (EUKnet) غير مسؤولة في حال تم فرض رسومات كمركية في أي نقطة حدودية للدول، بل صاحب البضاعة ملزم بدفع هذه الرسوم.',
      'ku':
          'کۆمپانیای ستێرس (EUKnet) بەرپرس نییە لە حاڵەتی سەپاندنی باجی گومرکی لە هەر خاڵێکی سنووری وڵاتان، بەڵکو خاوەنی کاڵاکە پێویستە ئەم باجانە بدات.',
      'en':
          'EUKnet Transport Company is not responsible in case customs fees are imposed at any border point of countries, but the goods owner is obligated to pay these fees.',
    },
  };

  /// الحصول على الترجمة
  static String translate(String key, String language) {
    return _translations[key]?[language] ?? _translations[key]?['ar'] ?? key;
  }

  /// الحصول على جميع الترجمات للغة معينة
  static Map<String, String> getAllTranslations(String language) {
    Map<String, String> result = {};
    _translations.forEach((key, translations) {
      result[key] = translations[language] ?? translations['ar'] ?? key;
    });
    return result;
  }
}
