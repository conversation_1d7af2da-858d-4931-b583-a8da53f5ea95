import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/code_data_service.dart';
import '../../utils/ui_helper.dart';
import 'settings_variables_mixin.dart';

/// مزيج خاص بمعلومات الوكيل
mixin AgentInfoMixin<T extends ConsumerStatefulWidget>
    on ConsumerState<T>, SettingsVariablesMixin<T> {
  // متغيرات لتخزين المدن
  List<Map<String, dynamic>> _allCities = [];
  List<Map<String, dynamic>> _filteredCities = [];

  // متغير لتخزين المدينة المحددة
  String? _selectedCityCode;

  // متغير لتخزين معلومات الوكيل
  Map<String, dynamic>? _currentAgentInfo;

  // متغير لتنشيط عملية تحميل البيانات
  bool _isLoadingAgentInfo = false;

  // متحكمات النصوص للبحث
  final TextEditingController _citySearchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _citySearchController.addListener(_filterCities);
  }

  @override
  void dispose() {
    _citySearchController.removeListener(_filterCities);
    _citySearchController.dispose();
    super.dispose();
  }

  // تصفية المدن بناءً على نص البحث
  void _filterCities() {
    final query = _citySearchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredCities = List.from(_allCities);
      } else {
        _filteredCities = _allCities.where((city) {
          final cityName = city['city']?.toString().toLowerCase() ?? '';
          final countryName = city['country']?.toString().toLowerCase() ?? '';
          return cityName.contains(query) || countryName.contains(query);
        }).toList();
      }
    });
  }

  // تحميل المدن من قاعدة البيانات
  Future<void> loadAgentCities() async {
    setState(() {
      _isLoadingAgentInfo = true;
    });

    try {
      final codeDataService = CodeDataService();
      final cities = await codeDataService.getAllUniqueCities();

      if (mounted) {
        setState(() {
          _allCities = cities;
          _filteredCities = List.from(cities);
          _isLoadingAgentInfo = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المدن: $e');
      if (mounted) {
        setState(() {
          _isLoadingAgentInfo = false;
        });
      }
    }
  }

  // تحميل معلومات الوكيل للمدينة المحددة
  Future<void> loadAgentInfo(String codeNo) async {
    setState(() {
      _isLoadingAgentInfo = true;
      _currentAgentInfo = null;
    });

    try {
      final codeDataService = CodeDataService();

      // الحصول على السجل الكامل في قاعدة البيانات
      final fullRecord = await codeDataService.getCodeDataByCode(codeNo);

      if (mounted) {
        setState(() {
          _currentAgentInfo = fullRecord;
          _isLoadingAgentInfo = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل معلومات الوكيل: $e');
      if (mounted) {
        setState(() {
          _isLoadingAgentInfo = false;
        });
      }
    }
  }

  // نسخ معلومات الوكيل إلى الحافظة
  Future<void> copyAgentInfoToClipboard(String info) async {
    await Clipboard.setData(ClipboardData(text: info));

    if (mounted) {
      UiHelper.safelyShowSnackBar(
        context,
        'تم نسخ معلومات الوكيل',
        isError: false,
      );
    }
  }

  // توليد نص معلومات الوكيل باللغة الإنجليزية
  String generateEnglishAgentInfo() {
    if (_currentAgentInfo == null) return '';

    final agentName = _currentAgentInfo?['agent_name'] ?? '';
    final agentPhone1 = _currentAgentInfo?['agent_phone1'] ?? '';
    final agentPhone2 = _currentAgentInfo?['agent_phone2'] ?? '';
    final agentAddress = _currentAgentInfo?['agent_address'] ?? '';
    final city = _currentAgentInfo?['city'] ?? '';
    final country = _currentAgentInfo?['country'] ?? '';

    return '''
Agent Name: $agentName
City: $city
Country: $country
Phone 1: $agentPhone1
Phone 2: $agentPhone2
Address: $agentAddress
''';
  }

  // توليد نص معلومات الوكيل باللغة الكردية
  String generateKurdishAgentInfo() {
    if (_currentAgentInfo == null) return '';

    final agentName = _currentAgentInfo?['agent_name'] ?? '';
    final agentPhone1 = _currentAgentInfo?['agent_phone1'] ?? '';
    final agentPhone2 = _currentAgentInfo?['agent_phone2'] ?? '';
    final agentAddress = _currentAgentInfo?['agent_address'] ?? '';
    final city = _currentAgentInfo?['city'] ?? '';
    final country = _currentAgentInfo?['country'] ?? '';

    return '''
ناوی بریکار: $agentName
شار: $city
وڵات: $country
ژمارەی تەلەفۆن ١: $agentPhone1
ژمارەی تەلەفۆن ٢: $agentPhone2
ناونیشان: $agentAddress
''';
  }

  // توليد نص معلومات الوكيل باللغة العربية
  String generateArabicAgentInfo() {
    if (_currentAgentInfo == null) return '';

    final agentName = _currentAgentInfo?['agent_name'] ?? '';
    final agentPhone1 = _currentAgentInfo?['agent_phone1'] ?? '';
    final agentPhone2 = _currentAgentInfo?['agent_phone2'] ?? '';
    final agentAddress = _currentAgentInfo?['agent_address'] ?? '';
    final city = _currentAgentInfo?['city'] ?? '';
    final country = _currentAgentInfo?['country'] ?? '';

    return '''
اسم الوكيل: $agentName
المدينة: $city
الدولة: $country
رقم الهاتف ١: $agentPhone1
رقم الهاتف ٢: $agentPhone2
العنوان: $agentAddress
''';
  }

  // بناء قسم معلومات الوكيل
  Widget buildAgentInfoTab() {
    return Padding(
      padding: const EdgeInsets.all(14.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حقل قائمة قابل للبحث للمدن
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(14.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان العنصر
                  Row(
                    children: [
                      if (_isLoadingAgentInfo)
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                    ],
                  ),
                  const SizedBox(height: 14),

                  // حقل اختيار المدينة
                  _isLoadingAgentInfo
                      ? const Center(child: CircularProgressIndicator())
                      : Column(
                          children: [
                            // حقل البحث
                            TextField(
                              controller: _citySearchController,
                              decoration: InputDecoration(
                                hintText: 'Type to search for city or country',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 16,
                                ),
                              ),
                              onChanged: (value) {
                                _filterCities();
                              },
                            ),
                            const SizedBox(height: 12),
                            // قائمة المدن
                            Container(
                              height: 200,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade200),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: _filteredCities.isEmpty
                                  ? const Center(
                                      child:
                                          Text('No cities match your search'))
                                  : ListView.separated(
                                      itemCount: _filteredCities.length,
                                      separatorBuilder: (context, index) =>
                                          const Divider(height: 1),
                                      itemBuilder: (context, index) {
                                        final city = _filteredCities[index];
                                        final cityName =
                                            city['city']?.toString() ?? '';
                                        final countryName =
                                            city['country']?.toString() ?? '';
                                        final codeNo =
                                            city['code_no']?.toString() ?? '';
                                        final isSelected =
                                            _selectedCityCode == codeNo;

                                        return ListTile(
                                          dense: true,
                                          title: Text(
                                            cityName,
                                            style: TextStyle(
                                              fontWeight: isSelected
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                              fontSize: 14,
                                            ),
                                          ),
                                          subtitle: Text(
                                            countryName,
                                            style:
                                                const TextStyle(fontSize: 12),
                                          ),
                                          leading: const Icon(Icons.location_on,
                                              size: 16, color: Colors.grey),
                                          selected: isSelected,
                                          selectedTileColor:
                                              Colors.blue.shade50,
                                          onTap: () {
                                            setState(() {
                                              _selectedCityCode = codeNo;
                                            });
                                            loadAgentInfo(codeNo);
                                          },
                                        );
                                      },
                                    ),
                            ),
                          ],
                        ),

                  // عرض المدينة المحددة
                  if (_selectedCityCode != null && _currentAgentInfo != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Container(
                        padding: const EdgeInsets.all(12.0),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle,
                                color: Colors.green, size: 16),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Selected: ${_currentAgentInfo?['city']} (${_currentAgentInfo?['country']})',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.clear, size: 16),
                              onPressed: () {
                                setState(() {
                                  _selectedCityCode = null;
                                  _currentAgentInfo = null;
                                });
                              },
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // معلومات الوكيل بمختلف اللغات
          Expanded(
            child: _isLoadingAgentInfo
                ? const Center(child: CircularProgressIndicator())
                : _currentAgentInfo == null
                    ? const Center(
                        child: Text(
                          'Please select a city to view agent information',
                          style: TextStyle(
                            fontSize: 16,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      )
                    : _buildLanguageInfoCard(
                        title: 'Agent Information',
                        info: generateEnglishAgentInfo(),
                        textDirection: TextDirection.ltr,
                        onCopy: () => copyAgentInfoToClipboard(
                            generateEnglishAgentInfo()),
                      ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة معلومات الوكيل بلغة محددة
  Widget _buildLanguageInfoCard({
    required String title,
    required String info,
    required TextDirection textDirection,
    required VoidCallback onCopy,
  }) {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // العنوان وزر النسخ
          Container(
            color: Theme.of(context).colorScheme.primary,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy, color: Colors.white),
                  tooltip: 'Copy Information',
                  onPressed: onCopy,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

          // المحتوى
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Directionality(
                textDirection: textDirection,
                child: SingleChildScrollView(
                  child: SelectableText(
                    info,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.5,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
