import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:logging/logging.dart';

/// خدمة معالجة دورة حياة التطبيق لضمان الإغلاق الآمن
class AppLifecycleService extends WidgetsBindingObserver {
  static final Logger _logger = Logger('AppLifecycleService');
  static AppLifecycleService? _instance;

  AppLifecycleService._();

  static AppLifecycleService get instance {
    _instance ??= AppLifecycleService._();
    return _instance!;
  }

  /// تهيئة خدمة دورة حياة التطبيق
  void initialize() {
    WidgetsBinding.instance.addObserver(this);
    _logger.info('تم تهيئة خدمة دورة حياة التطبيق');
  }

  /// تنظيف خدمة دورة حياة التطبيق
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _logger.info('تم تنظيف خدمة دورة حياة التطبيق');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    _logger.info('تغيير حالة التطبيق: ${state.name}');

    switch (state) {
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// معالجة حالة فصل التطبيق (الإغلاق النهائي)
  void _handleAppDetached() {
    _logger.info('التطبيق في حالة فصل - بدء التنظيف النهائي');

    try {
      // تنظيف الموارد هنا
      _cleanupResources();

      // إشارة للنظام أن التنظيف قد انتهى
      if (Platform.isWindows) {
        // لا حاجة لعمل شيء إضافي في Windows
        // النظام سيتولى باقي العملية
      }
    } catch (e) {
      _logger.severe('خطأ أثناء معالجة فصل التطبيق: $e');
    }
  }

  /// معالجة حالة إيقاف التطبيق مؤقتاً
  void _handleAppPaused() {
    _logger.info('التطبيق في حالة إيقاف مؤقت');

    try {
      // حفظ الحالة الحالية
      _saveCurrentState();
    } catch (e) {
      _logger.warning('خطأ أثناء حفظ الحالة: $e');
    }
  }

  /// معالجة حالة استئناف التطبيق
  void _handleAppResumed() {
    _logger.info('التطبيق في حالة استئناف');

    try {
      // استعادة الحالة إذا لزم الأمر
      _restoreState();
    } catch (e) {
      _logger.warning('خطأ أثناء استعادة الحالة: $e');
    }
  }

  /// معالجة حالة عدم نشاط التطبيق
  void _handleAppInactive() {
    _logger.info('التطبيق في حالة عدم نشاط');
  }

  /// معالجة حالة إخفاء التطبيق
  void _handleAppHidden() {
    _logger.info('التطبيق مخفي');
  }

  /// تنظيف الموارد
  void _cleanupResources() {
    _logger.info('بدء تنظيف الموارد...');

    try {
      // إغلاق قواعد البيانات
      // DatabaseHelper().close(); // إذا كان لديك دالة إغلاق

      // تنظيف الـ controllers والـ streams
      // يمكنك إضافة المزيد من عمليات التنظيف هنا

      _logger.info('تم تنظيف الموارد بنجاح');
    } catch (e) {
      _logger.severe('خطأ أثناء تنظيف الموارد: $e');
    }
  }

  /// حفظ الحالة الحالية
  void _saveCurrentState() {
    _logger.info('حفظ الحالة الحالية...');
    // إضافة منطق حفظ الحالة هنا
  }

  /// استعادة الحالة
  void _restoreState() {
    _logger.info('استعادة الحالة...');
    // إضافة منطق استعادة الحالة هنا
  }

  /// إجبار إغلاق التطبيق بشكل آمن
  static void forceQuitSafely() {
    _logger.info('طلب إغلاق آمن للتطبيق');

    try {
      // تنظيف الموارد أولاً
      instance._cleanupResources();

      // ثم الخروج
      if (Platform.isWindows) {
        // في Windows، نستخدم SystemNavigator.pop()
        SystemNavigator.pop();
      } else {
        exit(0);
      }
    } catch (e) {
      _logger.severe('خطأ أثناء الإغلاق الآمن: $e');
      // في حالة فشل الإغلاق الآمن، استخدم الإغلاق العادي
      exit(1);
    }
  }
}
