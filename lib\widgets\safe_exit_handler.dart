import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/app_lifecycle_service.dart';

/// Widget لمعالجة الخروج الآمن من التطبيق
class SafeExitHandler extends StatelessWidget {
  final Widget child;

  const SafeExitHandler({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldExit = await _showExitConfirmation(context);
          if (shouldExit) {
            _performSafeExit();
          }
        }
      },
      child: KeyboardListener(
        focusNode: FocusNode(),
        autofocus: true,
        onKeyEvent: (KeyEvent event) {
          // معالجة اختصارات لوحة المفاتيح للخروج الآمن
          if (event is KeyDownEvent) {
            // Alt + F4 أو Ctrl + Q للخروج
            if ((HardwareKeyboard.instance.isAltPressed &&
                    event.logicalKey == LogicalKeyboardKey.f4) ||
                (HardwareKeyboard.instance.isControlPressed &&
                    event.logicalKey == LogicalKeyboardKey.keyQ)) {
              _handleSafeExit(context);
            }
          }
        },
        child: child,
      ),
    );
  }

  /// عرض تأكيد الخروج
  Future<bool> _showExitConfirmation(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Exit'),
          content:
              const Text('Are you sure you want to close the application?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Exit'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// معالجة الخروج الآمن
  void _handleSafeExit(BuildContext context) async {
    final shouldExit = await _showExitConfirmation(context);
    if (shouldExit) {
      _performSafeExit();
    }
  }

  /// تنفيذ الخروج الآمن
  void _performSafeExit() {
    AppLifecycleService.forceQuitSafely();
  }
}
