class GermanyAddress {
  final String postalCode;
  final String cityName;

  GermanyAddress({
    required this.postalCode,
    required this.cityName,
  });

  factory GermanyAddress.fromMap(Map<String, dynamic> map) {
    return GermanyAddress(
      postalCode: map['postal_code'] ?? '',
      cityName: map['city_name'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'postal_code': postalCode,
      'city_name': cityName,
    };
  }

  @override
  String toString() {
    return '$postalCode, $cityName';
  }
}
