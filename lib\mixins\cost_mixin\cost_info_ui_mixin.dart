import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// مزيج يحتوي على دوال بناء واجهة المستخدم الخاصة بمعلومات التكلفة
mixin CostInfoUIMixin<T extends StatefulWidget> on State<T> {
  // دالة مساعدة لإنشاء حقول النص
  Widget buildTextField({
    required TextEditingController controller,
    required String label,
    bool isNumeric = false,
    bool readOnly = false,
    bool isBold = true,
    Color? textColor,
    Color? backgroundColor,
    Function(String)? onChanged,
    bool formatWithCommas = false,
    bool isError = false,
  }) {
    return SizedBox(
      height: 45, // تقليل ارتفاع الحقل قليلاً لتناسب جميع الحقول
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 13,
            color: isError ? Colors.red : null,
          ),
          border: const OutlineInputBorder(),
          enabledBorder: isError
              ? const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.red, width: 2.0),
                )
              : null,
          focusedBorder: isError
              ? const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.red, width: 2.0),
                )
              : null,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
          filled: readOnly || backgroundColor != null,
          fillColor:
              backgroundColor ?? (readOnly ? const Color(0xFFF5F5F5) : null),
          isDense: false, // تغيير إلى false لزيادة الارتفاع
        ),
        keyboardType: isNumeric
            ? const TextInputType.numberWithOptions(decimal: true)
            : TextInputType.text,
        inputFormatters: isNumeric
            ? [
                // استخدام تعبير منتظم أكثر دقة للسماح بالأرقام والنقطة العشرية فقط
                FilteringTextInputFormatter.allow(RegExp(r'[0-9\.]')),
                if (formatWithCommas) ThousandsSeparatorInputFormatter(),
              ]
            : null,
        readOnly: readOnly,
        style: TextStyle(
          fontSize: 14, // زيادة حجم الخط
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          color: isError ? Colors.red : textColor,
        ),
        onChanged: onChanged,
        // إضافة خاصية تحديد النص تلقائياً عند الضغط على الحقل
        onTap: () {
          // تحديد كل النص في الحقل
          controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: controller.text.length,
          );
        },
      ),
    );
  }
}

// منسق إدخال لإضافة فواصل الآلاف
class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // إذا كان النص فارغًا، لا تقم بأي تنسيق
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // إزالة الفواصل من النص الجديد
    String newText = newValue.text.replaceAll(',', '');

    // إذا كان النص بعد إزالة الفواصل فارغًا، أعد النص الفارغ
    if (newText.isEmpty) {
      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    // التحقق من صحة النص كرقم باستخدام تعبير منتظم
    RegExp regExp = RegExp(r'^\d*\.?\d*$');
    if (!regExp.hasMatch(newText)) {
      return oldValue;
    }

    // تنسيق النص بفواصل الآلاف
    String formattedText;
    if (newText.contains('.')) {
      // إذا كان الرقم به كسور
      List<String> parts = newText.split('.');
      String intPart = parts[0];
      String fracPart = parts.length > 1 ? parts[1] : '';

      // تنسيق الجزء الصحيح فقط
      formattedText = _addCommasToNumericString(intPart);

      // إعادة تجميع الرقم
      if (fracPart.isNotEmpty) {
        formattedText += '.$fracPart';
      } else if (newText.endsWith('.')) {
        formattedText += '.';
      }
    } else {
      // إذا كان الرقم صحيحًا (بدون كسور)
      formattedText = _addCommasToNumericString(newText);
    }

    // تحديد موضع المؤشر بعد التنسيق
    int cursorPosition = formattedText.length;
    if (newValue.selection.baseOffset < newValue.text.length) {
      // حساب عدد الفواصل قبل موضع المؤشر في النص القديم
      int oldCommasBeforeCursor = _countCommasBeforeCursor(
          oldValue.text, newValue.selection.baseOffset);

      // حساب عدد الفواصل قبل موضع المؤشر في النص الجديد
      int newCommasBeforeCursor = _countCommasBeforeCursor(
          formattedText, newValue.selection.baseOffset);

      // تعديل موضع المؤشر بناءً على الفرق في عدد الفواصل
      cursorPosition = newValue.selection.baseOffset +
          (newCommasBeforeCursor - oldCommasBeforeCursor);

      // التأكد من أن موضع المؤشر ضمن حدود النص
      cursorPosition = cursorPosition.clamp(0, formattedText.length);
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }

  // دالة مساعدة لإضافة فواصل إلى نص رقمي
  static String _addCommasToNumericString(String numericString) {
    if (numericString.isEmpty) return '';

    // تنسيق الرقم بالفواصل
    String result = '';
    int length = numericString.length;

    for (int i = 0; i < length; i++) {
      if (i > 0 && (length - i) % 3 == 0) {
        result += ',';
      }
      result += numericString[i];
    }

    return result;
  }

  // دالة لحساب عدد الفواصل قبل موضع المؤشر
  static int _countCommasBeforeCursor(String text, int cursorPosition) {
    int count = 0;
    for (int i = 0; i < cursorPosition && i < text.length; i++) {
      if (text[i] == ',') count++;
    }
    return count;
  }
}
