import 'package:flutter/material.dart';
import '../services/code_data_service.dart';
import '../services/database_helper.dart';
import '../widgets/admin_tabs/countries_tab.dart';
import '../widgets/admin_tabs/cities_tab.dart';
import '../widgets/admin_tabs/branches_tab.dart';

class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final CodeDataService _codeDataService = CodeDataService();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey.shade300,
          indicatorWeight: 3,
          tabs: const [
            Tab(
              icon: Icon(Icons.flag_outlined),
              text: 'Countries',
            ),
            Tab(
              icon: Icon(Icons.location_city_outlined),
              text: 'Cities',
            ),
            Tab(
              icon: Icon(Icons.people_outline),
              text: 'Branches & Users',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CountriesTab(codeDataService: _codeDataService),
                CitiesTab(codeDataService: _codeDataService),
                BranchesTab(databaseHelper: _databaseHelper),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
