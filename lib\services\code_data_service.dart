import 'package:flutter/foundation.dart';
import 'package:euknet_company_app/services/database_helper.dart';

class CodeDataService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على جميع بيانات الكود
  Future<List<Map<String, dynamic>>> getAllCodeData() async {
    return await _databaseHelper.getAllCodeData();
  }

  // الحصول على بيانات كود محدد
  Future<Map<String, dynamic>?> getCodeDataByCode(String codeNo) async {
    return await _databaseHelper.getCodeDataByCode(codeNo);
  }

  // تحويل القيم المنطقية وتصحيح أسماء الحقول
  Map<String, dynamic> _sanitizeData(Map<String, dynamic> data) {
    Map<String, dynamic> sanitizedData = {};

    // تحويل القيم المنطقية (bool) إلى أرقام (0 أو 1)
    data.forEach((key, value) {
      if (value is bool) {
        sanitizedData[key] = value ? 1 : 0;
      } else if (key == 'exchange_rate') {
        // معالجة خاصة لسعر الصرف
        debugPrint('معالجة سعر الصرف: $value (${value?.runtimeType})');

        try {
          double finalRate;

          if (value == null) {
            finalRate = 1309.0;
          } else if (value is double) {
            finalRate = value;
          } else if (value is int) {
            finalRate = value.toDouble();
          } else if (value is String) {
            // تنظيف النص من أي أحرف غير رقمية
            String cleanText = value.trim().replaceAll(',', '.');
            cleanText = cleanText.replaceAll(' ', '');
            debugPrint('النص المنظف لسعر الصرف: $cleanText');
            finalRate = double.parse(cleanText);
          } else {
            // محاولة التحويل من أي نوع آخر
            finalRate = double.parse(value.toString());
          }

          // التأكد من أن القيمة موجبة
          if (finalRate <= 0) {
            finalRate = 1309.0;
          }

          sanitizedData[key] = finalRate;
          debugPrint(
              'سعر الصرف بعد المعالجة: ${sanitizedData[key]} (${sanitizedData[key].runtimeType})');
        } catch (e) {
          debugPrint('خطأ في معالجة سعر الصرف: $e');
          sanitizedData[key] = 1309.0;
        }
      } else {
        sanitizedData[key] = value;
      }
    });

    // تصحيح أسماء حقول العنوان لتتوافق مع أسماء الأعمدة في قاعدة البيانات
    _getFieldMappings().forEach((uiField, dbField) {
      if (sanitizedData.containsKey(uiField)) {
        sanitizedData[dbField] = sanitizedData[uiField];
        sanitizedData.remove(uiField);
      }
    });

    return sanitizedData;
  }

  // الحصول على تعيينات الحقول بين واجهة المستخدم وقاعدة البيانات
  Map<String, String> _getFieldMappings() {
    return {
      'street': 'street_name_no',
      'email': 'receiver_email',
      'real_weight': 'real_weight_kg',
      'total_weight': 'total_weight_kg',
    };
  }

  // إضافة بيانات كود جديدة
  Future<int> insertCodeData(Map<String, dynamic> codeData) async {
    final sanitizedData = _sanitizeData(codeData);
    return await _databaseHelper.insertCodeData(sanitizedData);
  }

  // تحديث بيانات كود موجودة
  Future<int> updateCodeData(Map<String, dynamic> codeData) async {
    try {
      // طباعة البيانات الأصلية للتشخيص
      debugPrint('بدء تحديث بيانات الكود باستخدام CodeDataService');
      debugPrint('البيانات الأصلية المستلمة: $codeData');

      // التحقق من وجود مفتاح code_no
      if (!codeData.containsKey('code_no') ||
          codeData['code_no'] == null ||
          codeData['code_no'].toString().isEmpty) {
        debugPrint('خطأ: مفتاح code_no غير موجود أو فارغ');
        return 0;
      }

      debugPrint('code_no الموجود في البيانات: ${codeData['code_no']}');

      // معالجة لتحديثات محددة مثل معلومات الوكيل
      if (codeData.containsKey('agent_name') ||
          codeData.containsKey('agent_phone1') ||
          codeData.containsKey('agent_phone2') ||
          codeData.containsKey('agent_address')) {
        debugPrint('تحديث معلومات الوكيل للمدينة: ${codeData['code_no']}');

        // إذا كان التحديث يخص معلومات الوكيل فقط، نقوم بإعداد تحديث مبسط
        Map<String, dynamic> agentUpdateData = {
          'agent_name': codeData['agent_name'] ?? '',
          'agent_phone1': codeData['agent_phone1'] ?? '',
          'agent_phone2': codeData['agent_phone2'] ?? '',
          'agent_address': codeData['agent_address'] ?? '',
        };

        // استخدام الدالة المحسنة للتحديث
        Map<String, dynamic> result = await _databaseHelper
            .updateAgentInfoDirect(codeData['code_no'], agentUpdateData);

        debugPrint(
            'نتيجة تحديث معلومات الوكيل من خلال CodeDataService: $result');

        if (result['success'] == true) {
          return result['updates'];
        } else {
          // إذا فشلت الطريقة المحسنة، نعود للطريقة القديمة
          debugPrint('فشل التحديث المباشر، محاولة استخدام الطريقة القديمة');
          final sanitizedData = _sanitizeData(codeData);
          debugPrint('البيانات بعد التنظيف: $sanitizedData');
          return await _databaseHelper.updateCodeData(sanitizedData);
        }
      }

      // في حالة التحديث العام، نقوم بتطبيق التنظيف المعتاد
      final sanitizedData = _sanitizeData(codeData);
      debugPrint('البيانات بعد التنظيف: $sanitizedData');

      // استدعاء دالة التحديث في DatabaseHelper
      int result = await _databaseHelper.updateCodeData(sanitizedData);
      debugPrint('نتيجة التحديث من DatabaseHelper: $result');

      return result;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات الكود: $e');
      // عرض رسالة خطأ أكثر فائدة للمستخدم
      throw Exception('فشل في تحديث البيانات: $e');
    }
  }

  // حذف بيانات كود
  Future<int> deleteCodeData(String codeNo) async {
    return await _databaseHelper.deleteCodeData(codeNo);
  }

  // البحث في بيانات الكود
  Future<List<Map<String, dynamic>>> searchCodeData(String query) async {
    return await _databaseHelper.searchCodeData(query);
  }

  // البحث في بيانات الكود مع تحديد الحقول المطلوبة فقط
  Future<List<Map<String, dynamic>>> searchCodeDataWithFields(
      String query) async {
    return await _databaseHelper.searchCodeDataWithFields(query);
  }

  // تحويل اسم العمود من واجهة المستخدم إلى اسم العمود في قاعدة البيانات
  String _mapColumnName(String uiColumnName) {
    return _getFieldMappings()[uiColumnName] ?? uiColumnName;
  }

  // البحث في بيانات الكود باستخدام عمود محدد
  Future<List<Map<String, dynamic>>> searchCodeDataByColumn(
      String columnKey, String searchValue) async {
    try {
      // تحويل اسم العمود إذا كان مختلفاً بين واجهة المستخدم وقاعدة البيانات
      String dbColumnKey = _mapColumnName(columnKey);

      // استدعاء دالة البحث في قاعدة البيانات
      return await _databaseHelper.searchCodeDataByColumn(
          dbColumnKey, searchValue);
    } catch (e) {
      debugPrint('خطأ في البحث بواسطة العمود: $e');
      // إرجاع قائمة فارغة في حالة حدوث خطأ
      return [];
    }
  }

  // الحصول على جميع الدول الفريدة من قاعدة البيانات
  Future<List<Map<String, dynamic>>> getAllUniqueCountries() async {
    try {
      debugPrint('بدء استرجاع الدول الفريدة من قاعدة البيانات...');

      // استخدام استعلام SQL مباشر للحصول على الدول الفريدة مع سعر الصرف
      final db = await _databaseHelper.database;

      // استعلام مبسط للحصول على جميع الدول الفريدة
      var result = await db.rawQuery('''
        SELECT DISTINCT
          code_no,
          country,
          CAST(COALESCE(exchange_rate, 1309.0) AS REAL) as exchange_rate,
          updated_at
        FROM ${DatabaseHelper.tableCodeData}
        WHERE
          country IS NOT NULL AND
          country != ''
        ORDER BY country
      ''');

      debugPrint('تم استرجاع ${result.length} دولة من قاعدة البيانات');

      // إزالة التكرار يدوياً والاحتفاظ بأحدث سجل لكل دولة
      final Map<String, Map<String, dynamic>> uniqueCountries = {};

      for (var country in result) {
        final countryName = country['country']?.toString() ?? '';
        if (countryName.isNotEmpty) {
          // إذا لم تكن الدولة موجودة، أو إذا كان السجل الحالي أحدث
          if (!uniqueCountries.containsKey(countryName) ||
              (country['updated_at'] != null &&
                  uniqueCountries[countryName]!['updated_at'] != null &&
                  country['updated_at'].toString().compareTo(
                          uniqueCountries[countryName]!['updated_at']
                              .toString()) >
                      0)) {
            uniqueCountries[countryName] = country;
          }
        }
      }

      result = uniqueCountries.values.toList();
      result.sort((a, b) => (a['country']?.toString() ?? '')
          .compareTo(b['country']?.toString() ?? ''));

      debugPrint('العدد النهائي للدول بعد إزالة التكرار: ${result.length}');

      // طباعة الدول المسترجعة للتشخيص
      debugPrint('تم استرجاع ${result.length} دولة فريدة من قاعدة البيانات');

      // معالجة النتائج للتأكد من أن سعر الصرف هو double
      final processedResults = result.map((country) {
        // نسخة جديدة من البيانات
        final processedCountry = Map<String, dynamic>.from(country);

        // معالجة سعر الصرف
        var exchangeRate = country['exchange_rate'];
        debugPrint(
            'الدولة: ${country['country']}, سعر الصرف الأصلي: $exchangeRate (${exchangeRate?.runtimeType})');

        try {
          double finalRate;

          if (exchangeRate == null) {
            finalRate = 1309.0;
          } else if (exchangeRate is double) {
            finalRate = exchangeRate;
          } else if (exchangeRate is int) {
            finalRate = exchangeRate.toDouble();
          } else {
            // محاولة التحويل من أي نوع آخر
            finalRate = double.parse(exchangeRate.toString());
          }

          // التأكد من أن القيمة موجبة
          if (finalRate <= 0) {
            finalRate = 1309.0;
          }

          processedCountry['exchange_rate'] = finalRate;
        } catch (e) {
          debugPrint(
              'خطأ في معالجة سعر الصرف للدولة ${country['country']}: $e');
          processedCountry['exchange_rate'] = 1309.0;
        }

        debugPrint(
            'الدولة: ${processedCountry['country']}, سعر الصرف النهائي: ${processedCountry['exchange_rate']} (${processedCountry['exchange_rate'].runtimeType})');

        return processedCountry;
      }).toList();

      return processedResults;
    } catch (e) {
      debugPrint('خطأ في الحصول على الدول الفريدة: $e');
      return [];
    }
  }

  // الحصول على جميع المدن الفريدة من قاعدة البيانات
  Future<List<Map<String, dynamic>>> getAllUniqueCities() async {
    try {
      // استخدام استعلام SQL مباشر للحصول على المدن الفريدة
      final db = await _databaseHelper.database;

      // التأكد من وجود أعمدة معلومات الوكيل
      await _databaseHelper.ensureAgentColumnsExist();

      // استخدام استعلام محسن لاسترجاع أحدث معلومات المدن مع بيانات الوكيل الكاملة
      final result = await db.rawQuery('''
        SELECT 
          c1.code_no, 
          c1.city, 
          c1.country, 
          c1.agent_name, 
          c1.agent_phone1, 
          c1.agent_phone2, 
          c1.agent_address,
          c1.flag_image,
          c1.updated_at
        FROM ${DatabaseHelper.tableCodeData} c1
        INNER JOIN (
          SELECT 
            MAX(updated_at) as max_updated_at, 
            city, 
            country
          FROM ${DatabaseHelper.tableCodeData}
          WHERE city IS NOT NULL AND city != '' AND country IS NOT NULL AND country != ''
          GROUP BY country, city
        ) c2 ON c1.city = c2.city AND c1.country = c2.country AND c1.updated_at = c2.max_updated_at
        GROUP BY c1.country, c1.city
        ORDER BY c1.country, c1.city
      ''');

      // طباعة المدن والمعلومات المسترجعة للتشخيص
      debugPrint('تم استرجاع ${result.length} مدينة فريدة من قاعدة البيانات');
      for (var city in result) {
        debugPrint(
            'المدينة: ${city['city']}, الدولة: ${city['country']}, الكود: ${city['code_no']}');

        // التحقق من وجود صورة المدينة
        bool hasFlagImage = city['flag_image'] != null &&
            city['flag_image'].toString().isNotEmpty;

        if (hasFlagImage) {
          String flagImageStr = city['flag_image'].toString();
          String sample = flagImageStr.length > 30
              ? '${flagImageStr.substring(0, 30)}...'
              : flagImageStr;
          debugPrint('  - يوجد صورة علم للمدينة ($sample)');
        } else {
          debugPrint('  - لا توجد صورة علم للمدينة');
        }

        // التحقق من وجود معلومات الوكيل
        bool hasAgentInfo = (city['agent_name'] != null &&
                city['agent_name'].toString().isNotEmpty) ||
            (city['agent_phone1'] != null &&
                city['agent_phone1'].toString().isNotEmpty) ||
            (city['agent_phone2'] != null &&
                city['agent_phone2'].toString().isNotEmpty) ||
            (city['agent_address'] != null &&
                city['agent_address'].toString().isNotEmpty);

        if (hasAgentInfo) {
          debugPrint('  - معلومات الوكيل متوفرة:');
          debugPrint('    * اسم الوكيل: ${city['agent_name']}');
          debugPrint('    * هاتف 1: ${city['agent_phone1']}');
          debugPrint('    * هاتف 2: ${city['agent_phone2']}');
          debugPrint('    * العنوان: ${city['agent_address']}');
        } else {
          debugPrint('  - لا توجد معلومات وكيل');
        }
      }

      // تحويل القيم الفارغة إلى null للتوحيد
      List<Map<String, dynamic>> normalizedResult = result.map((city) {
        Map<String, dynamic> normalized = {...city};
        for (var field in [
          'agent_name',
          'agent_phone1',
          'agent_phone2',
          'agent_address',
          'flag_image'
        ]) {
          if (normalized[field] == '') {
            normalized[field] = null;
          }
        }
        return normalized;
      }).toList();

      return normalizedResult;
    } catch (e) {
      debugPrint('خطأ في الحصول على المدن: $e');
      return [];
    }
  }

  // الحصول على جميع بيانات الكود مع التفاصيل الكاملة
  Future<List<Map<String, dynamic>>> getAllCodeDataDetails() async {
    try {
      // الحصول على جميع بيانات الكود
      List<Map<String, dynamic>> allCodes = await getAllCodeData();

      // طباعة عدد السجلات المسترجعة للتأكد
      debugPrint('تم استرجاع ${allCodes.length} سجل من قاعدة البيانات');

      return allCodes;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات الكود: $e');
      return [];
    }
  }

  // تحويل بيانات النموذج إلى قاموس لقاعدة البيانات
  Map<String, dynamic> formDataToDbMap(Map<String, dynamic> formData) {
    Map<String, dynamic> dbMap = {};

    formData.forEach((key, value) {
      String dbKey = _mapColumnName(key);
      dbMap[dbKey] = value;
    });

    return dbMap;
  }

  // تحويل بيانات قاعدة البيانات إلى قاموس للنموذج
  Map<String, dynamic> dbMapToFormData(Map<String, dynamic> dbData) {
    Map<String, dynamic> formData = {};

    // عكس تعيينات الحقول
    final Map<String, String> reverseFieldMappings = {};
    _getFieldMappings().forEach((uiField, dbField) {
      reverseFieldMappings[dbField] = uiField;
    });

    dbData.forEach((key, value) {
      String formKey = reverseFieldMappings[key] ?? key;
      formData[formKey] = value;
    });

    return formData;
  }

  // دالة للحصول على قيمة افتراضية لحقل truck_no
  Future<String> getDefaultTruckNo() async {
    return await _databaseHelper.getDefaultTruckNo();
  }

  // دالة للحصول على أكبر رقم شاحنة في قاعدة البيانات
  Future<int> getMaxTruckNo() async {
    return await _databaseHelper.getMaxTruckNo();
  }

  // دالة للتحقق من وجود رقم شاحنة في قاعدة البيانات
  Future<bool> truckNoExists(String truckNo) async {
    return await _databaseHelper.truckNoExists(truckNo);
  }

  // الحصول على بيانات الكود مع تحديد الأعمدة المطلوبة فقط
  Future<List<Map<String, dynamic>>> getCodeDataWithSelectedColumns(
      List<String> columns) async {
    return await _databaseHelper.getCodeDataWithSelectedColumns(columns);
  }

  // حفظ العناصر المحددة لسجل معين
  Future<bool> saveSelectedItems(
      String codeNo, List<Map<String, dynamic>> items) async {
    return await _databaseHelper.saveSelectedItems(codeNo, items);
  }

  // الحصول على العناصر المحددة لسجل معين
  Future<List<Map<String, dynamic>>> getSelectedItems(String codeNo) async {
    return await _databaseHelper.getSelectedItems(codeNo);
  }

  // التحقق من بيانات الدول والمدن في قاعدة البيانات
  Future<void> verifyCountriesAndCitiesData() async {
    try {
      // الحصول على الدول الحالية من قاعدة البيانات
      final existingCountries = await getAllUniqueCountries();
      debugPrint(
          'عدد الدول الموجودة في قاعدة البيانات: ${existingCountries.length}');

      // الحصول على المدن الحالية من قاعدة البيانات
      final existingCities = await getAllUniqueCities();
      debugPrint(
          'عدد المدن الموجودة في قاعدة البيانات: ${existingCities.length}');

      // التحقق من وجود أسعار في قاعدة البيانات
      final existingPrices = await getAllUniquePrices();
      if (existingPrices.isEmpty) {
        debugPrint('لا توجد أسعار في قاعدة البيانات.');
      } else {
        debugPrint(
            'عدد الأسعار الموجودة في قاعدة البيانات: ${existingPrices.length}');
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من بيانات الدول والمدن: $e');
    }
  }

  // الحصول على الأسعار حسب الدولة والمدينة
  Future<Map<String, dynamic>?> getPricesByCountryAndCity(
      String country, String city) async {
    try {
      final allData = await getAllCodeData();

      // البحث عن سجل يحتوي على الدولة والمدينة المحددة وله بيانات أسعار
      for (var data in allData) {
        if (data['country'] == country &&
            data['city'] == city &&
            (data['price_door_to_door'] != null ||
                data['for_each_1_kg'] != null ||
                data['minimum_price'] != null)) {
          return {
            'price_door_to_door': data['price_door_to_door'] ?? 0.0,
            'for_each_1_kg': data['for_each_1_kg'] ?? 0.0,
            'minimum_price': data['minimum_price'] ?? 0.0,
          };
        }
      }

      // إذا كانت المدينة فارغة، نبحث عن سجل للدولة فقط
      if (city.isEmpty) {
        // البحث عن سجل يحتوي على الدولة المحددة فقط وله بيانات أسعار
        for (var data in allData) {
          if (data['country'] == country &&
              (data['city'] == null ||
                  data['city'] == '' ||
                  data['city'].toString().isEmpty) &&
              (data['price_door_to_door'] != null ||
                  data['for_each_1_kg'] != null ||
                  data['minimum_price'] != null)) {
            return {
              'price_door_to_door': data['price_door_to_door'] ?? 0.0,
              'for_each_1_kg': data['for_each_1_kg'] ?? 0.0,
              'minimum_price': data['minimum_price'] ?? 0.0,
            };
          }
        }
      }

      // إذا لم يتم العثور على أي سجل، نرجع null
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الأسعار حسب الدولة والمدينة: $e');
      return null;
    }
  }

  // الحصول على جميع الأسعار الفريدة من قاعدة البيانات
  Future<List<Map<String, dynamic>>> getAllUniquePrices() async {
    try {
      final allData = await getAllCodeData();
      final Map<String, Map<String, dynamic>> uniquePricesMap = {};

      debugPrint('عدد السجلات الكلي: ${allData.length}');

      for (var data in allData) {
        final country = data['country'] as String?;
        final city = data['city'] as String?;
        final codeNo = data['code_no'] as String?;

        // التحقق من وجود code_no صالح وأنه يبدأ بـ PRICE_
        if (country != null &&
            country.isNotEmpty &&
            codeNo != null &&
            codeNo.isNotEmpty &&
            codeNo.startsWith('PRICE_') &&
            (data['price_door_to_door'] != null ||
                data['for_each_1_kg'] != null ||
                data['minimum_price'] != null)) {
          final key =
              city != null && city.isNotEmpty ? '${country}_$city' : country;

          // طباعة تفاصيل السعر للتشخيص
          debugPrint('وجدت سعر: $key - رمز: $codeNo');

          if (!uniquePricesMap.containsKey(key)) {
            uniquePricesMap[key] = {
              'id': data['id'],
              'code_no': codeNo,
              'country': country,
              'city': city,
              'price_door_to_door': data['price_door_to_door'] ?? 0.0,
              'for_each_1_kg': data['for_each_1_kg'] ?? 0.0,
              'minimum_price': data['minimum_price'] ?? 0.0,
            };
          }
        }
      }

      final result = uniquePricesMap.values.toList();
      debugPrint('عدد الأسعار الفريدة بعد التصفية: ${result.length}');
      return result;
    } catch (e) {
      debugPrint('خطأ في الحصول على الأسعار: $e');
      return [];
    }
  }

  // التحقق من بيانات الأسعار في قاعدة البيانات
  Future<void> verifyPricesData() async {
    try {
      // التحقق من وجود أسعار في قاعدة البيانات
      final existingPrices = await getAllUniquePrices();
      debugPrint(
          'عدد الأسعار الموجودة في قاعدة البيانات: ${existingPrices.length}');
    } catch (e) {
      debugPrint('خطأ في التحقق من بيانات الأسعار: $e');
    }
  }

  // دالة للتحقق من وجود دولة ومدينة في قاعدة البيانات
  Future<bool> checkCountryAndCityExists(String country, String city) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'code_data',
        where: 'country = ? AND city = ?',
        whereArgs: [country, city],
        limit: 1,
      );

      return result.isNotEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الدولة والمدينة: $e');
      return false;
    }
  }

  // دالة للحصول على تنسيق الكود الخاص بالفرع
  Future<Map<String, String>> getBranchCodeFormat(String branchName) async {
    try {
      // الحصول على بيانات الفرع من قاعدة البيانات
      final db = await _databaseHelper.database;
      final results = await db.query(
        DatabaseHelper.tableBranches,
        where: 'name = ?',
        whereArgs: [branchName],
        limit: 1,
      );

      if (results.isNotEmpty) {
        final branch = results.first;
        return {
          'prefix': branch['code_prefix']?.toString() ?? 'XX',
          'digits': branch['code_digits']?.toString() ?? '25',
        };
      }

      // إذا لم يتم العثور على الفرع، نرجع القيم الافتراضية
      return {'prefix': 'XX', 'digits': '25'};
    } catch (e) {
      debugPrint('خطأ في الحصول على تنسيق الكود للفرع: $e');
      return {'prefix': 'XX', 'digits': '25'};
    }
  }

  // الحصول على سعر الصرف لدولة محددة
  Future<double> getExchangeRateByCountry(String countryName) async {
    try {
      debugPrint('بدء استرجاع سعر الصرف للدولة: $countryName');

      if (countryName.isEmpty) {
        debugPrint('اسم الدولة فارغ، إرجاع القيمة الافتراضية');
        return 1309.0;
      }

      // استخدام استعلام SQL مباشر للحصول على سعر الصرف للدولة المحددة
      final db = await _databaseHelper.database;

      final result = await db.rawQuery('''
        SELECT
          CAST(exchange_rate AS REAL) as exchange_rate
        FROM ${DatabaseHelper.tableCodeData}
        WHERE
          country = ? AND
          exchange_rate IS NOT NULL
        ORDER BY updated_at DESC
        LIMIT 1
      ''', [countryName]);

      if (result.isNotEmpty && result.first['exchange_rate'] != null) {
        var exchangeRate = result.first['exchange_rate'];
        debugPrint(
            'تم استرجاع سعر الصرف للدولة $countryName: $exchangeRate (${exchangeRate.runtimeType})');

        double finalRate;

        if (exchangeRate is double) {
          finalRate = exchangeRate;
        } else if (exchangeRate is int) {
          finalRate = exchangeRate.toDouble();
        } else {
          // محاولة التحويل من أي نوع آخر
          finalRate = double.parse(exchangeRate.toString());
        }

        // التأكد من أن القيمة موجبة
        if (finalRate <= 0) {
          finalRate = 1309.0;
        }

        debugPrint('سعر الصرف النهائي للدولة $countryName: $finalRate');
        return finalRate;
      } else {
        debugPrint(
            'لم يتم العثور على سعر صرف للدولة $countryName، إرجاع القيمة الافتراضية');
        return 1309.0;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على سعر الصرف للدولة $countryName: $e');
      return 1309.0;
    }
  }

  // الحصول على معلومات الفرع بناء على اسمه
  Future<Map<String, dynamic>?> getBranchInfo(String branchName) async {
    try {
      debugPrint('بدء استرجاع معلومات الفرع: $branchName');

      if (branchName.isEmpty) {
        debugPrint('اسم الفرع فارغ');
        return null;
      }

      final branchInfo = await _databaseHelper.getBranchByName(branchName);

      if (branchInfo != null) {
        debugPrint('تم العثور على معلومات الفرع: ${branchInfo['name']}');
        return branchInfo;
      } else {
        debugPrint('لم يتم العثور على معلومات للفرع: $branchName');
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الفرع $branchName: $e');
      return null;
    }
  }

  // الحصول على معلومات الوكيل بناءً على اسم المدينة والدولة
  Future<Map<String, dynamic>?> getAgentInfoByCity(
      String cityName, String countryName) async {
    try {
      debugPrint(
          'بدء استرجاع معلومات الوكيل للمدينة: $cityName، الدولة: $countryName');

      if (cityName.isEmpty || countryName.isEmpty) {
        debugPrint('اسم المدينة أو الدولة فارغ');
        return null;
      }

      final db = await _databaseHelper.database;

      // استعلام للحصول على أحدث معلومات الوكيل للمدينة والدولة المحددة
      final result = await db.rawQuery('''
        SELECT 
          agent_name, 
          agent_phone1, 
          agent_phone2, 
          agent_address,
          city,
          country,
          updated_at
        FROM ${DatabaseHelper.tableCodeData}
        WHERE city = ? AND country = ?
        AND (agent_name IS NOT NULL OR agent_phone1 IS NOT NULL OR agent_phone2 IS NOT NULL OR agent_address IS NOT NULL)
        ORDER BY updated_at DESC
        LIMIT 1
      ''', [cityName, countryName]);

      if (result.isNotEmpty) {
        Map<String, dynamic> agentInfo = result.first;
        debugPrint('تم العثور على معلومات الوكيل للمدينة $cityName:');
        debugPrint('- اسم الوكيل: ${agentInfo['agent_name']}');
        debugPrint('- هاتف 1: ${agentInfo['agent_phone1']}');
        debugPrint('- هاتف 2: ${agentInfo['agent_phone2']}');
        debugPrint('- العنوان: ${agentInfo['agent_address']}');
        return agentInfo;
      } else {
        debugPrint(
            'لم يتم العثور على معلومات الوكيل للمدينة: $cityName، الدولة: $countryName');
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الوكيل للمدينة $cityName: $e');
      return null;
    }
  }

  // دالة متخصصة لتحديث جميع السجلات لدولة معينة
  Future<int> updateCountryData(
      String oldCountryName, String newCountryName, double exchangeRate) async {
    try {
      debugPrint(
          'بدء تحديث جميع سجلات الدولة من "$oldCountryName" إلى "$newCountryName"');
      debugPrint('سعر الصرف الجديد: $exchangeRate');

      // الحصول على قاعدة البيانات
      final db = await _databaseHelper.database;

      // تحديث جميع السجلات التي تحتوي على اسم الدولة القديم
      final updateCount = await db.update(
        DatabaseHelper.tableCodeData,
        {
          'country': newCountryName,
          'exchange_rate': exchangeRate,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'country = ?',
        whereArgs: [oldCountryName],
      );

      debugPrint('تم تحديث $updateCount سجل للدولة "$oldCountryName"');

      // ملاحظة: تحديث قاعدة بيانات الفرع مؤجل لحين حل مشاكل الوصول
      debugPrint('التحديث في قاعدة البيانات الرئيسية مكتمل');

      return updateCount;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات الدولة: $e');
      throw Exception('فشل في تحديث بيانات الدولة: $e');
    }
  }

  // دالة متخصصة لتحديث جميع السجلات لمدينة معينة في دولة معينة
  // هذه الدالة تحل مشكلة إضافة مدينة جديدة بدلاً من تحديث المدينة الحالية
  // عبر تحديث جميع السجلات التي تحتوي على نفس اسم المدينة والدولة دفعة واحدة
  Future<int> updateCityData(
    String oldCountryName,
    String oldCityName,
    String newCountryName,
    String newCityName,
    Map<String, dynamic> additionalData,
  ) async {
    try {
      debugPrint('بدء تحديث جميع سجلات المدينة');
      debugPrint('من: "$oldCountryName - $oldCityName"');
      debugPrint('إلى: "$newCountryName - $newCityName"');
      debugPrint('بيانات إضافية: $additionalData');

      // الحصول على قاعدة البيانات
      final db = await _databaseHelper.database;

      // إعداد البيانات للتحديث
      final updateData = {
        'country': newCountryName,
        'city': newCityName,
        'updated_at': DateTime.now().toIso8601String(),
        ...additionalData, // إضافة أي بيانات إضافية (مثل معلومات الوكيل)
      };

      // تحديث جميع السجلات التي تحتوي على المدينة والدولة القديمة
      final updateCount = await db.update(
        DatabaseHelper.tableCodeData,
        updateData,
        where: 'country = ? AND city = ?',
        whereArgs: [oldCountryName, oldCityName],
      );

      debugPrint(
          'تم تحديث $updateCount سجل للمدينة "$oldCityName" في الدولة "$oldCountryName"');

      return updateCount;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات المدينة: $e');
      throw Exception('فشل في تحديث بيانات المدينة: $e');
    }
  }

  // دالة متخصصة لتحديث أسعار مدينة معينة
  // تحديث جميع سجلات الأسعار المرتبطة بالمدينة بدلاً من إنشاء سجلات جديدة
  Future<int> updateCityPrices(
    String oldCountryName,
    String oldCityName,
    String newCountryName,
    String newCityName,
    double doorToDoorPrice,
    double forEachKgPrice,
    double minimumPrice,
  ) async {
    try {
      debugPrint('بدء تحديث أسعار المدينة');
      debugPrint('من: "$oldCountryName - $oldCityName"');
      debugPrint('إلى: "$newCountryName - $newCityName"');

      // الحصول على قاعدة البيانات
      final db = await _databaseHelper.database;

      // تحديث جميع السجلات الخاصة بالأسعار
      final updateCount = await db.update(
        DatabaseHelper.tableCodeData,
        {
          'country': newCountryName,
          'city': newCityName,
          'price_door_to_door': doorToDoorPrice,
          'for_each_1_kg': forEachKgPrice,
          'minimum_price': minimumPrice,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'country = ? AND city = ? AND code_no LIKE ?',
        whereArgs: [oldCountryName, oldCityName, 'PRICE_%'],
      );

      debugPrint('تم تحديث $updateCount سجل أسعار للمدينة "$oldCityName"');

      return updateCount;
    } catch (e) {
      debugPrint('خطأ في تحديث أسعار المدينة: $e');
      throw Exception('فشل في تحديث أسعار المدينة: $e');
    }
  }

  // حذف جميع السجلات المرتبطة بدولة معينة
  Future<int> deleteCountryData(String countryName) async {
    try {
      debugPrint('=== بدء حذف جميع سجلات الدولة: $countryName ===');

      // الحصول على جميع السجلات المرتبطة بهذه الدولة أولاً للتشخيص
      final db = await _databaseHelper.database;

      // عد السجلات قبل الحذف
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseHelper.tableCodeData} WHERE country = ?',
        [countryName],
      );
      final recordCount = countResult.first['count'] as int;
      debugPrint('عدد السجلات الموجودة للدولة $countryName: $recordCount');

      if (recordCount == 0) {
        debugPrint('لا توجد سجلات للدولة $countryName');
        return 0;
      }

      // عرض تفاصيل السجلات التي سيتم حذفها
      final recordsToDelete = await db.query(
        DatabaseHelper.tableCodeData,
        columns: ['code_no', 'country', 'city'],
        where: 'country = ?',
        whereArgs: [countryName],
      );

      debugPrint('السجلات التي سيتم حذفها:');
      for (var record in recordsToDelete) {
        debugPrint(
            '  - ${record['code_no']}: ${record['country']} - ${record['city'] ?? 'لا توجد مدينة'}');
      }

      // حذف جميع السجلات التي تحتوي على نفس اسم الدولة
      final deletedCount = await db.delete(
        DatabaseHelper.tableCodeData,
        where: 'country = ?',
        whereArgs: [countryName],
      );

      debugPrint('✅ تم حذف $deletedCount سجل للدولة: $countryName');

      // التحقق من نجاح الحذف
      final remainingCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseHelper.tableCodeData} WHERE country = ?',
        [countryName],
      );
      final remaining = remainingCount.first['count'] as int;

      if (remaining > 0) {
        debugPrint(
            '⚠️ تحذير: لا تزال هناك $remaining سجل للدولة $countryName بعد الحذف');
      } else {
        debugPrint('✅ تم حذف جميع سجلات الدولة $countryName بنجاح');
      }

      return deletedCount;
    } catch (e) {
      debugPrint('❌ خطأ في حذف سجلات الدولة $countryName: $e');
      return 0;
    }
  }

  // تعطيل دولة معينة
  Future<int> disableCountryData(String countryName) async {
    try {
      debugPrint('=== بدء تعطيل الدولة: $countryName ===');

      final result = await _databaseHelper.disableCountryData(countryName);

      debugPrint('✅ تم تعطيل الدولة $countryName - النتيجة: $result');
      return result;
    } catch (e) {
      debugPrint('خطأ في تعطيل الدولة $countryName: $e');
      return 0;
    }
  }

  // تفعيل دولة معينة
  Future<int> enableCountryData(String countryName) async {
    try {
      debugPrint('=== بدء تفعيل الدولة: $countryName ===');

      final result = await _databaseHelper.enableCountryData(countryName);

      debugPrint('✅ تم تفعيل الدولة $countryName - النتيجة: $result');
      return result;
    } catch (e) {
      debugPrint('خطأ في تفعيل الدولة $countryName: $e');
      return 0;
    }
  }

  // التحقق من حالة تفعيل الدولة
  Future<bool> isCountryEnabled(String countryName) async {
    try {
      return await _databaseHelper.isCountryEnabled(countryName);
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة الدولة $countryName: $e');
      return true;
    }
  }

  // الحصول على الدول المفعلة فقط (للمستخدم العادي)
  Future<List<Map<String, dynamic>>> getEnabledCountries() async {
    try {
      debugPrint('=== بدء استرجاع الدول المفعلة فقط ===');

      // استخدم قاعدة البيانات الرئيسية للحصول على البيانات الكاملة
      final db = await _databaseHelper.mainDatabase;

      // التحقق من وجود عمود is_enabled
      final tableInfo = await db
          .rawQuery("PRAGMA table_info(${DatabaseHelper.tableCodeData})");
      final columnNames =
          tableInfo.map((col) => col['name'] as String).toList();
      final hasIsEnabledColumn = columnNames.contains('is_enabled');

      debugPrint('عمود is_enabled موجود: $hasIsEnabledColumn');

      // استعلام للحصول على الدول المفعلة فقط
      String whereClause = 'WHERE country IS NOT NULL AND country != \'\'';
      if (hasIsEnabledColumn) {
        whereClause += ' AND is_enabled = 1';
      }

      final result = await db.rawQuery('''
        SELECT DISTINCT country, 
               MIN(code_no) as code_no,
               MAX(exchange_rate) as exchange_rate
        FROM ${DatabaseHelper.tableCodeData} 
        $whereClause
        GROUP BY country 
        ORDER BY country
      ''');

      debugPrint('تم استرجاع ${result.length} دولة مفعلة من قاعدة البيانات');

      return result;
    } catch (e) {
      debugPrint('خطأ في استرجاع الدول المفعلة: $e');
      return [];
    }
  }

  // الحصول على جميع الدول مع حالة التفعيل (للأدمن)
  Future<List<Map<String, dynamic>>> getAllCountriesWithStatus() async {
    try {
      debugPrint('=== بدء استرجاع جميع الدول مع حالة التفعيل ===');

      // استخدم قاعدة البيانات الرئيسية دائماً للأدمن
      final db = await _databaseHelper.mainDatabase;

      // التحقق من وجود عمود is_enabled
      final tableInfo = await db
          .rawQuery("PRAGMA table_info(${DatabaseHelper.tableCodeData})");
      final columnNames =
          tableInfo.map((col) => col['name'] as String).toList();
      final hasIsEnabledColumn = columnNames.contains('is_enabled');

      debugPrint('عمود is_enabled موجود: $hasIsEnabledColumn');

      // استعلام للحصول على جميع الدول مع حالة التفعيل
      String selectClause =
          'SELECT DISTINCT country, MIN(code_no) as code_no, MAX(exchange_rate) as exchange_rate';
      if (hasIsEnabledColumn) {
        selectClause += ', MAX(is_enabled) as is_enabled';
      }

      final result = await db.rawQuery('''
        $selectClause
        FROM ${DatabaseHelper.tableCodeData} 
        WHERE country IS NOT NULL AND country != ''
        GROUP BY country 
        ORDER BY country
      ''');

      // إضافة حالة التفعيل الافتراضية إذا لم يكن العمود موجوداً
      if (!hasIsEnabledColumn) {
        for (var country in result) {
          country['is_enabled'] = 1;
        }
      }

      debugPrint(
          'تم استرجاع ${result.length} دولة مع حالة التفعيل من قاعدة البيانات');

      for (var country in result) {
        final isEnabled = (country['is_enabled'] as int) == 1;
        debugPrint(
            '  - ${country['country']} (${isEnabled ? 'مفعلة' : 'معطلة'})');
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في استرجاع الدول مع حالة التفعيل: $e');
      return [];
    }
  }
}
