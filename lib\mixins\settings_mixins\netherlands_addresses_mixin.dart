import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/riverpod/address_providers.dart';
import '../../providers/riverpod/address_database_provider.dart';
import '../../services/excel_import_service.dart';
import '../../utils/file_helper.dart';
import '../../widgets/settings_parts/paginated_address_list.dart';
import 'settings_variables_mixin.dart';
import 'address_import_export_mixin.dart';

/// Mixin يحتوي على الوظائف المتعلقة بعناوين هولندا
mixin NetherlandsAddressesMixin<T extends ConsumerStatefulWidget>
    on
        ConsumerState<T>,
        SettingsVariablesMixin<T>,
        AddressImportExportMixin<T> {
  // تحميل عناوين هولندا من قاعدة البيانات
  Future<void> loadNetherlandsAddresses() async {
    isLoadingNetherlands = true;

    try {
      final addressDbService = ref.read(addressDatabaseServiceProvider);
      final addresses = await addressDbService.getNetherlandsAddresses();

      if (mounted) {
        ref.read(netherlandsAddressesProvider.notifier).setAddresses(addresses);
      }
    } catch (e) {
      if (mounted) {
        showErrorMessage('Error loading Netherlands addresses: $e');
      }
    } finally {
      if (mounted) {
        isLoadingNetherlands = false;
      }
    }
  }

  // استيراد عناوين هولندا
  Future<void> importNetherlandsAddresses() async {
    // إعادة تعيين متغيرات التقدم
    updateImportProgress(
      progress: 0.0,
      processedRows: 0,
      totalRows: 0,
      stage: 'Reading File',
    );
    isImportingNetherlands = true;

    // عرض مربع حوار التقدم
    showImportProgressDialog('Importing Netherlands Addresses');

    try {
      // استيراد البيانات من ملف الإكسل
      final addresses = await ExcelImportService.importNetherlandsAddresses(
        ignoreNonCriticalColumns: true, // تجاهل الأعمدة غير الحرجة
        onError: (errorMessage) {
          if (mounted) {
            // إغلاق مربع حوار التقدم
            Navigator.of(context).pop();
            showErrorMessage(errorMessage, forceShow: true);
          }
        },
        onProgress: (progress, processedRows, totalRows) {
          if (mounted) {
            updateProgressAndShowDialog(
              title: 'Importing Netherlands Addresses',
              progress: progress,
              processedRows: processedRows,
              totalRows: totalRows,
              stage: 'Reading File',
            );
          }
        },
      );

      if (mounted && addresses.isNotEmpty) {
        // تحديث المرحلة
        updateProgressAndShowDialog(
          title: 'Importing Netherlands Addresses',
          progress: 0.0,
          processedRows: 0,
          totalRows: addresses.length,
          stage: 'Saving Data to Database',
        );

        // حفظ العناوين في قاعدة البيانات
        final addressDbService = ref.read(addressDatabaseServiceProvider);
        final result = await addressDbService.saveNetherlandsAddresses(
          addresses,
          skipExisting: true, // تجاهل العناصر المكررة
          onProgress: (progress, processedItems, totalItems) {
            if (mounted) {
              updateProgressAndShowDialog(
                title: 'Importing Netherlands Addresses',
                progress: progress,
                processedRows: processedItems,
                totalRows: totalItems,
                stage: 'Saving Data to Database',
              );
            }
          },
        );

        // إغلاق مربع حوار التقدم
        if (mounted) {
          Navigator.of(context).pop();
        }

        // تحديث حالة التطبيق
        await loadNetherlandsAddresses(); // إعادة تحميل العناوين من قاعدة البيانات

        // استخراج الإحصائيات
        final success = result['success'] as bool;

        if (success) {
          final stats = result['stats'] as Map<String, dynamic>;
          final totalImported = stats['totalImported'] as int;
          final newItems = stats['newItems'] as int;
          final skippedItems = stats['skippedItems'] as int;

          // عرض مربع حوار الإحصائيات
          showImportStatsDialog(
            'Netherlands Addresses Import Statistics',
            totalImported,
            newItems,
            skippedItems,
          );
        } else {
          // عرض رسالة الخطأ
          showErrorMessage(
            'Error saving addresses to database: ${result['error']}',
            forceShow: true,
          );
        }
      } else if (mounted) {
        // إغلاق مربع حوار التقدم
        Navigator.of(context).pop();

        showErrorMessage(
          addresses.isEmpty
              ? 'No addresses were imported. Please check the file format.'
              : 'No addresses were imported',
          forceShow: true,
        );
      }
    } catch (e) {
      if (mounted) {
        // إغلاق مربع حوار التقدم
        Navigator.of(context).pop();
        showErrorMessage('Error importing addresses: $e', forceShow: true);
      }
    } finally {
      if (mounted) {
        isImportingNetherlands = false;
      }
    }
  }

  // حذف عنوان هولندا
  Future<void> deleteNetherlandsAddress(int index) async {
    try {
      // حذف العنوان من الحالة
      ref.read(netherlandsAddressesProvider.notifier).removeAddress(index);

      // تحديث قاعدة البيانات
      final addressDbService = ref.read(addressDatabaseServiceProvider);
      final addresses = ref.read(netherlandsAddressesProvider);
      final result = await addressDbService.saveNetherlandsAddresses(
        addresses,
        skipExisting: false, // نريد استبدال جميع البيانات
      );

      final success = result['success'] as bool;

      if (mounted) {
        if (success) {
          showSuccessMessage('Address deleted successfully');
        } else {
          showErrorMessage('Error deleting address');
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorMessage('Error deleting address: $e');
      }
    }
  }

  // حذف جميع عناوين هولندا
  Future<void> clearAllNetherlandsAddresses() async {
    // التحقق من وجود عناوين للحذف
    final netherlandsAddresses = ref.read(netherlandsAddressesProvider);
    if (netherlandsAddresses.isEmpty) {
      showErrorMessage('No addresses to delete');
      return;
    }

    // عرض مربع حوار التأكيد
    if (!mounted) return;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Delete All Netherlands Addresses'),
          ],
        ),
        content: Text(
          'Are you sure you want to delete all ${netherlandsAddresses.length} Netherlands addresses?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // عرض مؤشر التحميل
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Deleting all addresses...'),
            ],
          ),
        ),
      );

      // حذف البيانات من قاعدة البيانات
      final addressDbService = ref.read(addressDatabaseServiceProvider);
      final success = await addressDbService.clearAllNetherlandsAddresses();

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success) {
        // تحديث الحالة المحلية
        ref.read(netherlandsAddressesProvider.notifier).clearAddresses();

        if (mounted) {
          showSuccessMessage('All Netherlands addresses deleted successfully');
        }
      } else {
        if (mounted) {
          showErrorMessage('Error deleting all addresses');
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة حدوث خطأ
      if (mounted) {
        Navigator.of(context).pop();
        showErrorMessage('Error deleting all addresses: $e');
      }
    }
  }

  // تصدير عناوين هولندا إلى ملف Excel
  Future<void> exportNetherlandsAddresses() async {
    final netherlandsAddresses = ref.read(netherlandsAddressesProvider);

    if (netherlandsAddresses.isEmpty) {
      showErrorMessage('No addresses to export');
      return;
    }

    try {
      // عرض مؤشر التحميل
      showExportDialog();

      // تحضير البيانات للتصدير
      final headers = [
        'Postal Code',
        'Street',
        'Min Number',
        'Max Number',
        'City Name'
      ];
      final data = netherlandsAddresses
          .map((address) => [
                address.postalCode,
                address.street,
                address.minNummer,
                address.maxNummer,
                address.cityName,
              ])
          .toList();

      // تصدير البيانات
      final filePath = await FileHelper.exportToExcel(
        sheetName: 'Netherlands Addresses',
        headers: headers,
        data: data,
        fileName: 'Netherlands_Addresses.xlsx',
      );

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة نجاح
      if (mounted && filePath != null) {
        showSuccessMessage('Addresses exported successfully to: $filePath');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة حدوث خطأ
      if (mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة الخطأ
      showErrorMessage('Error exporting addresses: $e');
    }
  }

  // بناء محتوى قسم عناوين هولندا
  Widget buildNetherlandsAddressesTab() {
    final netherlandsAddresses = ref.watch(netherlandsAddressesProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Netherlands Full Address section
          const Text(
            'Netherlands Full Addresses',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Import Excel button for Netherlands
          Row(
            children: [
              ElevatedButton.icon(
                onPressed:
                    isImportingNetherlands ? null : importNetherlandsAddresses,
                icon: isImportingNetherlands
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.upload_file),
                label: Text(isImportingNetherlands
                    ? 'Importing...'
                    : 'Import Excel File'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const SizedBox(width: 16),

              // Export Excel button for Netherlands
              ElevatedButton.icon(
                onPressed: netherlandsAddresses.isEmpty
                    ? null
                    : exportNetherlandsAddresses,
                icon: const Icon(Icons.download_rounded),
                label: const Text('Export Excel File'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  backgroundColor: Colors.green,
                ),
              ),

              const SizedBox(width: 16),

              // Delete All button for Netherlands
              ElevatedButton.icon(
                onPressed: netherlandsAddresses.isEmpty
                    ? null
                    : clearAllNetherlandsAddresses,
                icon: const Icon(Icons.delete_sweep),
                label: const Text('Delete All'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),

              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'Tip: Maximum number of rows in the imported file should not exceed 5000 rows',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Netherlands addresses list
          Expanded(
            child: Center(
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.75,
                child: isLoadingNetherlands
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : netherlandsAddresses.isEmpty
                        ? const Center(
                            child: Text('No addresses imported'),
                          )
                        : PaginatedAddressList<dynamic>(
                            items: netherlandsAddresses,
                            columns: const [
                              'Postal Code',
                              'Street',
                              'Min Number',
                              'Max Number',
                              'City Name'
                            ],
                            columnWidths: const [0.2, 0.3, 0.15, 0.15, 0.2],
                            itemToValues: (item) => [
                              item.postalCode,
                              item.street,
                              item.minNummer,
                              item.maxNummer,
                              item.cityName,
                            ],
                            onDelete: deleteNetherlandsAddress,
                            isLoading: isLoadingNetherlands,
                            emptyMessage: 'No addresses imported',
                            searchHint: 'Search Netherlands addresses...',
                          ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
