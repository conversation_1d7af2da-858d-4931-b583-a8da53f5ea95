import 'package:flutter/material.dart';
import 'dart:convert'; // لدعم تحويل base64
// للحصول على المجلد المؤقت
import '../../services/code_data_service.dart';
import '../../services/event_bus_service.dart';
import '../../services/database_helper.dart';
import '../../services/data_preload_service.dart';

class CitiesTab extends StatefulWidget {
  final CodeDataService codeDataService;

  const CitiesTab({super.key, required this.codeDataService});

  @override
  State<CitiesTab> createState() => _CitiesTabState();
}

class _CitiesTabState extends State<CitiesTab> {
  List<Map<String, dynamic>> _cities = [];
  List<Map<String, dynamic>> _filteredCities = [];
  List<Map<String, dynamic>> _countries = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  final EventBusService _eventBus = EventBusService();

  // إضافة متحكمات النصوص لحقول الأسعار
  final TextEditingController _doorToDoorController = TextEditingController();
  final TextEditingController _forEachKgController = TextEditingController();
  final TextEditingController _minimumPriceController = TextEditingController();

  // إضافة متحكمات النصوص لحقول معلومات الوكيل
  final TextEditingController _agentNameController = TextEditingController();
  final TextEditingController _agentPhone1Controller = TextEditingController();
  final TextEditingController _agentPhone2Controller = TextEditingController();
  final TextEditingController _agentAddressController = TextEditingController();

  String? _selectedCountry;
  bool _updateSuccess = false; // متغير لتتبع نجاح عملية التحديث

  // إضافة خدمة تحميل البيانات
  final DataPreloadService _dataPreloadService = DataPreloadService();

  @override
  void initState() {
    super.initState();
    _loadCities();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    // التخلص من متحكمات النصوص لحقول الأسعار
    _doorToDoorController.dispose();
    _forEachKgController.dispose();
    _minimumPriceController.dispose();

    // التخلص من متحكمات النصوص لحقول معلومات الوكيل
    _agentNameController.dispose();
    _agentPhone1Controller.dispose();
    _agentPhone2Controller.dispose();
    _agentAddressController.dispose();

    super.dispose();
  }

  Future<void> _loadCities() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // التأكد من وجود أعمدة معلومات الوكيل في قاعدة البيانات
      final DatabaseHelper dbHelper = DatabaseHelper();
      await dbHelper.ensureAgentColumnsExist();

      // استخدام الوظيفة للحصول على جميع المدن الفريدة
      final cities = await widget.codeDataService.getAllUniqueCities();

      // استخدام الوظيفة للحصول على جميع الدول الفريدة
      final countries = await widget.codeDataService.getAllUniqueCountries();

      // إزالة التكرار من قائمة الدول
      final Set<String> uniqueCountryNames = {};
      final List<Map<String, dynamic>> uniqueCountries = [];

      for (var country in countries) {
        final countryName = country['country'] as String;
        if (countryName.isNotEmpty &&
            !uniqueCountryNames.contains(countryName)) {
          uniqueCountryNames.add(countryName);
          uniqueCountries.add(country);
        }
      }

      setState(() {
        _cities = cities;
        _filteredCities = cities;
        _countries = uniqueCountries;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _cities = [];
        _filteredCities = [];
        _countries = [];
        _isLoading = false;
      });
      _showErrorDialog('Error loading cities: $e');
    }
  }

  void _filterCities(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCities = _cities;
      } else {
        _filteredCities = _cities
            .where((city) =>
                city['city']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                city['country']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  // دالة لعرض معلومات الوكيل
  Future<void> _showAgentInfo(Map<String, dynamic> city) async {
    // إعادة تعيين متغير النجاح في بداية العملية
    _updateSuccess = false;

    // تعيين قيم الوكيل الحالية في متحكمات النص
    _agentNameController.text = city['agent_name'] ?? '';
    _agentPhone1Controller.text = city['agent_phone1'] ?? '';
    _agentPhone2Controller.text = city['agent_phone2'] ?? '';
    _agentAddressController.text = city['agent_address'] ?? '';

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.people, color: Colors.blue, size: 18),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Agent Info - ${city['city']}',
                style: const TextStyle(fontSize: 16),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        contentPadding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        content: SizedBox(
          width: 300,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Agent Details',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: _agentNameController,
                  decoration: const InputDecoration(
                    labelText: 'Agent Name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person, size: 18),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: _agentPhone1Controller,
                  decoration: const InputDecoration(
                    labelText: 'Agent Phone 1',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.phone, size: 18),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: _agentPhone2Controller,
                  decoration: const InputDecoration(
                    labelText: 'Agent Phone 2',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.phone_android, size: 18),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: _agentAddressController,
                  decoration: const InputDecoration(
                    labelText: 'Agent Address',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_on, size: 18),
                    alignLabelWithHint: true,
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton.icon(
            icon: const Icon(Icons.cancel, color: Colors.grey, size: 16),
            label: const Text('Cancel', style: TextStyle(fontSize: 12)),
            onPressed: () => Navigator.pop(context),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.save, size: 16),
            label: const Text('Save', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            ),
            onPressed: () async {
              // إغلاق النافذة المنبثقة وإظهار مؤشر التحميل
              if (mounted) {
                // 1. أولاً: إغلاق الحوار
                Navigator.pop(context);

                // 2. إظهار مؤشر التحميل
                setState(() {
                  _isLoading = true;
                });

                try {
                  // 3. الحصول على القيم من المتحكمات
                  final String agentName = _agentNameController.text;
                  final String agentPhone1 = _agentPhone1Controller.text;
                  final String agentPhone2 = _agentPhone2Controller.text;
                  final String agentAddress = _agentAddressController.text;
                  final String codeNo = city['code_no'];

                  // طباعة القيم التي سيتم حفظها

                  // 4. إعداد نسخة من البيانات للتحديث المحلي
                  final Map<String, dynamic> updatedCity =
                      Map<String, dynamic>.from(city);
                  updatedCity['agent_name'] = agentName;
                  updatedCity['agent_phone1'] = agentPhone1;
                  updatedCity['agent_phone2'] = agentPhone2;
                  updatedCity['agent_address'] = agentAddress;
                  updatedCity['updated_at'] = DateTime.now().toIso8601String();

                  // 5. استخدام DatabaseHelper مباشرة لتحديث معلومات الوكيل
                  final DatabaseHelper dbHelper = DatabaseHelper();
                  int result = 0;

                  try {
                    // استخدام الدالة الجديدة المطورة لتحديث معلومات الوكيل
                    Map<String, dynamic> agentData = {
                      'agent_name': agentName,
                      'agent_phone1': agentPhone1,
                      'agent_phone2': agentPhone2,
                      'agent_address': agentAddress,
                    };

                    debugPrint(
                        'محاولة تحديث معلومات الوكيل للكود $codeNo باستخدام الدالة المحسنة');

                    Map<String, dynamic> updateResult =
                        await dbHelper.updateAgentInfoDirect(codeNo, agentData);

                    // تسجيل نتيجة التحديث للتشخيص
                    // ?? ??? ????? debugPrint

                    if (updateResult['success'] == true) {
                      result = updateResult['updates'];
                      _updateSuccess = true; // تعيين حالة النجاح إلى صحيح
                      // إذا نجح التحديث، أوقف العملية هنا ولا تحاول أي طرق تحديث أخرى
                      // تحديث البيانات المحلية سيتم في الكود اللاحق
                    } else {
                      debugPrint(
                          'فشل تحديث معلومات الوكيل: ${updateResult['message']}');

                      // محاولة تحديث البيانات باستخدام طريقة أخرى
                      debugPrint(
                          'محاولة تحديث بالطريقة الثانية updateAgentInfo');
                      // محاولة بديلة باستخدام الدالة القديمة
                      result = await dbHelper.updateAgentInfo(codeNo, agentName,
                          agentPhone1, agentPhone2, agentAddress);

                      // ?? ??? ????? debugPrint
                    }
                  } catch (dbError) {
                    // إذا فشلت المحاولة الأولى، نجرب محاولة أخرى باستخدام طريقة مختلفة
                    debugPrint(
                        'المحاولة الأولى للتحديث فشلت، محاولة بديلة: $dbError');
                    try {
                      debugPrint(
                          'محاولة تحديث البيانات بطريقة ثالثة باستخدام البيانات المختصرة');
                      final Map<String, dynamic> minimalUpdateData = {
                        'code_no': codeNo,
                        'agent_name': agentName,
                        'agent_phone1': agentPhone1,
                        'agent_phone2': agentPhone2,
                        'agent_address': agentAddress,
                      };

                      // استخدام الدالة المباشرة لتحديث معلومات الوكيل
                      Map<String, dynamic> serviceResult = await dbHelper
                          .updateAgentInfoDirect(codeNo, minimalUpdateData);

                      // ?? ??? ????? debugPrint

                      if (serviceResult['success'] == true) {
                        result = serviceResult['updates'];
                      } else {
                        throw Exception(
                            'فشل تحديث معلومات الوكيل: ${serviceResult['message']}');
                      }
                    } catch (serviceError) {
                      // ?? ??? ????? debugPrint

                      // محاولة استخدام codeDataService كحل بديل أخير
                      try {
                        debugPrint(
                            'محاولة تحديث البيانات باستخدام codeDataService');
                        await widget.codeDataService
                            .updateCodeData(updatedCity);
                        result = 1; // اعتبار أن التحديث تم بنجاح
                        debugPrint(
                            'تم تحديث البيانات بنجاح باستخدام codeDataService');
                      } catch (finalError) {
                        // ?? ??? ????? debugPrint
                        rethrow;
                      }
                    }
                  }

                  if (result > 0 || result == 0) {
                    // اعتبر 0 نجاحًا (في بعض الحالات لا يتم تغيير أي سطر)
                    // تحديث القوائم المحلية
                    if (mounted) {
                      setState(() {
                        // تحديث _filteredCities
                        final filteredIndex = _filteredCities
                            .indexWhere((c) => c['code_no'] == codeNo);
                        if (filteredIndex != -1) {
                          _filteredCities[filteredIndex]['agent_name'] =
                              agentName;
                          _filteredCities[filteredIndex]['agent_phone1'] =
                              agentPhone1;
                          _filteredCities[filteredIndex]['agent_phone2'] =
                              agentPhone2;
                          _filteredCities[filteredIndex]['agent_address'] =
                              agentAddress;
                          _filteredCities[filteredIndex]['updated_at'] =
                              updatedCity['updated_at'];

                          // ?? ??? ????? debugPrint
                        } else {
                          debugPrint(
                              'لم يتم العثور على المدينة في _filteredCities');
                        }

                        // تحديث _cities
                        final originalIndex =
                            _cities.indexWhere((c) => c['code_no'] == codeNo);
                        if (originalIndex != -1) {
                          _cities[originalIndex]['agent_name'] = agentName;
                          _cities[originalIndex]['agent_phone1'] = agentPhone1;
                          _cities[originalIndex]['agent_phone2'] = agentPhone2;
                          _cities[originalIndex]['agent_address'] =
                              agentAddress;
                          _cities[originalIndex]['updated_at'] =
                              updatedCity['updated_at'];

                          // ?? ??? ????? debugPrint
                        } else {
                          // ?? ??? ????? debugPrint
                        }

                        _isLoading = false;
                      });

                      // عرض رسالة النجاح
                      _showSuccessSnackBar(
                          'Agent information updated successfully');

                      // إعادة تحميل البيانات من القاعدة للتأكد من التحديث
                      _loadCities();
                    }
                  } else {
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                      _showErrorDialog(
                          'No records were updated. Please check the data and try again.');
                    }
                  }
                } catch (e) {
                  // ?? ??? ????? debugPrint
                  // تحقق مما إذا كان الخطأ متعلق بقراءة فقط ولكن كانت هناك عملية تحديث ناجحة سابقاً
                  if (_updateSuccess) {
                    // تجاهل رسالة الخطأ لأن التحديث نجح فعلاً
                    debugPrint(
                        'تم تجاهل خطأ القراءة فقط لأن التحديث نجح بالفعل');
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                      // إعادة تحميل البيانات من القاعدة للتأكد من التحديث
                      _loadCities();
                    }
                  } else {
                    // فقط إذا لم تكن عملية تحديث ناجحة سابقة
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                      _showErrorDialog('Error in update operation: $e');
                    }
                  }
                }
              }
            },
          ),
        ],
      ),
    );
  }

  Future<void> _addCity() async {
    _cityController.clear();
    // مسح حقول الأسعار
    _doorToDoorController.clear();
    _forEachKgController.clear();
    _minimumPriceController.clear();

    // تخزين سياق الحالي للاستخدام لاحقًا بعد العمليات غير المتزامنة
    final currentContext = context;

    // إنشاء قائمة فريدة من الدول لمنع التكرار
    final Set<String> uniqueCountryNames = {};
    final List<Map<String, dynamic>> uniqueCountries = [];

    for (var country in _countries) {
      final countryName = country['country'] as String;
      if (!uniqueCountryNames.contains(countryName)) {
        uniqueCountryNames.add(countryName);
        uniqueCountries.add(country);
      }
    }

    // طباعة الدول الفريدة للتشخيص
    // ?? ??? ????? debugPrint}');

    _selectedCountry =
        uniqueCountries.isNotEmpty ? uniqueCountries.first['country'] : null;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add New City'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Country',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCountry,
                  isExpanded: true,
                  hint: const Text('Choose a country'),
                  items: uniqueCountries.map((country) {
                    return DropdownMenuItem<String>(
                      value: country['country'],
                      child: Text(country['country']),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      _selectedCountry = value;
                      // ?? ??? ????? debugPrint
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'City Name',
                    hintText: 'Enter city name',
                    border: OutlineInputBorder(),
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
                const SizedBox(height: 16),
                // إضافة حقول الأسعار
                TextField(
                  controller: _doorToDoorController,
                  decoration: const InputDecoration(
                    labelText: 'Door to Door Price',
                    hintText: 'Enter door to door price',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _forEachKgController,
                  decoration: const InputDecoration(
                    labelText: 'For Each 1 KG',
                    hintText: 'Enter price for each 1 KG',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _minimumPriceController,
                  decoration: const InputDecoration(
                    labelText: 'Minimum Price',
                    hintText: 'Enter minimum price',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(currentContext),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (_cityController.text.isNotEmpty &&
                    _selectedCountry != null &&
                    _doorToDoorController.text.isNotEmpty &&
                    _forEachKgController.text.isNotEmpty &&
                    _minimumPriceController.text.isNotEmpty) {
                  // التحقق من وجود المدينة مسبقاً للدولة المحددة
                  final cityName = _cityController.text.trim();
                  debugPrint(
                      'Checking if city exists: $cityName for country: $_selectedCountry');

                  final existingCity = _cities
                      .where((city) =>
                          city['city'].toString().toLowerCase() ==
                              cityName.toLowerCase() &&
                          city['country'].toString() == _selectedCountry)
                      .toList();

                  // ?? ??? ????? debugPrint

                  if (existingCity.isNotEmpty) {
                    Navigator.pop(currentContext);
                    _showErrorDialog(
                        'This city already exists in this country. Please choose another city name.');
                    return;
                  }

                  try {
                    // تحويل قيم الأسعار إلى أرقام
                    final doorToDoorPrice =
                        double.tryParse(_doorToDoorController.text) ?? 0.0;
                    final forEachKgPrice =
                        double.tryParse(_forEachKgController.text) ?? 0.0;
                    final minimumPrice =
                        double.tryParse(_minimumPriceController.text) ?? 0.0;

                    // إنشاء سجل جديد للمدينة
                    final newCity = {
                      'code_no':
                          'CITY_${DateTime.now().millisecondsSinceEpoch}',
                      'city': cityName,
                      'country': _selectedCountry,
                      'flag_image':
                          null, // إزالة حفظ الصورة واستبدالها بقيمة null
                      'agent_name': '', // إضافة حقول الوكيل بقيم فارغة
                      'agent_phone1': '',
                      'agent_phone2': '',
                      'agent_address': '',
                      'created_at': DateTime.now().toIso8601String(),
                      'updated_at': DateTime.now().toIso8601String(),
                    };

                    // ?? ??? ????? debugPrint
                    // ?? ??? ????? debugPrint
                    // ?? ??? ????? debugPrint
                    // ?? ??? ????? debugPrint

                    await widget.codeDataService.insertCodeData(newCity);
                    // ?? ??? ????? debugPrint

                    // إنشاء سجل جديد للسعر
                    final newPrice = {
                      'code_no':
                          'PRICE_${DateTime.now().millisecondsSinceEpoch}',
                      'country': _selectedCountry,
                      'city': cityName,
                      'price_door_to_door': doorToDoorPrice,
                      'for_each_1_kg': forEachKgPrice,
                      'minimum_price': minimumPrice,
                      'created_at': DateTime.now().toIso8601String(),
                      'updated_at': DateTime.now().toIso8601String(),
                    };

                    // ?? ??? ????? debugPrint
                    await widget.codeDataService.insertCodeData(newPrice);

                    // إرسال إشعار بإضافة مدينة جديدة
                    _eventBus.fireEvent(
                        AppEvent(EventType.cityAdded, data: newCity));

                    // إرسال إشعار بإضافة سعر جديد
                    _eventBus.fireEvent(
                        AppEvent(EventType.priceAdded, data: newPrice));

                    if (mounted && currentContext.mounted) {
                      Navigator.pop(currentContext);
                      _loadCities();
                      _showSuccessSnackBar('City and price added successfully');
                    }
                  } catch (e) {
                    if (mounted && currentContext.mounted) {
                      Navigator.pop(currentContext);
                      _showErrorDialog('Error adding city: $e');
                    }
                  }
                } else {
                  _showErrorDialog('Please enter all required data');
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _editCity(Map<String, dynamic> city) async {
    _cityController.text = city['city'];
    _selectedCountry = city['country'];

    // تخزين سياق الحالي للاستخدام لاحقًا بعد العمليات غير المتزامنة
    final currentContext = context;

    // تحميل بيانات السعر للمدينة المحددة
    try {
      final prices = await widget.codeDataService.getAllUniquePrices();
      final cityPrices = prices
          .where((price) =>
              price['country'] == city['country'] &&
              price['city'] == city['city'])
          .toList();

      if (cityPrices.isNotEmpty) {
        final cityPrice = cityPrices.first;
        _doorToDoorController.text =
            cityPrice['price_door_to_door']?.toString() ?? '';
        _forEachKgController.text =
            cityPrice['for_each_1_kg']?.toString() ?? '';
        _minimumPriceController.text =
            cityPrice['minimum_price']?.toString() ?? '';
      } else {
        _doorToDoorController.clear();
        _forEachKgController.clear();
        _minimumPriceController.clear();
      }
    } catch (e) {
      // ?? ??? ????? debugPrint
      _doorToDoorController.clear();
      _forEachKgController.clear();
      _minimumPriceController.clear();
    }

    // إنشاء قائمة فريدة من الدول لمنع التكرار
    final Set<String> uniqueCountryNames = {};
    final List<Map<String, dynamic>> uniqueCountries = [];

    for (var country in _countries) {
      final countryName = country['country'] as String;
      if (!uniqueCountryNames.contains(countryName)) {
        uniqueCountryNames.add(countryName);
        uniqueCountries.add(country);
      }
    }

    if (!context.mounted) return;

    await showDialog(
      context: currentContext,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Edit City'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Country',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCountry,
                  isExpanded: true,
                  hint: const Text('Choose a country'),
                  items: uniqueCountries.map((country) {
                    return DropdownMenuItem<String>(
                      value: country['country'],
                      child: Text(country['country']),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      _selectedCountry = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'City Name',
                    hintText: 'Enter city name',
                    border: OutlineInputBorder(),
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
                const SizedBox(height: 16),
                // إضافة حقول الأسعار
                TextField(
                  controller: _doorToDoorController,
                  decoration: const InputDecoration(
                    labelText: 'Door to Door Price',
                    hintText: 'Enter door to door price',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _forEachKgController,
                  decoration: const InputDecoration(
                    labelText: 'For Each 1 KG',
                    hintText: 'Enter price for each 1 KG',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _minimumPriceController,
                  decoration: const InputDecoration(
                    labelText: 'Minimum Price',
                    hintText: 'Enter minimum price',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(currentContext),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (_cityController.text.isNotEmpty &&
                    _selectedCountry != null &&
                    _doorToDoorController.text.isNotEmpty &&
                    _forEachKgController.text.isNotEmpty &&
                    _minimumPriceController.text.isNotEmpty) {
                  final cityName = _cityController.text.trim();
                  final oldCityName = city['city'];
                  final oldCountryName = city['country'];

                  // التحقق من وجود المدينة مسبقاً للدولة المحددة (إذا تم تغيير الاسم أو الدولة)
                  if (cityName != oldCityName ||
                      _selectedCountry != oldCountryName) {
                    final existingCity = _cities
                        .where((c) =>
                            c['city'].toString().toLowerCase() ==
                                cityName.toLowerCase() &&
                            c['country'].toString() == _selectedCountry &&
                            c['code_no'] != city['code_no'])
                        .toList();

                    if (existingCity.isNotEmpty) {
                      Navigator.pop(currentContext);
                      _showErrorDialog(
                          'This city already exists in this country. Please choose another city name.');
                      return;
                    }
                  }

                  try {
                    // تحويل قيم الأسعار إلى أرقام
                    final doorToDoorPrice =
                        double.tryParse(_doorToDoorController.text) ?? 0.0;
                    final forEachKgPrice =
                        double.tryParse(_forEachKgController.text) ?? 0.0;
                    final minimumPrice =
                        double.tryParse(_minimumPriceController.text) ?? 0.0;

                    // ?? ??? ????? debugPrint
                    // ?? ??? ????? debugPrint
                    // ?? ??? ????? debugPrint

                    // إعداد البيانات الإضافية (معلومات الوكيل إذا كانت موجودة)
                    final additionalData = <String, dynamic>{};

                    // إضافة معلومات الوكيل إذا كانت متاحة
                    if (_agentNameController.text.isNotEmpty) {
                      additionalData['agent_name'] =
                          _agentNameController.text.trim();
                    }
                    if (_agentPhone1Controller.text.isNotEmpty) {
                      additionalData['agent_phone1'] =
                          _agentPhone1Controller.text.trim();
                    }
                    if (_agentPhone2Controller.text.isNotEmpty) {
                      additionalData['agent_phone2'] =
                          _agentPhone2Controller.text.trim();
                    }
                    if (_agentAddressController.text.isNotEmpty) {
                      additionalData['agent_address'] =
                          _agentAddressController.text.trim();
                    }

                    // تحديث جميع سجلات المدينة باستخدام الوظيفة المحسنة
                    final cityUpdateCount =
                        await widget.codeDataService.updateCityData(
                      city['country'].toString(),
                      city['city'].toString(),
                      _selectedCountry!,
                      cityName,
                      additionalData,
                    );

                    debugPrint(
                        'تم تحديث $cityUpdateCount سجل للمدينة "${city['city']}"');

                    // تحديث الأسعار باستخدام الوظيفة المحسنة
                    final priceUpdateCount =
                        await widget.codeDataService.updateCityPrices(
                      city['country'].toString(),
                      city['city'].toString(),
                      _selectedCountry!,
                      cityName,
                      doorToDoorPrice,
                      forEachKgPrice,
                      minimumPrice,
                    );

                    debugPrint(
                        'تم تحديث $priceUpdateCount سجل أسعار للمدينة "${city['city']}"');

                    // تحديث ذاكرة التخزين المؤقت
                    final key = cityName.isNotEmpty
                        ? '${_selectedCountry}_$cityName'
                        : _selectedCountry!;
                    _dataPreloadService.pricesByCountryAndCity[key] = {
                      'price_door_to_door': doorToDoorPrice,
                      'for_each_1_kg': forEachKgPrice,
                      'minimum_price': minimumPrice,
                    };

                    // إرسال إشعار بتحديث المدينة
                    _eventBus.fireEvent(AppEvent(EventType.cityUpdated, data: {
                      'old_country': city['country'],
                      'old_city': city['city'],
                      'new_country': _selectedCountry,
                      'new_city': cityName,
                      'city_updates': cityUpdateCount,
                      'price_updates': priceUpdateCount,
                    }));

                    if (mounted && currentContext.mounted) {
                      Navigator.pop(currentContext);
                      _loadCities();
                      _showSuccessSnackBar(
                          'تم تحديث المدينة بنجاح! ($cityUpdateCount سجل مدينة + $priceUpdateCount سجل أسعار)');
                    }
                  } catch (e) {
                    if (mounted && currentContext.mounted) {
                      Navigator.pop(currentContext);
                      _showErrorDialog('Error updating city: $e');
                    }
                  }
                } else {
                  _showErrorDialog('Please enter all required data');
                }
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteCity(Map<String, dynamic> city) async {
    // تخزين سياق الحالي للاستخدام لاحقًا بعد العمليات غير المتزامنة
    final currentContext = context;

    await showDialog(
      context: currentContext,
      builder: (context) => AlertDialog(
        title: const Text('Delete City'),
        content: Text('Are you sure you want to delete ${city['city']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(currentContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                // حذف المدينة
                await widget.codeDataService
                    .deleteCodeData(city['code_no'] ?? '');

                // البحث عن سجل السعر المرتبط بالمدينة وحذفه
                final prices =
                    await widget.codeDataService.getAllUniquePrices();
                final cityPrices = prices
                    .where((price) =>
                        price['country'] == city['country'] &&
                        price['city'] == city['city'])
                    .toList();

                if (cityPrices.isNotEmpty) {
                  for (var price in cityPrices) {
                    await widget.codeDataService
                        .deleteCodeData(price['code_no'] ?? '');

                    // حذف السعر من ذاكرة التخزين المؤقت
                    final key = city['city']?.isNotEmpty == true
                        ? '${city['country']}_${city['city']}'
                        : city['country'];
                    _dataPreloadService.pricesByCountryAndCity.remove(key);

                    // إرسال إشعار بحذف السعر
                    _eventBus.fireEvent(
                        AppEvent(EventType.priceDeleted, data: price));
                  }
                }

                // إرسال إشعار بحذف المدينة
                _eventBus
                    .fireEvent(AppEvent(EventType.cityDeleted, data: city));

                if (mounted && currentContext.mounted) {
                  Navigator.pop(currentContext);
                  _loadCities();
                  _showSuccessSnackBar(
                      'City and related price deleted successfully');
                }
              } catch (e) {
                if (mounted && currentContext.mounted) {
                  Navigator.pop(currentContext);
                  _showErrorDialog('Error deleting city: $e');
                }
              }
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100, // عرض في الأسفل بدلاً من الأعلى
        left: 0,
        right: 0,
        child: Center(
          // وسط الشاشة أفقياً
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              constraints: const BoxConstraints(
                maxWidth: 400, // حد أقصى للعرض
              ),
              decoration: BoxDecoration(
                color: Colors.green.shade600,
                borderRadius: BorderRadius.circular(25), // زوايا أكثر استدارة
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min, // العرض حسب المحتوى فقط
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    // بدلاً من Expanded لتجنب أخذ العرض الكامل
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد 3 ثواني مع أنيميشن
    Future.delayed(const Duration(seconds: 3), () {
      try {
        overlayEntry.remove();
      } catch (e) {
        // تجاهل الخطأ إذا كان العنصر تم حذفه بالفعل
      }
    });
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 10),
            Text('Error'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // دالة لبناء عنصر سعر
  Widget _buildPriceItem(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade800,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // دالة مساعدة للتحقق من وجود معلومات الوكيل
  bool _hasAgentInfo(Map<String, dynamic> city) {
    // التحقق من أن كل حقول الوكيل موجودة في الخريطة
    if (!city.containsKey('agent_name') ||
        !city.containsKey('agent_phone1') ||
        !city.containsKey('agent_phone2') ||
        !city.containsKey('agent_address')) {
      debugPrint(
          'بعض حقول معلومات الوكيل غير موجودة في بيانات المدينة: ${city['city']}');
      return false;
    }

    // التحقق بشكل صحيح من وجود بيانات فعلية (ليست فارغة) في حقول الوكيل
    bool hasName = city['agent_name'] != null &&
        city['agent_name'].toString().trim().isNotEmpty;
    bool hasPhone1 = city['agent_phone1'] != null &&
        city['agent_phone1'].toString().trim().isNotEmpty;
    bool hasPhone2 = city['agent_phone2'] != null &&
        city['agent_phone2'].toString().trim().isNotEmpty;
    bool hasAddress = city['agent_address'] != null &&
        city['agent_address'].toString().trim().isNotEmpty;

    // طباعة معلومات تشخيصية
    // ?? ??? ????? debugPrint
    // ?? ??? ????? debugPrint');
    // ?? ??? ????? debugPrint');
    // ?? ??? ????? debugPrint');
    // ?? ??? ????? debugPrint');

    // نعتبر أن المعلومات موجودة إذا كان هناك حقل واحد على الأقل له قيمة
    return hasName || hasPhone1 || hasPhone2 || hasAddress;
  }

  // دالة لبناء صف معلومات الوكيل
  Widget _buildAgentInfoRow(Map<String, dynamic> city) {
    // التحقق من وجود معلومات الوكيل
    if (_hasAgentInfo(city)) {
      return Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.green.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(25),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // عنوان معلومات الوكيل
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(Icons.person, size: 16, color: Colors.green),
            ),
            const SizedBox(width: 8),
            const Text(
              'Agent Information',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 13,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),

            // عرض معلومات الوكيل في الوسط
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    if (city['agent_name'] != null &&
                        city['agent_name'].toString().isNotEmpty)
                      _buildAgentInfoItem(
                          Icons.person_outline, 'Name', city['agent_name']),
                    if (city['agent_phone1'] != null &&
                        city['agent_phone1'].toString().isNotEmpty) ...[
                      const SizedBox(width: 10),
                      _buildAgentInfoItem(
                          Icons.phone, 'Phone 1', city['agent_phone1']),
                    ],
                    if (city['agent_phone2'] != null &&
                        city['agent_phone2'].toString().isNotEmpty) ...[
                      const SizedBox(width: 10),
                      _buildAgentInfoItem(
                          Icons.phone_android, 'Phone 2', city['agent_phone2']),
                    ],
                    if (city['agent_address'] != null &&
                        city['agent_address'].toString().isNotEmpty) ...[
                      const SizedBox(width: 10),
                      _buildAgentInfoItem(
                          Icons.location_on, 'Address', city['agent_address']),
                    ],
                  ],
                ),
              ),
            ),

            // زر التعديل
            const SizedBox(width: 8),
            TextButton.icon(
              icon: const Icon(Icons.edit, size: 14, color: Colors.white),
              label: const Text('Edit',
                  style: TextStyle(fontSize: 11, color: Colors.white)),
              style: TextButton.styleFrom(
                backgroundColor: Colors.green,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                minimumSize: const Size(70, 28),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              onPressed: () => _showAgentInfo(city),
            ),
          ],
        ),
      );
    } else {
      // عندما لا توجد معلومات وكيل، عرض زر لإضافتها
      return Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(25),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(Icons.person_add_alt_1,
                  size: 18, color: Colors.grey),
            ),
            const SizedBox(width: 10),
            const Text(
              'No agent information',
              style: TextStyle(fontSize: 13, color: Colors.grey),
            ),
            const Spacer(),
            ElevatedButton.icon(
              icon: const Icon(Icons.add, size: 16),
              label:
                  const Text('Add Agent Info', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              onPressed: () => _showAgentInfo(city),
            ),
          ],
        ),
      );
    }
  }

  // دالة لبناء عنصر معلومات وكيل مفرد
  Widget _buildAgentInfoItem(IconData icon, String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade300, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18, color: Colors.green.shade700),
          const SizedBox(width: 8),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة مساعدة لعرض اسم المدينة مع تلوين كلمة post باللون الأزرق
  Widget _buildCityNameWithColoredPost(String cityName) {
    if (!cityName.toLowerCase().contains('post')) {
      return Text(
        cityName,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      );
    }

    // البحث عن موقع كلمة post في النص (بغض النظر عن حالة الأحرف)
    String lowerCityName = cityName.toLowerCase();
    int postIndex = lowerCityName.indexOf('post');

    // تحديد النص قبل، أثناء، وبعد كلمة "post"
    String beforePost = cityName.substring(0, postIndex);
    // الحصول على الكلمة "post" بنفس كتابتها الأصلية كما وردت في النص
    String postText = cityName.substring(postIndex, postIndex + 4);
    String afterPost = cityName.substring(postIndex + 4);

    return RichText(
      text: TextSpan(
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        children: [
          TextSpan(text: beforePost),
          TextSpan(
            text: postText,
            style: const TextStyle(color: Colors.blue),
          ),
          TextSpan(text: afterPost),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading data...'),
                ],
              ),
            )
          : Column(
              children: [
                // حقل البحث وزر الإضافة
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          // زر إضافة مدينة جديدة
                          ElevatedButton.icon(
                            icon: const Icon(Icons.add,
                                color: Colors.white, size: 24),
                            label: const Text(
                              'Add City',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 14),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            onPressed: _addCity,
                          ),
                          const SizedBox(width: 10),
                          // زر إعادة التحميل
                          ElevatedButton.icon(
                            icon: const Icon(Icons.refresh,
                                color: Colors.white, size: 20),
                            label: const Text(
                              'Reload',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 12),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            onPressed: _loadCities,
                          ),
                          const SizedBox(width: 10),
                          // حقل البحث
                          Expanded(
                            child: TextField(
                              controller: _searchController,
                              decoration: InputDecoration(
                                labelText: 'Search Cities',
                                prefixIcon: const Icon(Icons.search),
                                suffixIcon: IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                    _filterCities('');
                                  },
                                ),
                                border: const OutlineInputBorder(),
                              ),
                              onChanged: _filterCities,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // قائمة المدن
                Expanded(
                  child: _filteredCities.isEmpty
                      ? const Center(
                          child: Text('No cities found. Add a new city.'),
                        )
                      : ListView.builder(
                          itemCount: _filteredCities.length,
                          itemBuilder: (context, index) {
                            var city = _filteredCities[index];
                            return Card(
                              margin: const EdgeInsets.all(8),
                              elevation: 3,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey.shade300),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // رأس البطاقة - اسم المدينة والدولة مع الأسعار والأزرار
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // صورة العلم
                                        city['flag_image'] != null
                                            ? Container(
                                                width: 40,
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  image: DecorationImage(
                                                    image: MemoryImage(
                                                        base64Decode(
                                                            city['flag_image']
                                                                .toString())),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              )
                                            : Container(
                                                width: 40,
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  gradient: LinearGradient(
                                                    begin: Alignment.topLeft,
                                                    end: Alignment.bottomRight,
                                                    colors: [
                                                      Colors.blue.shade300,
                                                      Colors.blue.shade600,
                                                    ],
                                                  ),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors
                                                          .blue.shade200
                                                          .withAlpha(128),
                                                      spreadRadius: 1,
                                                      blurRadius: 3,
                                                      offset:
                                                          const Offset(0, 2),
                                                    )
                                                  ],
                                                ),
                                                child: const Center(
                                                  child: Icon(
                                                    Icons.flag,
                                                    color: Colors.white,
                                                    size: 24,
                                                  ),
                                                ),
                                              ),
                                        const SizedBox(width: 12),

                                        // معلومات المدينة
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        _buildCityNameWithColoredPost(
                                                          city['city']
                                                              .toString(),
                                                        ),
                                                        const SizedBox(
                                                            height: 4),
                                                        Text(
                                                          'Country: ${city['country'].toString()}',
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            color: Colors
                                                                .grey[600],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),

                                                  // عرض الأسعار بجانب اسم المدينة على اليمين
                                                  Expanded(
                                                    child: FutureBuilder<
                                                        Map<String, dynamic>?>(
                                                      future: widget
                                                          .codeDataService
                                                          .getPricesByCountryAndCity(
                                                              city['country']
                                                                  .toString(),
                                                              city['city']
                                                                  .toString()),
                                                      builder:
                                                          (context, snapshot) {
                                                        if (snapshot
                                                                .connectionState ==
                                                            ConnectionState
                                                                .waiting) {
                                                          return const Center(
                                                            child: SizedBox(
                                                              height: 20,
                                                              width: 20,
                                                              child:
                                                                  CircularProgressIndicator(
                                                                      strokeWidth:
                                                                          2),
                                                            ),
                                                          );
                                                        } else if (snapshot
                                                            .hasError) {
                                                          return Text(
                                                              'Error: ${snapshot.error}',
                                                              style: const TextStyle(
                                                                  color: Colors
                                                                      .red,
                                                                  fontSize:
                                                                      12));
                                                        } else if (!snapshot
                                                                .hasData ||
                                                            snapshot.data ==
                                                                null) {
                                                          return const Text(
                                                              'No price data available',
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .grey,
                                                                  fontSize:
                                                                      12));
                                                        } else {
                                                          final priceData =
                                                              snapshot.data!;
                                                          return Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            children: [
                                                              _buildPriceItem(
                                                                  'Min. Price',
                                                                  '${priceData['minimum_price']} IQD'),
                                                              const SizedBox(
                                                                  width: 10),
                                                              _buildPriceItem(
                                                                  'Per Kg',
                                                                  '${priceData['for_each_1_kg']} IQD'),
                                                              const SizedBox(
                                                                  width: 10),
                                                              _buildPriceItem(
                                                                  'Door to Door',
                                                                  '${priceData['price_door_to_door']} IQD'),
                                                            ],
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ),

                                                  // أزرار التعديل والحذف بجانب بعض أفقيًا
                                                  Row(
                                                    children: [
                                                      IconButton(
                                                        icon: const Icon(
                                                            Icons.edit),
                                                        tooltip: 'Edit City',
                                                        onPressed: () {
                                                          _editCity(city);
                                                        },
                                                      ),
                                                      IconButton(
                                                        icon: const Icon(
                                                            Icons.delete,
                                                            color: Colors.red),
                                                        tooltip: 'Delete City',
                                                        onPressed: () {
                                                          _deleteCity(city);
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),

                                    // عرض معلومات الوكيل في صف منفصل
                                    _buildAgentInfoRow(city),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }
}
