import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

import 'cost_info_calculations_mixin.dart';
import 'cost_info_state_mixin.dart';

/// مزيج يحتوي على دوال الحصول على القيم وتعيينها الخاصة بمعلومات التكلفة
mixin CostInfoDataMixin<T extends StatefulWidget>
    on State<T>, CostInfoCalculationsMixin<T>, CostInfoStateMixin<T> {
  // مسجل للأحداث
  final Logger _dataLogger = Logger('CostInfoData');

  // دالة للحصول على نسبة التأمين
  double getInsurancePercent() {
    return double.tryParse(
            insurancePercentController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على قيمة البضائع
  double getGoodsValue() {
    try {
      // إزالة الفواصل من النص
      final cleanValue = goodsValueController.text.replaceAll(',', '');

      // تحويل النص إلى رقم
      final numericValue = double.tryParse(cleanValue) ?? 0;

      return numericValue;
    } catch (e) {
      _dataLogger.warning('خطأ في الحصول على قيمة البضائع: $e');
      return 0;
    }
  }

  // دالة للحصول على مبلغ التأمين
  double getInsuranceAmount() {
    return double.tryParse(
            insuranceAmountController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على تكلفة مستندات التصدير
  double getExportDoc() {
    return double.tryParse(exportDocController.text.replaceAll(',', '')) ?? 0;
  }

  // دالة للحصول على تكلفة تغليف الصناديق
  double getBoxPackingCost() {
    return double.tryParse(boxPackingCostController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على تكلفة التوصيل من الباب إلى الباب
  double getDoorToDoorCost() {
    return double.tryParse(doorToDoorCostController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على تكلفة البريد الفرعية
  double getPostSubCost() {
    try {
      // إزالة الفواصل من النص
      final cleanValue = postSubCostController.text.replaceAll(',', '');

      // طباعة القيمة للتشخيص
      _dataLogger.info('قيمة Post Sub Cost قبل التحويل: $cleanValue');

      // تحويل النص إلى رقم
      final numericValue = double.tryParse(cleanValue);

      // طباعة القيمة بعد التحويل
      _dataLogger.info('قيمة Post Sub Cost بعد التحويل: $numericValue');

      // فقط نقوم بتسجيل القيمة إذا كانت غير صالحة، ولكن لا نقوم بتحديثها تلقائيًا
      if (numericValue == null || numericValue < 0) {
        _dataLogger.warning('قيمة Post Sub Cost غير صالحة: $numericValue');
      }

      return numericValue ?? 0;
    } catch (e) {
      _dataLogger.warning('خطأ في الحصول على قيمة Post Sub Cost: $e');
      return 0;
    }
  }

  // دالة للحصول على مبلغ الخصم
  double getDiscountAmount() {
    return double.tryParse(discountAmountController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على إجمالي تكلفة البريد
  double getTotalPostCost() {
    return double.tryParse(totalPostCostController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على إجمالي المدفوع
  double getTotalPaid() {
    return double.tryParse(totalPaidController.text.replaceAll(',', '')) ?? 0;
  }

  // دالة للحصول على المبلغ غير المدفوع
  double getUnpaidAmount() {
    return double.tryParse(unpaidAmountController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على إجمالي التكلفة باليورو
  double getTotalCostEUR() {
    return double.tryParse(totalCostEURController.text.replaceAll(',', '')) ??
        0;
  }

  // دالة للحصول على المبلغ غير المدفوع باليورو
  double getUnpaidEUR() {
    return double.tryParse(unpaidEURController.text.replaceAll(',', '')) ?? 0;
  }

  // دالة للحصول على قيمة لكل كيلوغرام
  double getForEachKg() {
    // هذه الدالة تستخدم للتوافق مع home_screen_save_mixin.dart
    // في هذه الحالة، نعيد قيمة 0 لأن القيمة الفعلية موجودة في PriceInfo
    return 0.0;
  }

  // دالة للحصول على قيمة minimum price
  double getMinimumPrice() {
    // نحاول الحصول على قيمة minimum price من PriceInfo
    // هذه الدالة تستخدم في تحديد قيمة post sub cost عندما يكون الوزن أقل من الحد المسموح

    // في هذه الحالة، نفترض أن قيمة minimum price تم تخزينها في مكان آخر
    // ويمكن الوصول إليها من خلال هذه الدالة

    // لا نستخدم قيم افتراضية بعد الآن لأن الأسعار إجبارية عند إضافة المدينة
    return 0.0;
  }

  // دالة للحصول على حالة خيار التأمين
  bool getUseInsurance() {
    return useInsurance;
  }

  // دالة للحصول على حالة خيار تحويل المبلغ غير المدفوع إلى المدفوع
  bool getTransferUnpaidToPaid() {
    return transferUnpaidToPaid;
  }

  // دالة لتعيين استخدام التأمين
  void setUseInsurance(bool value) {
    setState(() {
      super.useInsurance = value;
    });
  }

  // دالة لتعيين حالة تحويل المبلغ غير المدفوع إلى المدفوع
  void setTransferUnpaidToPaid(bool value) {
    setState(() {
      super.transferUnpaidToPaid = value;
    });
  }

  // دالة لتعيين نسبة التأمين
  void setInsurancePercent(double value) {
    insurancePercentController.text = value.toString();
    calculateInsuranceAmount();

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين قيمة البضاعة
  void setGoodsValue(double value) {
    try {
      // تنسيق القيمة بالفواصل
      final formattedValue = formatNumberWithCommas(value);

      // تعيين القيمة في حقل النص
      goodsValueController.text = formattedValue;

      // إعادة حساب مبلغ التأمين
      calculateInsuranceAmount();

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      _dataLogger.warning('خطأ في تعيين قيمة البضاعة: $e');
      // في حالة حدوث خطأ، نضع قيمة صفر
      goodsValueController.text = '0';
    }
  }

  // دالة لتعيين مبلغ التأمين
  void setInsuranceAmount(double value) {
    insuranceAmountController.text = formatNumberWithCommas(value);
    calculateTotals();

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين قيمة Export Doc
  void setExportDoc(double value) {
    try {
      // تنسيق القيمة بالفواصل
      final formattedValue = formatNumberWithCommas(value);

      // تعيين القيمة في حقل النص
      exportDocController.text = formattedValue;

      calculateTotals();

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      _dataLogger.warning('خطأ في تعيين قيمة Export Doc: $e');
      // في حالة حدوث خطأ، نضع قيمة صفر
      exportDocController.text = '0';
    }
  }

  // دالة لتعيين تكلفة تعبئة الصندوق
  void setBoxPackingCost(double value) {
    boxPackingCostController.text = formatNumberWithCommas(value);
    calculateTotals();

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين تكلفة البريد الفرعية
  void setPostSubCost(double value) {
    postSubCostController.text = formatNumberWithCommas(value);
    calculateTotals();

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين تكلفة الباب إلى الباب
  void setDoorToDoorCost(double value) {
    _dataLogger.info('تعيين تكلفة الباب إلى الباب: $value');

    // طباعة تشخيصية للقيمة قبل التعيين
    debugPrint(
        'القيمة الحالية لـ Door to Door Cost قبل التعيين: ${doorToDoorCostController.text}');

    // تنسيق القيمة بالفواصل
    String formattedValue = formatNumberWithCommas(value);
    doorToDoorCostController.text = formattedValue;

    _dataLogger.fine(
        'نص متحكم تكلفة الباب إلى الباب بعد التعيين: ${doorToDoorCostController.text}');
    debugPrint('تم تعيين Door to Door Cost إلى: $formattedValue');

    // التحقق من القيمة بعد التعيين مباشرة
    try {
      double currentValue =
          double.tryParse(doorToDoorCostController.text.replaceAll(',', '')) ??
              0;
      debugPrint(
          'القيمة الحالية لـ Door to Door Cost بعد التعيين مباشرة: $currentValue');
    } catch (e) {
      debugPrint('خطأ في الحصول على القيمة الحالية: $e');
    }

    // إعادة حساب المجاميع
    calculateTotals();

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // طباعة تشخيصية للقيمة بعد تحديث واجهة المستخدم
          debugPrint(
              'القيمة النهائية لـ Door to Door Cost بعد تحديث واجهة المستخدم: ${doorToDoorCostController.text}');
        });
      }
    });
  }

  // دالة لتعيين مبلغ الخصم
  void setDiscountAmount(double value) {
    try {
      // تنسيق القيمة بالفواصل
      final formattedValue = formatNumberWithCommas(value);

      // تعيين القيمة في حقل النص
      discountAmountController.text = formattedValue;

      calculateTotals();

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      _dataLogger.warning('خطأ في تعيين قيمة Discount Amount: $e');
      // في حالة حدوث خطأ، نضع قيمة صفر
      discountAmountController.text = '0';
    }
  }

  // دالة لتعيين إجمالي تكلفة البريد
  void setTotalPostCost(double value) {
    totalPostCostController.text = formatNumberWithCommas(value);
    calculateUnpaid();

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين إجمالي المدفوع
  void setTotalPaid(double value) {
    try {
      // تنسيق القيمة بالفواصل
      final formattedValue = formatNumberWithCommas(value);

      // تعيين القيمة في حقل النص
      totalPaidController.text = formattedValue;

      calculateUnpaid();

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      _dataLogger.warning('خطأ في تعيين قيمة Total Paid: $e');
      // في حالة حدوث خطأ، نضع قيمة صفر
      totalPaidController.text = '0';
    }
  }

  // دالة لتعيين المبلغ غير المدفوع
  void setUnpaidAmount(double value) {
    unpaidAmountController.text = formatNumberWithCommas(value);

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين إجمالي التكلفة باليورو
  void setTotalCostEUR(double value) {
    totalCostEURController.text = value.toStringAsFixed(2);

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتعيين المبلغ غير المدفوع باليورو
  void setUnpaidEUR(double value) {
    unpaidEURController.text = value.toStringAsFixed(2);

    // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // دالة لتحديث تكلفة البريد الفرعية
  void updatePostSubCost(double value) {
    if (value <= 0) {
      _dataLogger
          .warning('محاولة تحديث Post Sub Cost بقيمة صفر أو سالبة: $value');
    }

    _dataLogger.info('تحديث تكلفة البريد الفرعية: $value');
    debugPrint('\n***** تحديث Post Sub Cost في CostInfo *****');
    debugPrint('القيمة الجديدة المراد تعيينها: $value');

    // الحصول على القيمة الحالية
    String currentValueStr = postSubCostController.text;
    double currentValue =
        double.tryParse(currentValueStr.replaceAll(',', '')) ?? 0;

    // طباعة تشخيصية للقيمة الحالية
    debugPrint('القيمة الحالية في حقل Post Sub Cost: $currentValue');

    // تحديث فقط إذا تغيرت القيمة
    if (value != currentValue) {
      // تنسيق القيمة بالفواصل
      final formattedValue = formatNumberWithCommas(value);

      // تعيين القيمة مباشرة في حقل النص
      setState(() {
        postSubCostController.text = formattedValue;
        debugPrint(
            'تم تعيين قيمة Post Sub Cost في setState إلى: $formattedValue');
      });

      _dataLogger.info('تم تحديث متحكم Post Sub Cost إلى: $formattedValue');
      debugPrint('تم تحديث Post Sub Cost من $currentValue إلى $value');

      // التحقق من القيمة بعد التعيين
      debugPrint(
          'النص الحالي في حقل Post Sub Cost بعد التعيين المباشر: ${postSubCostController.text}');

      // إعادة حساب المجاميع
      calculateTotals();

      // استخدام معالجة أخرى من خلال تأخير لضمان تحديث واجهة المستخدم
      Future.delayed(const Duration(milliseconds: 50), () {
        if (mounted) {
          setState(() {
            // التحقق من القيمة بعد التحديث المتأخر
            try {
              double updatedValue = double.tryParse(
                      postSubCostController.text.replaceAll(',', '')) ??
                  0;
              debugPrint(
                  'القيمة في حقل Post Sub Cost بعد التحديث المتأخر: $updatedValue');
              debugPrint('تم تحديث واجهة المستخدم بنجاح.');
            } catch (e) {
              debugPrint('خطأ في قراءة القيمة بعد التحديث المتأخر: $e');
            }
          });
        } else {
          debugPrint('Widget غير مُثبت في الشجرة، لا يمكن تحديث الواجهة');
        }
      });
    } else {
      _dataLogger
          .info('لم يتم تحديث Post Sub Cost لأن القيمة لم تتغير: $value');
      debugPrint('لم يتم تحديث Post Sub Cost لأن القيمة لم تتغير: $value');
    }
    debugPrint('***** انتهاء تحديث Post Sub Cost *****\n');
  }

  // دالة للحصول على نص Post Sub Cost
  String getPostSubCostText() {
    return postSubCostController.text;
  }

  // دالة لتحديث قيمة Box packing cost بناءً على عدد الصناديق
  void updateBoxPackingCost(int boxNo) {
    try {
      // حساب تكلفة التعبئة: 3000 لكل صندوق
      final boxPackingCost = boxNo * 3000;
      boxPackingCostController.text =
          formatNumberWithCommas(boxPackingCost.toDouble());
      calculateTotals();

      // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      // في حالة حدوث خطأ، نضع قيمة صفر
      boxPackingCostController.text = formatNumberWithCommas(0);
    }
  }

  // دالة لإعادة تعيين جميع الحقول
  void resetFields() {
    try {
      debugPrint('إعادة تعيين حقول CostInfo...');

      // إعادة تعيين حالة خيار التأمين
      setUseInsurance(false);

      // إعادة تعيين حالة خيار تحويل المبلغ غير المدفوع إلى المدفوع
      setTransferUnpaidToPaid(false);

      // إعادة تعيين قيم الحقول
      // نترك قيمة insurance_percent كما هي (6)
      goodsValueController.text = '0';
      insuranceAmountController.text = '0';
      exportDocController.text = '0';
      boxPackingCostController.text = '0';
      doorToDoorCostController.text = '0';
      postSubCostController.text = '0';
      discountAmountController.text = '0';
      totalPostCostController.text = '0';
      totalPaidController.text = '0';
      unpaidAmountController.text = '0';
      totalCostEURController.text = '0.00';
      unpaidEURController.text = '0.00';

      // إعادة حساب القيم
      calculateInsuranceAmount();
      calculateTotals();

      _dataLogger.info('تم إعادة تعيين حقول CostInfo');
    } catch (e) {
      _dataLogger.warning('خطأ في إعادة تعيين حقول CostInfo: $e');
    }
  }
}
