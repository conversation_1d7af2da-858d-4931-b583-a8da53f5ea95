import 'package:flutter/foundation.dart';
import 'code_data_service.dart';

/// خدمة لتحميل البيانات مسبقًا وتخزينها في الذاكرة
class DataPreloadService {
  static final DataPreloadService _instance = DataPreloadService._internal();
  factory DataPreloadService() => _instance;
  DataPreloadService._internal();

  final CodeDataService _codeDataService = CodeDataService();

  // قوائم الدول والمدن المخزنة في الذاكرة
  List<String> _countries = [];
  Map<String, List<String>> _citiesByCountry = {};
  Map<String, Map<String, dynamic>> _pricesByCountryAndCity = {};
  bool _isDataLoaded = false;

  // الحصول على قائمة الدول
  List<String> get countries => _countries;

  // الحصول على قائمة المدن حسب الدولة
  Map<String, List<String>> get citiesByCountry => _citiesByCountry;

  // الحصول على الأسعار حسب الدولة والمدينة
  Map<String, Map<String, dynamic>> get pricesByCountryAndCity =>
      _pricesByCountryAndCity;

  // التحقق مما إذا كانت البيانات محملة
  bool get isDataLoaded => _isDataLoaded;

  /// إعادة تحميل البيانات (يستخدم عند إضافة أو تحديث البيانات)
  Future<void> refreshData() async {
    debugPrint('=== بدء إعادة تحميل البيانات في DataPreloadService ===');

    try {
      // إعادة تعيين حالة تحميل البيانات
      _isDataLoaded = false;
      _resetDataCache();
      debugPrint('تم إعادة تعيين ذاكرة التخزين المؤقت');

      // إعادة تحميل البيانات
      await preloadData();

      debugPrint('=== تم إعادة تحميل البيانات بنجاح ===');
      debugPrint('   - عدد الدول: ${_countries.length}');
      debugPrint(
          '   - عدد المدن: ${_citiesByCountry.values.fold(0, (sum, cities) => sum + cities.length)}');
      debugPrint('   - عدد الأسعار: ${_pricesByCountryAndCity.length}');
    } catch (e) {
      debugPrint('خطأ في إعادة تحميل البيانات: $e');
      // في حالة الخطأ، تأكد من إعادة تعيين الحالة
      _isDataLoaded = false;
      _resetDataCache();
      rethrow; // إعادة طرح الخطأ للتعامل معه في الأعلى
    }
  }

  /// تحميل البيانات مسبقًا
  Future<void> preloadData() async {
    if (_isDataLoaded) {
      debugPrint('البيانات محملة بالفعل، تخطي إعادة التحميل');
      return;
    }

    debugPrint('=== بدء تحميل البيانات مسبقاً ===');

    try {
      // التحقق من بيانات الدول والمدن في قاعدة البيانات
      await _codeDataService.verifyCountriesAndCitiesData();

      // تحميل الدول من قاعدة البيانات
      await _loadCountriesFromDatabase();

      // تحميل المدن من قاعدة البيانات
      await _loadCitiesFromDatabase();

      // تحميل الأسعار من قاعدة البيانات
      await _loadPricesFromDatabase();

      _isDataLoaded = true;
      debugPrint('=== تم تحميل البيانات مسبقاً بنجاح ===');
      debugPrint('   - عدد الدول: ${_countries.length}');
      debugPrint(
          '   - عدد المدن: ${_citiesByCountry.values.fold(0, (sum, cities) => sum + cities.length)}');
      debugPrint('   - عدد الأسعار: ${_pricesByCountryAndCity.length}');
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات مسبقاً: $e');
      // إعادة تعيين ذاكرة التخزين المؤقت في حالة حدوث خطأ
      _resetDataCache();
    }
  }

  /// تحميل الدول من قاعدة البيانات
  Future<void> _loadCountriesFromDatabase() async {
    try {
      // استخدام الدول المفعلة فقط للمستخدم العادي
      final countriesData = await _codeDataService.getEnabledCountries();

      if (countriesData.isEmpty) {
        debugPrint(
            'لا توجد دول مفعلة في قاعدة البيانات، سيتم إعادة تعيين ذاكرة التخزين المؤقت');
        _resetDataCache();
        return;
      }

      final List<String> countries = [];
      for (var country in countriesData) {
        if (country['country'] != null &&
            country['country'].toString().isNotEmpty) {
          countries.add(country['country'].toString());
        }
      }

      if (countries.isEmpty) {
        debugPrint(
            'لا توجد دول صالحة في قاعدة البيانات، سيتم إعادة تعيين ذاكرة التخزين المؤقت');
        _resetDataCache();
        return;
      }

      _countries = countries;
      debugPrint('عدد الدول الموجودة في قاعدة البيانات: ${_countries.length}');
      // ?? ??? ????? debugPrint
    } catch (e) {
      debugPrint('خطأ في تحميل الدول من قاعدة البيانات: $e');
      // ?? ??? ????? debugPrint
      _resetDataCache();
    }
  }

  /// تحميل المدن من قاعدة البيانات
  Future<void> _loadCitiesFromDatabase() async {
    try {
      final citiesData = await _codeDataService.getAllUniqueCities();

      if (citiesData.isEmpty) {
        debugPrint(
            'لا توجد مدن في قاعدة البيانات، سيتم إعادة تعيين ذاكرة التخزين المؤقت');
        _resetCitiesCache();
        return;
      }

      final Map<String, List<String>> citiesByCountry = {};

      for (var city in citiesData) {
        if (city['city'] != null &&
            city['country'] != null &&
            city['city'].toString().isNotEmpty &&
            city['country'].toString().isNotEmpty) {
          final cityName = city['city'].toString();
          final countryName = city['country'].toString();

          // تحميل المدن من الدول المفعلة فقط
          if (_countries.contains(countryName)) {
            if (!citiesByCountry.containsKey(countryName)) {
              citiesByCountry[countryName] = [];
            }

            if (!citiesByCountry[countryName]!.contains(cityName)) {
              citiesByCountry[countryName]!.add(cityName);
            }
          }
        }
      }

      if (citiesByCountry.isEmpty) {
        debugPrint(
            'لا توجد مدن صالحة في قاعدة البيانات، سيتم إعادة تعيين ذاكرة التخزين المؤقت');
        _resetCitiesCache();
        return;
      }

      _citiesByCountry = citiesByCountry;
      debugPrint(
          'تم تحميل المدن لـ ${_citiesByCountry.length} دولة من قاعدة البيانات');
    } catch (e) {
      debugPrint('خطأ في تحميل المدن من قاعدة البيانات: $e');
      // ?? ??? ????? debugPrint
      _resetCitiesCache();
    }
  }

  /// تحميل الأسعار من قاعدة البيانات
  Future<void> _loadPricesFromDatabase() async {
    try {
      final pricesData = await _codeDataService.getAllUniquePrices();

      if (pricesData.isEmpty) {
        // ?? ??? ????? debugPrint
        _resetPricesCache();
        return;
      }

      final Map<String, Map<String, dynamic>> pricesByCountryAndCity = {};

      for (var price in pricesData) {
        if (price['country'] != null &&
            price['country'].toString().isNotEmpty) {
          final countryName = price['country'].toString();
          final cityName = price['city']?.toString() ?? '';
          final key =
              cityName.isNotEmpty ? '${countryName}_$cityName' : countryName;

          pricesByCountryAndCity[key] = {
            'price_door_to_door': price['price_door_to_door'] ?? 0.0,
            'for_each_1_kg': price['for_each_1_kg'] ?? 0.0,
            'minimum_price': price['minimum_price'] ?? 0.0,
          };
        }
      }

      if (pricesByCountryAndCity.isEmpty) {
        // ?? ??? ????? debugPrint
        _resetPricesCache();
        return;
      }

      _pricesByCountryAndCity = pricesByCountryAndCity;
      debugPrint(
          'تم تحميل الأسعار لـ ${_pricesByCountryAndCity.length} دولة/مدينة من قاعدة البيانات');
    } catch (e) {
      // ?? ??? ????? debugPrint
      _resetPricesCache();
    }
  }

  /// إعادة تعيين ذاكرة التخزين المؤقت للبيانات
  void _resetDataCache() {
    _countries = [];
    _resetCitiesCache();
    _resetPricesCache();
    // ?? ??? ????? debugPrint
  }

  /// إعادة تعيين ذاكرة التخزين المؤقت للأسعار
  void _resetPricesCache() {
    _pricesByCountryAndCity = {};
    // ?? ??? ????? debugPrint
  }

  /// إعادة تعيين ذاكرة التخزين المؤقت للمدن
  void _resetCitiesCache() {
    _citiesByCountry = {};
    // ?? ??? ????? debugPrint
  }

  // الحصول على الأسعار حسب الدولة والمدينة
  Map<String, dynamic>? getPricesByCountryAndCity(String country, String city) {
    try {
      final key = city.isNotEmpty ? '${country}_$city' : country;
      return _pricesByCountryAndCity[key];
    } catch (e) {
      // ?? ??? ????? debugPrint
      return null;
    }
  }
}
