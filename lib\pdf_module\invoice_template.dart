import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import '../utils/invoice_language_helper.dart';

/// فئة مسؤولة عن إنشاء تصميم الفاتورة فقط
class InvoiceTemplate {
  // ===== متغيرات التحكم في أبعاد الجداول =====

  // أبعاد الجداول الرئيسية
  static const double tableWidth = 270;

  // ارتفاعات الجداول الثابتة
  static const double senderReceiverTableHeight = 230;
  static const double costTableHeight = 230;
  static const double agentInfoTableHeight = 80;
  static const double goodsTableHeight = 80; // ارتفاع جدول البضاعة الكامل
  static const double goodsTableRowHeight = 55; // ارتفاع صف البيانات فقط

  // الهوامش والمسافات
  static const double tablePaddingVertical = 2.2; // للمرسل والمستلم
  static const double costTablePaddingVertical = 2.5; // لجدول التكلفة
  static const double tableSpacing = 10; // المسافة بين الجداول
  static const double sectionSpacing = 8; // المسافة بين الأقسام

  // أبعاد QR Code
  static const double qrCodeSize = 60;
  static const double qrImageSize = 58;

  // أبعاد منطقة النص في معلومات الوكيل
  static const double agentInfoTextWidth = 120;

  // هوامش المستند
  static const double documentMarginTop = 20; // للأعلى
  static const double documentMarginBottom = 5; // للأسفل
  static const double documentMarginLeft = 30; // لليسار
  static const double documentMarginRight = 30; // لليمين

  // أبعاد مربع الملاحظات
  static const double notesBoxWidth = 200; // تقليل من 300 إلى 200
  static const double notesBoxHeight = 30; // تقليل من 40 إلى 30

  /// إنشاء فاتورة بتصميم خاص
  static Future<Uint8List> generate({
    required String companyName,
    required String branchName,
    required String branchAddress,
    required String branchPhone,
    required String codeNo,
    required String truckNo,
    required Map<String, dynamic> codeData,
    String? language, // إضافة معامل اللغة
  }) async {
    // الحصول على اللغة المحفوظة إذا لم يتم تمريرها
    final invoiceLanguage =
        language ?? await InvoiceLanguageHelper.getSavedLanguage();
    // تحميل الخطوط العربية
    final Map<String, pw.Font> arabicFonts = await _loadArabicFonts();

    // تحميل الصور
    final euknetLogo = await rootBundle.load('assets/images/EUKnet_Logo.png');
    final stersLogo = await rootBundle.load('assets/images/Sters_Logo.png');
    final qrImage = await rootBundle.load('assets/images/QR.png');
    final notAcceptImage =
        await rootBundle.load('assets/images/not_accept.png');

    // الحصول على التاريخ الحالي
    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(now);

    // إنشاء المستند
    final pdf = pw.Document(
      theme: pw.ThemeData.withFont(
        base: arabicFonts['regular']!,
        bold: arabicFonts['bold']!,
      ),
    );

    // تحديد اتجاه النص - جميع اللغات من اليمين لليسار
    final textDirection = pw.TextDirection.rtl;

    // إضافة صفحة للمستند
    pdf.addPage(
      pw.Page(
        textDirection: textDirection,
        pageFormat: PdfPageFormat.a4.copyWith(
          marginTop: documentMarginTop,
          marginBottom: documentMarginBottom,
          marginLeft: documentMarginLeft,
          marginRight: documentMarginRight,
        ),
        build: (pw.Context context) {
          return pw.Column(
            children: [
              // عنوان الشركة مع الشعارات في أعلى الصفحة
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.only(bottom: 2),
                decoration: const pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide(width: 0.7, color: PdfColors.black),
                  ),
                ),
                child: pw.Column(
                  children: [
                    // صف الشعارات وعنوان الشركة
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        // شعار Sters على اليسار
                        pw.Image(
                          pw.MemoryImage(stersLogo.buffer.asUint8List()),
                          width: 100,
                          height: 50,
                        ),

                        // عنوان الشركة في الوسط
                        pw.Column(
                          children: [
                            pw.Text(
                              InvoiceTranslations.translate(
                                  'company_name', invoiceLanguage),
                              style: pw.TextStyle(
                                font: arabicFonts['bold'],
                                fontSize: 16,
                                color: PdfColors.blue800,
                              ),
                              textAlign: pw.TextAlign.center,
                              textDirection: textDirection,
                            ),
                            pw.SizedBox(height: 1),
                            pw.Text(
                              _getCompanySubtitle(invoiceLanguage),
                              style: pw.TextStyle(
                                font: arabicFonts['bold'],
                                fontSize: 10,
                                color: PdfColors.indigo900,
                              ),
                              textAlign: pw.TextAlign.center,
                              textDirection: textDirection,
                            ),
                          ],
                        ),

                        // شعار EUKnet على اليمين
                        pw.Image(
                          pw.MemoryImage(euknetLogo.buffer.asUint8List()),
                          width: 100,
                          height: 50,
                        ),
                      ],
                    ),

                    pw.SizedBox(height: 3),

                    // معلومات الفرع
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Text(
                          '${InvoiceTranslations.translate('branch', invoiceLanguage)}: $branchName',
                          style: pw.TextStyle(
                            font: arabicFonts['bold'],
                            fontSize: 8,
                            color: PdfColors.black,
                          ),
                          textDirection: textDirection,
                        ),
                        pw.SizedBox(width: 15),
                        pw.Text(
                          '${InvoiceTranslations.translate('address', invoiceLanguage)}: $branchAddress',
                          style: pw.TextStyle(
                            font: arabicFonts['bold'],
                            fontSize: 8,
                            color: PdfColors.black,
                          ),
                          textDirection: textDirection,
                        ),
                        pw.SizedBox(width: 15),
                        pw.Text(
                          '${InvoiceTranslations.translate('phone', invoiceLanguage)}: $branchPhone',
                          style: pw.TextStyle(
                            font: arabicFonts['regular'],
                            fontSize: 8,
                            color: PdfColors.black,
                          ),
                          textDirection: textDirection,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // قسم رقم الكود والتاريخ
              _buildCodeAndDateSection(formattedDate, codeNo, truckNo,
                  arabicFonts, invoiceLanguage, textDirection),

              pw.SizedBox(height: 8),

              // قسم الجداول المتجاورة الأولى
              pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Expanded(
                    flex: 1,
                    child: _buildSenderReceiverTable(
                        codeData, arabicFonts, invoiceLanguage, textDirection),
                  ),

                  pw.SizedBox(width: tableSpacing),

                  // الجدول الثاني (معلومات التكلفة) - يأخذ 50% من العرض
                  pw.Expanded(
                    flex: 1,
                    child: _buildCostTable(
                        codeData, arabicFonts, invoiceLanguage, textDirection),
                  ),
                ],
              ),

              pw.SizedBox(height: sectionSpacing),

              // قسم الجداول المتجاورة الثانية
              pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // الجدول الثالث (معلومات البضاعة) - يأخذ 60% من العرض
                  pw.Expanded(
                    flex: 6,
                    child: _buildGoodsDetailsTable(
                        codeData, arabicFonts, invoiceLanguage, textDirection),
                  ),

                  pw.SizedBox(width: tableSpacing),

                  // الجدول الرابع (معلومات الوكيل) - يأخذ 40% من العرض
                  pw.Expanded(
                    flex: 4,
                    child: _buildAgentInfoTable(codeData, arabicFonts, qrImage,
                        invoiceLanguage, textDirection),
                  ),
                ],
              ),

              pw.SizedBox(height: 8),

              // مربع الشروط بعرض الشاشة ويمتد حتى الفوتر
              pw.Expanded(
                child: pw.Container(
                  width: double.infinity, // عرض الشاشة كاملة
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 0.7),
                    borderRadius: pw.BorderRadius.circular(1),
                  ),
                  child: pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // قسم الشروط على اليمين
                        pw.Expanded(
                          flex: 75,
                          child: pw.Expanded(
                            child: pw.Table(
                              columnWidths: {
                                0: const pw.FlexColumnWidth(1),
                              },
                              children: [
                                // البند 1
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 1',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getMainTermFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'term_1',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize: _getMainTermFontSize(
                                                    invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 2
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 2 ',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getMainTermFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'term_2',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize: _getMainTermFontSize(
                                                    invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 3
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 3 ',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getTermTitleFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'term_3',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize:
                                                    _getTermContentFontSize(
                                                        invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 4
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.Column(
                                        crossAxisAlignment:
                                            pw.CrossAxisAlignment.start,
                                        children: [
                                          pw.RichText(
                                            textDirection: pw.TextDirection.rtl,
                                            textAlign: pw.TextAlign.justify,
                                            text: pw.TextSpan(
                                              text: ' - 4 ',
                                              style: pw.TextStyle(
                                                font: arabicFonts['bold'],
                                                fontSize: _getTermTitleFontSize(
                                                    invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                              children: [
                                                pw.TextSpan(
                                                  text: InvoiceTranslations
                                                      .translate('term_4_intro',
                                                          invoiceLanguage),
                                                  style: pw.TextStyle(
                                                    font:
                                                        arabicFonts['regular'],
                                                    fontSize:
                                                        _getTermContentFontSize(
                                                            invoiceLanguage),
                                                    color: PdfColors.black,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          pw.SizedBox(height: 2),
                                          pw.Padding(
                                            padding: const pw.EdgeInsets.only(
                                                right: 15),
                                            child: pw.RichText(
                                              textDirection:
                                                  pw.TextDirection.rtl,
                                              textAlign: pw.TextAlign.justify,
                                              text: pw.TextSpan(
                                                text: 'أ - ',
                                                style: pw.TextStyle(
                                                  font: arabicFonts['bold'],
                                                  fontSize:
                                                      _getTermContentFontSize(
                                                          invoiceLanguage),
                                                  color: PdfColors.black,
                                                ),
                                                children: [
                                                  pw.TextSpan(
                                                    text: InvoiceTranslations
                                                        .translate('term_4a',
                                                            invoiceLanguage),
                                                    style: pw.TextStyle(
                                                      font: arabicFonts[
                                                          'regular'],
                                                      fontSize:
                                                          _getTermContentFontSize(
                                                              invoiceLanguage),
                                                      color: PdfColors.black,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          pw.SizedBox(height: 2),
                                          pw.Padding(
                                            padding: const pw.EdgeInsets.only(
                                                right: 15),
                                            child: pw.RichText(
                                              textDirection:
                                                  pw.TextDirection.rtl,
                                              textAlign: pw.TextAlign.justify,
                                              text: pw.TextSpan(
                                                text: 'ب - ',
                                                style: pw.TextStyle(
                                                  font: arabicFonts['bold'],
                                                  fontSize:
                                                      _getTermContentFontSize(
                                                          invoiceLanguage),
                                                  color: PdfColors.black,
                                                ),
                                                children: [
                                                  pw.TextSpan(
                                                    text: InvoiceTranslations
                                                        .translate(
                                                            'term_4b_part1',
                                                            invoiceLanguage),
                                                    style: pw.TextStyle(
                                                      font: arabicFonts[
                                                          'regular'],
                                                      fontSize:
                                                          _getTermContentFontSize(
                                                              invoiceLanguage),
                                                      color: PdfColors.black,
                                                    ),
                                                  ),
                                                  pw.TextSpan(
                                                    text: InvoiceTranslations
                                                        .translate(
                                                            'term_4b_part2',
                                                            invoiceLanguage),
                                                    style: pw.TextStyle(
                                                      font: arabicFonts['bold'],
                                                      fontSize:
                                                          _getTermContentFontSize(
                                                              invoiceLanguage),
                                                      color: PdfColors.red900,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 5
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 5 ',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getTermContentFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'term_5',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize:
                                                    _getTermContentFontSize(
                                                        invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 6
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 6 ',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getTermContentFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'term_6',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize:
                                                    _getTermContentFontSize(
                                                        invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 7
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 7 ',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getTermContentFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'term_7',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize:
                                                    _getTermContentFontSize(
                                                        invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // البند 8
                                pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding:
                                          const pw.EdgeInsets.only(bottom: 4),
                                      child: pw.RichText(
                                        textDirection: pw.TextDirection.rtl,
                                        textAlign: pw.TextAlign.justify,
                                        text: pw.TextSpan(
                                          text: ' - 8 ',
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: _getTermContentFontSize(
                                                invoiceLanguage),
                                            color: PdfColors.black,
                                          ),
                                          children: [
                                            pw.TextSpan(
                                              text:
                                                  InvoiceTranslations.translate(
                                                      'signature_agreement',
                                                      invoiceLanguage),
                                              style: pw.TextStyle(
                                                font: arabicFonts['regular'],
                                                fontSize:
                                                    _getTermContentFontSize(
                                                        invoiceLanguage),
                                                color: PdfColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        pw.SizedBox(width: 10),
                        // قسم المواد الممنوعة على اليسار (بدون إطار داخلي)
                        pw.Expanded(
                          flex: 25,
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.center,
                            children: [
                              pw.Text(
                                InvoiceTranslations.translate(
                                    'prohibited_items', invoiceLanguage),
                                style: pw.TextStyle(
                                  font: arabicFonts['bold'],
                                  fontSize: 16,
                                  color: PdfColors.red,
                                ),
                                textAlign: pw.TextAlign.center,
                                textDirection: textDirection,
                              ),
                              pw.SizedBox(height: 8),
                              pw.Container(
                                height: 150,
                                width: 180,
                                child: pw.Image(
                                  pw.MemoryImage(
                                      notAcceptImage.buffer.asUint8List()),
                                  fit: pw.BoxFit.contain,
                                ),
                              ),
                              pw.SizedBox(height: 10),
                              // خط أفقي فوق التوقيع
                              pw.Container(
                                width: 120,
                                height: 1,
                                color: PdfColors.black,
                              ),
                              pw.SizedBox(height: 10),
                              pw.Expanded(
                                child: pw.Container(
                                  width: 120,
                                  decoration: pw.BoxDecoration(
                                    border: pw.Border.all(
                                        color: PdfColors.black, width: 0.7),
                                    borderRadius: pw.BorderRadius.circular(4),
                                  ),
                                  child: pw.Stack(
                                    alignment: pw.Alignment.center,
                                    children: [
                                      pw.Opacity(
                                        opacity: 0.07, // شفافية أعلى
                                        child: pw.Image(
                                          pw.MemoryImage(
                                              stersLogo.buffer.asUint8List()),
                                          fit: pw.BoxFit.contain,
                                          width: 100,
                                        ),
                                      ),
                                      // إضافة نص "توقيع المرسل" في أسفل المربع
                                      pw.Positioned(
                                        bottom: 5,
                                        child: pw.Text(
                                          InvoiceTranslations.translate(
                                              'sender_signature',
                                              invoiceLanguage),
                                          style: pw.TextStyle(
                                            font: arabicFonts['bold'],
                                            fontSize: 11,
                                            color: PdfColors.black,
                                          ),
                                          textDirection: textDirection,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // إضافة الفوتر
              _buildFooter(formattedDate, arabicFonts, codeData,
                  invoiceLanguage, textDirection),
            ],
          );
        },
      ),
    );

    // حفظ المستند
    return pdf.save();
  }

  /// تحميل الخطوط العربية
  static Future<Map<String, pw.Font>> _loadArabicFonts() async {
    final Map<String, pw.Font> fonts = {};

    try {
      // تحميل خط Arial العادي
      final regularFontData = await rootBundle.load('assets/fonts/arial.ttf');
      fonts['regular'] = pw.Font.ttf(regularFontData);

      // تحميل خط Arial الغامق
      final boldFontData = await rootBundle.load('assets/fonts/arial_bold.ttf');
      fonts['bold'] = pw.Font.ttf(boldFontData);

      // تعيين الخطوط الإضافية
      fonts['medium'] = fonts['regular']!;
      fonts['cairo'] = fonts['bold']!;
    } catch (e) {
      debugPrint('خطأ في تحميل الخطوط: $e');
      // في حالة حدوث خطأ، استخدم الخط الافتراضي
      fonts['regular'] = pw.Font.helvetica();
      fonts['bold'] = pw.Font.helveticaBold();
      fonts['medium'] = pw.Font.helvetica();
      fonts['cairo'] = pw.Font.helveticaBold();
    }

    return fonts;
  }

  /// إنشاء قسم رقم الكود والتاريخ
  static pw.Widget _buildCodeAndDateSection(
      String date,
      String codeNo,
      String truckNo,
      Map<String, pw.Font> fonts,
      String language,
      pw.TextDirection textDirection) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: pw.Column(
        children: [
          // خط أفقي 1 - تم حذفه

          pw.SizedBox(height: 5),

          // قسم رقم الكود والتاريخ
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              // التاريخ بدون مستطيل
              pw.Row(
                children: [
                  pw.Text(
                    '${InvoiceTranslations.translate('date', language)}:',
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 10,
                      color: PdfColors.black,
                    ),
                    textDirection: textDirection,
                  ),
                  pw.SizedBox(width: 5),
                  pw.Text(
                    date,
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 10,
                      color: PdfColors.black,
                    ),
                  ),
                ],
              ),

              // رقم الكود ورقم الشاحنة بمستطيل أكبر وخلفية برتقالية
              pw.Row(
                children: [
                  pw.Text(
                    '${InvoiceTranslations.translate('code_no', language)}:',
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 12,
                      color: PdfColors.black,
                    ),
                    textDirection: textDirection,
                  ),
                  pw.SizedBox(width: 8),
                  pw.Container(
                    width: 300,
                    height: 35,
                    decoration: pw.BoxDecoration(
                      color: PdfColors.orange200,
                      border: pw.Border.all(color: PdfColors.orange),
                      borderRadius: pw.BorderRadius.circular(4),
                    ),
                    child: pw.Center(
                      child: pw.Text(
                        '$codeNo - $truckNo',
                        style: pw.TextStyle(
                          font: fonts['bold'],
                          fontSize: 24,
                          color: PdfColors.black,
                        ),
                      ),
                    ),
                  ),
                  pw.SizedBox(width: 8),
                  pw.Text(
                    'Code No.',
                    style: pw.TextStyle(
                      font: fonts['bold'],
                      fontSize: 12,
                      color: PdfColors.black,
                    ),
                  ),
                ],
              ),
            ],
          ),

          pw.SizedBox(height: 5),
          // خط أفقي 2
          pw.Container(
            height: 0.7,
            color: PdfColors.black,
          ),
        ],
      ),
    );
  }

  /// بناء جدول معلومات المرسل والمستلم
  static pw.Widget _buildSenderReceiverTable(
      Map<String, dynamic> codeData,
      Map<String, pw.Font> fonts,
      String language,
      pw.TextDirection textDirection) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان معلومات المرسل والمستلم
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Text(
            _getSenderReceiverTitle(language),
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 11,
              color: PdfColors.blue800,
            ),
            textAlign: pw.TextAlign.center,
            textDirection: textDirection,
          ),
        ),

        pw.SizedBox(height: 3),

        // جدول المعلومات
        pw.Container(
          width: tableWidth,
          height: senderReceiverTableHeight,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 1.0),
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Table(
            border: pw.TableBorder.all(
              color: PdfColors.black,
              width: 1.0,
            ),
            columnWidths: {
              0: const pw.FlexColumnWidth(3), // عمود القيم (على اليسار)
              1: const pw.FlexColumnWidth(2), // عمود العناوين (على اليمين)
            },
            defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
            children: [
              // اسم المرسل
              _buildTableRow(
                  '${InvoiceTranslations.translate('sender_name', language)}:',
                  codeData['sender_name'] ?? '',
                  fonts),

              // رقم المرسل
              _buildTableRow(
                  '${InvoiceTranslations.translate('sender_phone', language)}:',
                  codeData['sender_phone'] ?? '',
                  fonts),

              // اسم المستلم
              _buildTableRow(
                  '${InvoiceTranslations.translate('receiver_name', language)}:',
                  codeData['receiver_name'] ?? '',
                  fonts),

              // رقم المستلم
              _buildReceiverPhoneRow(
                  '${InvoiceTranslations.translate('receiver_phone', language)}:',
                  codeData['receiver_phone'] ?? '',
                  fonts),

              // عنوان المستلم
              _buildTableRow(
                  '${InvoiceTranslations.translate('receiver_address', language)}:',
                  codeData['street_name_no'] ?? '',
                  fonts,
                  codeData: codeData),

              // اسم المدينة
              _buildTableRow(
                  '${InvoiceTranslations.translate('city_name', language)}:',
                  _getCityValue(codeData),
                  fonts),

              // الرمز البريدي
              _buildTableRow(
                  '${InvoiceTranslations.translate('postal_code', language)}:',
                  codeData['postal_code'] ?? '',
                  fonts),

              // الدولة
              _buildTableRow(
                  '${InvoiceTranslations.translate('country', language)}:',
                  codeData['country'] ?? '',
                  fonts),

              // هل تم تأمين البضاعة؟
              _buildInsuranceRow(
                  InvoiceTranslations.translate('insurance_question', language),
                  fonts,
                  codeData['insurance_cost'],
                  codeData,
                  language),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء جدول معلومات التكلفة
  static pw.Widget _buildCostTable(
      Map<String, dynamic> codeData,
      Map<String, pw.Font> fonts,
      String language,
      pw.TextDirection textDirection) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان تفاصيل تكلفة النقل
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Text(
            InvoiceTranslations.translate('cost_details', language),
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 11,
              color: PdfColors.blue800,
            ),
            textAlign: pw.TextAlign.center,
            textDirection: textDirection,
          ),
        ),

        pw.SizedBox(height: 3),

        // جدول التكاليف
        pw.Container(
          width: tableWidth,
          height: costTableHeight,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 1.0),
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Table(
            border: pw.TableBorder.all(
              color: PdfColors.black,
              width: 1.0,
            ),
            columnWidths: {
              0: const pw.FlexColumnWidth(1), // عمود العملة (أقصى اليسار)
              1: const pw.FlexColumnWidth(2), // عمود القيم (في الوسط)
              2: const pw.FlexColumnWidth(3), // عمود العناوين (على اليمين)
            },
            defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
            children: [
              // كلفة النقل
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('transport_cost', language)}:',
                  codeData['post_sub_cost']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // كلفة الكارتون الفارغ
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('box_packing_cost', language)}:',
                  codeData['box_packing_cost']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // الكمرك والاداريات
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('customs_admin', language)}:',
                  codeData['export_doc']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // النقل الداخلي الى عنوان المستلم
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('door_to_door', language)}:',
                  codeData['door_to_door_cost']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // كلفة التأمين
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('insurance_cost', language)}:',
                  codeData['insurance_amount']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // الكلفة الكلية
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('total_cost', language)}:',
                  codeData['total_post_cost']?.toString() ?? '0',
                  fonts,
                  isHighlighted: true,
                  codeData: codeData),

              // المبلغ المدفوع
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('paid_amount', language)}:',
                  codeData['total_paid']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // المبلغ المطلوب
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('required_amount', language)}:',
                  codeData['unpaid_amount']?.toString() ?? '0',
                  fonts,
                  codeData: codeData),

              // المبلغ المطلوب دفعه في اوروبا
              _buildCostTableRow(
                  '${InvoiceTranslations.translate('required_eur', language)}:',
                  codeData['unpaid_eur']?.toString() ?? '0',
                  fonts,
                  isEUR: true,
                  codeData: codeData),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف في جدول التكاليف
  static pw.TableRow _buildCostTableRow(
      String title, String value, Map<String, pw.Font> fonts,
      {bool isHighlighted = false,
      bool isEUR = false,
      Map<String, dynamic>? codeData}) {
    // تحديد لون الخلفية بناءً على القيم المطلوبة
    PdfColor bgColor = _getBackgroundColorForCostField(title, value, codeData);

    // إذا كان isHighlighted = true، استخدم اللون البرتقالي الفاتح
    if (isHighlighted) {
      bgColor = PdfColors.orange100;
    }

    // إذا كان اليورو، استخدم خلفية حمراء
    if (isEUR) {
      bgColor = PdfColors.red200;
    }

    final titleFont = isHighlighted
        ? fonts['cairo']
        : fonts['bold']; // استخدام خط Cairo للعناوين
    final valueFont = isHighlighted || isEUR
        ? fonts['bold']
        : fonts[
            'bold']; // تغيير من fonts['regular'] إلى fonts['bold'] لجعل كل القيم غامقة
    // زيادة حجم الخط للقيم لجميع الحالات
    final valueFontSize = isHighlighted || isEUR ? 11.5 : 11.0;

    // معالجة تنسيق الأرقام للمبالغ بالدينار العراقي
    String formattedValue = value;
    if (!isEUR && value.isNotEmpty) {
      try {
        // تحويل النص إلى رقم
        double numValue = double.parse(value);
        // تقريب الرقم لإزالة الكسور
        int intValue = numValue.round();
        // تنسيق الرقم مع إضافة فواصل الآلاف
        formattedValue = _formatNumber(intValue);
      } catch (e) {
        // في حال فشل التحويل، استخدم القيمة الأصلية
        formattedValue = value;
      }
    }

    // تحديد نوع العملة
    String currencyType = isEUR ? 'EUR' : 'IQD';

    return pw.TableRow(
      decoration: pw.BoxDecoration(
        color: bgColor,
      ),
      children: [
        // عمود العملة (أقصى اليسار)
        pw.Padding(
          padding: pw.EdgeInsets.symmetric(
              vertical: costTablePaddingVertical, horizontal: 4),
          child: pw.Text(
            currencyType,
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 10,
              color: PdfColors.black, // لون أسود لجميع العملات
            ),
            textAlign: pw.TextAlign.center,
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        // عمود القيمة (في الوسط)
        pw.Padding(
          // التحكم في الـpadding من المتغيرات الثابتة
          padding: pw.EdgeInsets.symmetric(
              vertical: costTablePaddingVertical, horizontal: 4),
          child: pw.Text(
            formattedValue,
            style: pw.TextStyle(
              font: valueFont,
              fontSize: valueFontSize,
              color: PdfColors.black, // لون أسود لجميع القيم
            ),
            textAlign: pw.TextAlign.center,
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        // عمود العنوان (على اليمين) - بدون وحدة العملة الآن
        pw.Padding(
          // التحكم في الـpadding من المتغيرات الثابتة
          padding: pw.EdgeInsets.symmetric(
              vertical: costTablePaddingVertical, horizontal: 4),
          child: pw.Text(
            title,
            style: pw.TextStyle(
              font: titleFont,
              fontSize: 10.5, // زيادة حجم خط العنوان من 9 إلى 10.5
              color: PdfColors.black,
            ),
            textDirection: pw.TextDirection.rtl,
            textAlign: pw.TextAlign.right,
          ),
        ),
      ],
    );
  }

  /// تحديد لون الخلفية المناسب للحقل بناءً على القيمة والشروط المطلوبة
  static PdfColor _getBackgroundColorForCostField(
      String title, String value, Map<String, dynamic>? codeData) {
    if (codeData == null) return PdfColors.white;

    try {
      double numValue = double.tryParse(value) ?? 0.0;

      // تحديد لون الخلفية بناءً على اسم الحقل وقيمته
      switch (title) {
        case 'المبلغ المدفوع:':
          // إذا كان المبلغ المطلوب = 0، اجعل خلفية المبلغ المدفوع خضراء فاتحة
          double unpaidAmount =
              double.tryParse(codeData['unpaid_amount']?.toString() ?? '0') ??
                  0.0;
          if (unpaidAmount == 0) {
            return PdfColors.green300;
          }
          break;

        case 'المبلغ المطلوب:':
          // إذا كان المبلغ المطلوب > 0، اجعل الخلفية حمراء فاتحة
          if (numValue > 0) {
            return PdfColors.red200;
          }
          break;

        case 'المبلغ المطلوب دفعه في اوروبا:':
          // إذا كان المبلغ المطلوب دفعه في أوروبا > 0، اجعل الخلفية برتقالية فاتحة
          if (numValue > 0) {
            return PdfColors.orange100;
          }
          break;

        case 'النقل الداخلي الى عنوان المستلم:':
          // إذا كان النقل الداخلي > 0، اجعل الخلفية زرقاء فاتحة
          if (numValue > 0) {
            return PdfColors.blue100;
          }
          break;

        case 'كلفة التأمين:':
          // إذا كان insurance_percent > 0، اجعل الخلفية خضراء فاتحة
          double insurancePercent = double.tryParse(
                  codeData['insurance_percent']?.toString() ?? '0') ??
              0.0;
          if (insurancePercent > 0) {
            return PdfColors.green300;
          }
          break;
      }
    } catch (e) {
      debugPrint('خطأ في تحديد لون الخلفية: $e');
    }

    return PdfColors.white;
  }

  /// تنسيق الرقم بإضافة فواصل الآلاف
  static String _formatNumber(int number) {
    // تحويل الرقم إلى نص
    String numString = number.toString();

    // إضافة فواصل الآلاف
    final RegExp reg = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String result =
        numString.replaceAllMapped(reg, (Match match) => '${match[1]},');

    return result;
  }

  /// بناء جدول تفاصيل البضاعة
  static pw.Widget _buildGoodsDetailsTable(
      Map<String, dynamic> codeData,
      Map<String, pw.Font> fonts,
      String language,
      pw.TextDirection textDirection) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان تفاصيل البضاعة
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Text(
            InvoiceTranslations.translate('goods_details', language),
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 11,
              color: PdfColors.blue800,
            ),
            textAlign: pw.TextAlign.center,
            textDirection: textDirection,
          ),
        ),

        pw.SizedBox(height: 3),

        // جدول البضاعة
        pw.Container(
          height: goodsTableHeight,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 1.0),
            borderRadius: pw.BorderRadius.circular(1),
          ),
          child: pw.Column(
            children: [
              // عناوين الأعمدة
              pw.Container(
                decoration: const pw.BoxDecoration(
                  color: PdfColors.grey200,
                  border: pw.Border(
                    bottom: pw.BorderSide(color: PdfColors.black, width: 1.0),
                  ),
                ),
                child: pw.Row(
                  children: [
                    // عمود عدد القطع (على اليمين)
                    pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          InvoiceTranslations.translate('quantity', language),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 11,
                          ),
                          textAlign: pw.TextAlign.center,
                          textDirection: textDirection,
                        ),
                      ),
                    ),

                    // عمود الوزن
                    pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          InvoiceTranslations.translate('weight', language),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 11,
                          ),
                          textAlign: pw.TextAlign.center,
                          textDirection: textDirection,
                        ),
                      ),
                    ),

                    // عمود تفاصيل البضاعة
                    pw.Expanded(
                      flex: 4,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          InvoiceTranslations.translate(
                              'goods_description', language),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 9,
                          ),
                          textAlign: pw.TextAlign.center,
                          textDirection: textDirection,
                        ),
                      ),
                    ),

                    // عمود قيمة البضاعة (على اليسار)
                    pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          _formatGoodsValue(codeData['goods_value']),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 11,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // صف البيانات
              pw.Container(
                height:
                    goodsTableRowHeight, // التحكم في ارتفاع الصف من المتغيرات
                child: pw.Row(
                  children: [
                    // عمود عدد القطع (على اليمين)
                    pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          _buildBoxAndPalletDisplay(codeData),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 10,
                          ),
                          textAlign: pw.TextAlign.center,
                          maxLines: 5,
                          overflow: pw.TextOverflow.clip,
                        ),
                      ),
                    ),

                    // عمود الوزن
                    pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          _buildWeightDisplayWithDimensions(codeData),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 10,
                          ),
                          textAlign: pw.TextAlign.center,
                          maxLines: 3,
                          overflow: pw.TextOverflow.clip,
                        ),
                      ),
                    ),

                    // عمود تفاصيل البضاعة
                    pw.Expanded(
                      flex: 4,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          _truncateGoodsDescription(
                              codeData['goods_description']?.toString() ?? ''),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 9,
                          ),
                          textAlign: pw.TextAlign.center,
                          maxLines: 4,
                          overflow: pw.TextOverflow.clip,
                        ),
                      ),
                    ),

                    // عمود قيمة البضاعة (على اليسار)
                    pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        padding: const pw.EdgeInsets.all(4),
                        alignment: pw.Alignment.center,
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            left: pw.BorderSide(
                                color: PdfColors.black, width: 1.0),
                          ),
                        ),
                        child: pw.Text(
                          _formatGoodsValue(codeData['goods_value']),
                          style: pw.TextStyle(
                            font: fonts['bold'],
                            fontSize: 11,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء جدول معلومات الوكيل
  static pw.Widget _buildAgentInfoTable(
      Map<String, dynamic> codeData,
      Map<String, pw.Font> fonts,
      ByteData qrImage,
      String language,
      pw.TextDirection textDirection) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان معلومات الوكيل
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Text(
            InvoiceTranslations.translate('agent_info', language),
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 11,
              color: PdfColors.blue800,
            ),
            textAlign: pw.TextAlign.center,
            textDirection: textDirection,
          ),
        ),

        pw.SizedBox(height: 1),

        // جدول معلومات الوكيل
        pw.Container(
          height: agentInfoTableHeight,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 1.0),
            borderRadius: pw.BorderRadius.circular(1),
          ),
          child: pw.Padding(
            padding: const pw.EdgeInsets.symmetric(horizontal: 6, vertical: 4),
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                // صورة QR في أقصى اليسار - العمود الأول
                pw.Container(
                  width: qrCodeSize,
                  height: qrCodeSize,
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
                    borderRadius: pw.BorderRadius.circular(0.5),
                  ),
                  padding: pw.EdgeInsets.zero,
                  child: pw.Center(
                    child: pw.Image(
                      pw.MemoryImage(qrImage.buffer.asUint8List()),
                      width: qrImageSize,
                      height: qrImageSize,
                      fit: pw.BoxFit.contain,
                    ),
                  ),
                ),

                // مساحة مرنة لدفع معلومات الوكيل إلى أقصى اليمين
                pw.Expanded(child: pw.SizedBox()),

                // عمود المعلومات في أقصى اليمين - العمود الثاني
                pw.Container(
                  width: agentInfoTextWidth,
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                    children: [
                      // اسم الوكيل
                      pw.Text(
                        codeData['agent_name']?.toString() ?? '',
                        style: pw.TextStyle(
                          font: fonts['bold'],
                          fontSize: 11,
                        ),
                        textDirection: pw.TextDirection.ltr,
                        textAlign: pw.TextAlign.left,
                      ),

                      // أرقام هاتف الوكيل (منفصلة)
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رقم الهاتف الأول
                          if ((codeData['agent_phone1']?.toString() ?? '')
                              .isNotEmpty)
                            pw.Text(
                              codeData['agent_phone1']?.toString() ?? '',
                              style: pw.TextStyle(
                                font: fonts['bold'],
                                fontSize: 11,
                              ),
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                            ),

                          // مسافة صغيرة بين الأرقام
                          if ((codeData['agent_phone1']?.toString() ?? '')
                                  .isNotEmpty &&
                              (codeData['agent_phone2']?.toString() ?? '')
                                  .isNotEmpty)
                            pw.SizedBox(height: 2),

                          // رقم الهاتف الثاني
                          if ((codeData['agent_phone2']?.toString() ?? '')
                              .isNotEmpty)
                            pw.Text(
                              codeData['agent_phone2']?.toString() ?? '',
                              style: pw.TextStyle(
                                font: fonts['bold'],
                                fontSize: 11,
                              ),
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left,
                            ),
                        ],
                      ),

                      // عنوان الوكيل
                      pw.Text(
                        codeData['agent_address']?.toString() ?? '',
                        style: pw.TextStyle(
                          font: fonts['bold'],
                          fontSize: 11,
                        ),
                        textDirection: pw.TextDirection.ltr,
                        textAlign: pw.TextAlign.left,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قسم الفوتر
  static pw.Widget _buildFooter(
      String date,
      Map<String, pw.Font> fonts,
      Map<String, dynamic> codeData,
      String language,
      pw.TextDirection textDirection) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 5),
      // decoration: const pw.BoxDecoration(
      //   border: pw.Border(
      //     top: pw.BorderSide(width: 0.7, color: PdfColors.black),
      //   ),
      // ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // القسم الأيسر - معلومات المكتب (العنوان والمعلومات بجانب بعض)
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                InvoiceTranslations.translate('main_office_europe', language),
                style: pw.TextStyle(
                  font: fonts['bold'],
                  fontSize: 12,
                  color: PdfColors.blue800,
                ),
                textDirection: textDirection,
              ),
              pw.SizedBox(width: 10),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    '<EMAIL>',
                    style: pw.TextStyle(
                      font: fonts['regular'],
                      fontSize: 12,
                      color: PdfColors.black,
                    ),
                  ),
                  pw.Text(
                    'www.euknet.com',
                    style: pw.TextStyle(
                      font: fonts['regular'],
                      fontSize: 12,
                      color: PdfColors.black,
                    ),
                  ),
                ],
              ),
            ],
          ),

          // القسم الأيمن - الملاحظات
          pw.Row(
            children: [
              pw.Text(
                '${InvoiceTranslations.translate('notes', language)}:',
                style: pw.TextStyle(
                  font: fonts['bold'],
                  fontSize: 12,
                  color: PdfColors.black,
                ),
                textDirection: textDirection,
              ),
              pw.SizedBox(width: 10),
              pw.Container(
                width: notesBoxWidth,
                height: notesBoxHeight,
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.black),
                ),
                padding: const pw.EdgeInsets.all(5),
                child: pw.Text(
                  codeData['notes']?.toString() ?? '',
                  style: pw.TextStyle(
                    font: fonts['regular'],
                    fontSize: 10,
                    color: PdfColors.black,
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء صف في الجدول
  static pw.TableRow _buildTableRow(
      String title, String value, Map<String, pw.Font> fonts,
      {bool isHighlighted = false, Map<String, dynamic>? codeData}) {
    // تحديد لون الخلفية والقيمة المعروضة للحقول الخاصة
    PdfColor bgColor = PdfColors.white;
    String displayValue = value;

    // فحص خاص لحقل "عنوان المستلم"
    if (title == 'عنوان المستلم:' && codeData != null) {
      String postalCode = codeData['postal_code']?.toString() ?? '';
      if (postalCode.isEmpty) {
        displayValue = 'يتم استلام البضاعة من مكتب الوكيل';
        bgColor = PdfColors.red200;
      }
    }

    // تطبيق اللون البرتقالي للحقول المميزة
    if (isHighlighted) {
      bgColor = PdfColors.orange100;
    }

    final titleFont = isHighlighted
        ? fonts['cairo']
        : fonts['bold']; // استخدام خط Cairo للعناوين
    final valueFont = fonts[
        'bold']; // تغيير من fonts['regular'] إلى fonts['bold'] لجعل الخط غامق دائمًا

    return pw.TableRow(
      decoration: pw.BoxDecoration(
        color: bgColor,
      ),
      children: [
        // عمود القيمة (الآن على اليسار)
        pw.Padding(
          // التحكم في الـpadding من المتغيرات الثابتة
          padding: pw.EdgeInsets.symmetric(
              vertical: tablePaddingVertical, horizontal: 4),
          child: pw.Text(
            displayValue,
            style: pw.TextStyle(
              font: valueFont,
              fontSize: 10.5, // زيادة حجم الخط من 9 إلى 10.5
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        // عمود العنوان (الآن على اليمين)
        pw.Padding(
          // التحكم في الـpadding من المتغيرات الثابتة
          padding: pw.EdgeInsets.symmetric(
              vertical: tablePaddingVertical, horizontal: 4),
          child: pw.Text(
            title,
            style: pw.TextStyle(
              font: titleFont,
              fontSize: 10, // زيادة حجم خط العنوان أيضًا من 9 إلى 10
            ),
            textAlign: pw.TextAlign.right,
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ],
    );
  }

  /// بناء صف رقم هاتف المستلم مع ارتفاع ثابت
  static pw.TableRow _buildReceiverPhoneRow(
      String title, String value, Map<String, pw.Font> fonts) {
    const double fixedHeight = 30.0; // ارتفاع ثابت لحقل رقم هاتف المستلم

    return pw.TableRow(
      children: [
        // عمود القيمة (الآن على اليسار)
        pw.Container(
          height: fixedHeight,
          padding: const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: fonts['bold'],
                fontSize: 10.5,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ),

        // عمود العنوان (الآن على اليمين)
        pw.Container(
          height: fixedHeight,
          padding: const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Text(
              title,
              style: pw.TextStyle(
                font: fonts['bold'],
                fontSize: 10,
              ),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء صف التأمين مع مربعات الاختيار
  static pw.TableRow _buildInsuranceRow(
      String title,
      Map<String, pw.Font> fonts,
      dynamic insuranceCost,
      Map<String, dynamic> codeData,
      String language) {
    // تحديد ما إذا كانت هناك نسبة تأمين أكبر من 0
    double insurancePercent =
        double.tryParse(codeData['insurance_percent']?.toString() ?? '0') ??
            0.0;
    bool hasInsurance = insurancePercent > 0;

    // تحديد لون الخلفية - أخضر فاتح إذا كانت الإجابة "نعم"
    PdfColor bgColor = hasInsurance ? PdfColors.green300 : PdfColors.white;

    return pw.TableRow(
      decoration: pw.BoxDecoration(
        color: bgColor,
      ),
      children: [
        // عمود القيمة (على اليسار)
        pw.Padding(
          padding: pw.EdgeInsets.symmetric(
              vertical: tablePaddingVertical, horizontal: 4),
          child: pw.Text(
            hasInsurance
                ? InvoiceTranslations.translate('yes', language)
                : InvoiceTranslations.translate('no', language),
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 10.5,
              color: PdfColors.black,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        // عمود العنوان (على اليمين)
        pw.Padding(
          padding: pw.EdgeInsets.symmetric(
              vertical: tablePaddingVertical, horizontal: 4),
          child: pw.Text(
            title,
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 10,
              color: PdfColors.black,
            ),
            textAlign: pw.TextAlign.right,
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ],
    );
  }

  /// الحصول على قيمة المدينة المناسبة
  static String _getCityValue(Map<String, dynamic> codeData) {
    // فحص إذا كان العنوان مفعل
    final bool isAddressInfoVisible = codeData['address_info_visible'] == 1 ||
        codeData['address_info_visible'] == true;

    // إذا كان العنوان مفعل، استخدم city_name، وإلا استخدم city
    if (isAddressInfoVisible) {
      return codeData['city_name']?.toString() ?? '';
    } else {
      return codeData['city']?.toString() ?? '';
    }
  }

  /// تنسيق قيمة البضاعة بإضافة فواصل الآلاف وعرضها بشكل مناسب
  static String _formatGoodsValue(dynamic value) {
    if (value == null) return '';

    try {
      // تحويل القيمة إلى رقم
      double numValue = double.parse(value.toString());
      // تقريب الرقم لإزالة الكسور العشرية
      int intValue = numValue.round();
      // استخدام نفس دالة تنسيق الأرقام المستخدمة في قسم التكلفة
      return _formatNumber(intValue);
    } catch (e) {
      // في حالة فشل التحويل، إرجاع القيمة كما هي
      return value.toString();
    }
  }

  /// تقليل طول نص تفاصيل البضاعة ليناسب 3 أسطر بدلاً من 4
  static String _truncateGoodsDescription(String description) {
    if (description.length > 120) {
      return '${description.substring(0, 120)}...';
    }
    return description;
  }

  /// بناء سلسلة الوزن مع القياسات بين أقواس
  static String _buildWeightDisplayWithDimensions(
      Map<String, dynamic> codeData) {
    List<String> lines = [];

    // جلب الوزن الحقيقي
    double realWeight = 0.0;
    if (codeData['real_weight_kg'] != null &&
        codeData['real_weight_kg'].toString().isNotEmpty) {
      realWeight =
          double.tryParse(codeData['real_weight_kg'].toString()) ?? 0.0;
    }

    // جلب الوزن الحجمي (الوزن الإضافي)
    double volumeWeight = 0.0;
    if (codeData['volume_weight'] != null &&
        codeData['volume_weight'].toString().isNotEmpty) {
      volumeWeight =
          double.tryParse(codeData['volume_weight'].toString()) ?? 0.0;
    }

    // إضافة الوزن الحقيقي في السطر الأول
    if (realWeight > 0) {
      lines.add('${realWeight.toStringAsFixed(1)} kg');
    }

    // إضافة الوزن الحجمي في السطر الثاني (إذا وجد)
    if (volumeWeight > 0) {
      lines.add('+ ${volumeWeight.toStringAsFixed(1)} kg');
    }

    // جلب القياسات
    double width = double.tryParse(codeData['width']?.toString() ?? '0') ?? 0.0;
    double height =
        double.tryParse(codeData['height']?.toString() ?? '0') ?? 0.0;
    double length =
        double.tryParse(codeData['length']?.toString() ?? '0') ?? 0.0;

    // إضافة القياسات في السطر الثالث (إذا وجدت)
    if (width > 0 && height > 0 && length > 0) {
      lines.add(
          '(${width.toStringAsFixed(0)}×${height.toStringAsFixed(0)}×${length.toStringAsFixed(0)})');
    }

    // دمج الأسطر مع فواصل الأسطر
    return lines.join('\n');
  }

  /// بناء عرض عدد القطع مع Box و Pallet
  static String _buildBoxAndPalletDisplay(Map<String, dynamic> codeData) {
    List<String> lines = [];

    String boxDisplay = codeData['box_no']?.toString() ?? '';
    String palletDisplay = codeData['pallet_no']?.toString() ?? '';

    // إضافة Box إذا كان موجود
    if (boxDisplay.isNotEmpty) {
      lines.add(boxDisplay);
      lines.add('Box');
    }

    // إضافة Pallet إذا كان موجود وأكبر من 0
    if (palletDisplay.isNotEmpty) {
      // التحقق من أن قيمة pallet أكبر من 0
      int palletValue = int.tryParse(palletDisplay) ?? 0;
      if (palletValue > 0) {
        if (boxDisplay.isNotEmpty) {
          lines.add(''); // سطر فارغ للفصل
        }
        lines.add(palletDisplay);
        lines.add('Pallet');
      }
    }

    return lines.join('\n');
  }

  /// الحصول على عنوان فرعي للشركة حسب اللغة
  static String _getCompanySubtitle(String language) {
    switch (language) {
      case InvoiceLanguageHelper.arabic:
        return 'الشركة الرائدة في مجال النقل الدولي';
      case InvoiceLanguageHelper.kurdish:
        return 'کۆمپانیای پێشەنگ لە بواری گواستنەوەی نێودەوڵەتی';
      case InvoiceLanguageHelper.english:
        return 'Leading Company in International Transport';
      default:
        return 'الشركة الرائدة في مجال النقل الدولي';
    }
  }

  /// الحصول على عنوان جدول معلومات المرسل والمستلم
  static String _getSenderReceiverTitle(String language) {
    switch (language) {
      case InvoiceLanguageHelper.arabic:
        return 'معلومات المرسل والمستلم';
      case InvoiceLanguageHelper.kurdish:
        return 'زانیاری نێردەر و وەرگر';
      case InvoiceLanguageHelper.english:
        return 'Sender and Receiver Information';
      default:
        return 'معلومات المرسل والمستلم';
    }
  }

  /// الحصول على حجم خط عناوين البنود حسب اللغة
  static double _getTermTitleFontSize(String language) {
    switch (language) {
      case InvoiceLanguageHelper.kurdish:
        return 9.0;
      case InvoiceLanguageHelper.english:
        return 8.0;
      default:
        return 10.0;
    }
  }

  /// الحصول على حجم خط محتوى البنود حسب اللغة
  static double _getTermContentFontSize(String language) {
    switch (language) {
      case InvoiceLanguageHelper.kurdish:
        return 9.0;
      case InvoiceLanguageHelper.english:
        return 8.0;
      default:
        return 10.0;
    }
  }

  /// الحصول على حجم خط البنود الرئيسية (1، 2) حسب اللغة
  static double _getMainTermFontSize(String language) {
    switch (language) {
      case InvoiceLanguageHelper.kurdish:
        return 9.0;
      case InvoiceLanguageHelper.english:
        return 8.0;
      default:
        return 10.0;
    }
  }
}
