import 'package:pdf/pdf.dart';

/// نموذج إعدادات الطباعة
class PrintSettingsModel {
  final int? id;
  final String documentType;
  final String? printerName;
  final String paperSize;
  final String orientation;
  final double marginTop;
  final double marginBottom;
  final double marginLeft;
  final double marginRight;
  final int dpi;
  final int copies;
  final bool isDefault;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PrintSettingsModel({
    this.id,
    required this.documentType,
    this.printerName,
    this.paperSize = 'A4',
    this.orientation = 'Portrait',
    this.marginTop = 15.0,
    this.marginBottom = 15.0,
    this.marginLeft = 15.0,
    this.marginRight = 15.0,
    this.dpi = 300,
    this.copies = 1,
    this.isDefault = false,
    this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نسخة من النموذج مع تعديل بعض القيم
  PrintSettingsModel copyWith({
    int? id,
    String? documentType,
    String? printerName,
    String? paperSize,
    String? orientation,
    double? marginTop,
    double? marginBottom,
    double? marginLeft,
    double? marginRight,
    int? dpi,
    int? copies,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PrintSettingsModel(
      id: id ?? this.id,
      documentType: documentType ?? this.documentType,
      printerName: printerName ?? this.printerName,
      paperSize: paperSize ?? this.paperSize,
      orientation: orientation ?? this.orientation,
      marginTop: marginTop ?? this.marginTop,
      marginBottom: marginBottom ?? this.marginBottom,
      marginLeft: marginLeft ?? this.marginLeft,
      marginRight: marginRight ?? this.marginRight,
      dpi: dpi ?? this.dpi,
      copies: copies ?? this.copies,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'document_type': documentType,
      'printer_name': printerName,
      'paper_size': paperSize,
      'orientation': orientation,
      'margin_top': marginTop,
      'margin_bottom': marginBottom,
      'margin_left': marginLeft,
      'margin_right': marginRight,
      'dpi': dpi,
      'copies': copies,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء النموذج من Map
  factory PrintSettingsModel.fromMap(Map<String, dynamic> map) {
    return PrintSettingsModel(
      id: map['id']?.toInt(),
      documentType: map['document_type'] ?? '',
      printerName: map['printer_name'],
      paperSize: map['paper_size'] ?? 'A4',
      orientation: map['orientation'] ?? 'Portrait',
      marginTop: (map['margin_top'] ?? 15.0).toDouble(),
      marginBottom: (map['margin_bottom'] ?? 15.0).toDouble(),
      marginLeft: (map['margin_left'] ?? 15.0).toDouble(),
      marginRight: (map['margin_right'] ?? 15.0).toDouble(),
      dpi: (map['dpi'] ?? 300).toInt(),
      copies: (map['copies'] ?? 1).toInt(),
      isDefault: (map['is_default'] ?? 0) == 1,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء من JSON
  factory PrintSettingsModel.fromJson(Map<String, dynamic> json) => PrintSettingsModel.fromMap(json);

  /// الحصول على تنسيق صفحة PDF بناءً على الإعدادات
  PdfPageFormat getPdfPageFormat() {
    // تحديد حجم الورقة
    PdfPageFormat baseFormat;
    switch (paperSize.toUpperCase()) {
      case 'A4':
        baseFormat = PdfPageFormat.a4;
        break;
      case 'A3':
        baseFormat = PdfPageFormat.a3;
        break;
      case 'A5':
        baseFormat = PdfPageFormat.a5;
        break;
      case 'LETTER':
        baseFormat = PdfPageFormat.letter;
        break;
      case 'LEGAL':
        baseFormat = PdfPageFormat.legal;
        break;
      case 'TABLOID':
        baseFormat = const PdfPageFormat(11 * PdfPageFormat.inch, 17 * PdfPageFormat.inch);
        break;
      default:
        baseFormat = PdfPageFormat.a4;
    }

    // تطبيق الاتجاه
    PdfPageFormat orientedFormat;
    if (orientation.toLowerCase() == 'landscape') {
      orientedFormat = baseFormat.landscape;
    } else {
      orientedFormat = baseFormat.portrait;
    }

    // تطبيق الهوامش
    return orientedFormat.copyWith(
      marginTop: marginTop * PdfPageFormat.mm,
      marginBottom: marginBottom * PdfPageFormat.mm,
      marginLeft: marginLeft * PdfPageFormat.mm,
      marginRight: marginRight * PdfPageFormat.mm,
    );
  }

  /// الحصول على قائمة أحجام الورق المدعومة
  static List<String> getSupportedPaperSizes() {
    return ['A4', 'A3', 'A5', 'Letter', 'Legal', 'Tabloid'];
  }

  /// الحصول على قائمة الاتجاهات المدعومة
  static List<String> getSupportedOrientations() {
    return ['Portrait', 'Landscape'];
  }

  /// الحصول على أسماء أحجام الورق المترجمة
  static Map<String, String> getPaperSizeDisplayNames() {
    return {
      'A4': 'A4 (210 × 297 مم)',
      'A3': 'A3 (297 × 420 مم)',
      'A5': 'A5 (148 × 210 مم)',
      'Letter': 'Letter (8.5 × 11 بوصة)',
      'Legal': 'Legal (8.5 × 14 بوصة)',
      'Tabloid': 'Tabloid (11 × 17 بوصة)',
    };
  }

  /// الحصول على أسماء الاتجاهات المترجمة
  static Map<String, String> getOrientationDisplayNames() {
    return {
      'Portrait': 'عمودي (Portrait)',
      'Landscape': 'أفقي (Landscape)',
    };
  }

  /// التحقق من صحة الإعدادات
  bool isValid() {
    return documentType.isNotEmpty &&
           getSupportedPaperSizes().contains(paperSize) &&
           getSupportedOrientations().contains(orientation) &&
           marginTop >= 0 &&
           marginBottom >= 0 &&
           marginLeft >= 0 &&
           marginRight >= 0 &&
           dpi > 0 &&
           copies > 0;
  }

  /// الحصول على وصف مختصر للإعدادات
  String getDescription() {
    final orientationName = getOrientationDisplayNames()[orientation] ?? orientation;
    final paperSizeName = getPaperSizeDisplayNames()[paperSize] ?? paperSize;
    return '$paperSizeName - $orientationName';
  }

  @override
  String toString() {
    return 'PrintSettingsModel{'
        'id: $id, '
        'documentType: $documentType, '
        'printerName: $printerName, '
        'paperSize: $paperSize, '
        'orientation: $orientation, '
        'margins: T:$marginTop B:$marginBottom L:$marginLeft R:$marginRight, '
        'dpi: $dpi, '
        'copies: $copies, '
        'isDefault: $isDefault'
        '}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is PrintSettingsModel &&
        other.id == id &&
        other.documentType == documentType &&
        other.printerName == printerName &&
        other.paperSize == paperSize &&
        other.orientation == orientation &&
        other.marginTop == marginTop &&
        other.marginBottom == marginBottom &&
        other.marginLeft == marginLeft &&
        other.marginRight == marginRight &&
        other.dpi == dpi &&
        other.copies == copies &&
        other.isDefault == isDefault;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        documentType.hashCode ^
        printerName.hashCode ^
        paperSize.hashCode ^
        orientation.hashCode ^
        marginTop.hashCode ^
        marginBottom.hashCode ^
        marginLeft.hashCode ^
        marginRight.hashCode ^
        dpi.hashCode ^
        copies.hashCode ^
        isDefault.hashCode;
  }
}

/// إعدادات الطباعة الافتراضية لأنواع المستندات المختلفة
class DefaultPrintSettings {
  static const PrintSettingsModel invoice = PrintSettingsModel(
    documentType: 'invoice',
    paperSize: 'A4',
    orientation: 'Portrait',
    marginTop: 15.0,
    marginBottom: 15.0,
    marginLeft: 15.0,
    marginRight: 15.0,
    dpi: 300,
    copies: 1,
    isDefault: true,
  );

  static const PrintSettingsModel officeLabel = PrintSettingsModel(
    documentType: 'office_label',
    paperSize: 'A4',
    orientation: 'Portrait',
    marginTop: 5.0,
    marginBottom: 5.0,
    marginLeft: 5.0,
    marginRight: 5.0,
    dpi: 300,
    copies: 1,
  );

  static const PrintSettingsModel postLabel = PrintSettingsModel(
    documentType: 'post_label',
    paperSize: 'A4',
    orientation: 'Portrait',
    marginTop: 8.0,
    marginBottom: 8.0,
    marginLeft: 8.0,
    marginRight: 8.0,
    dpi: 300,
    copies: 1,
  );

  /// الحصول على الإعدادات الافتراضية لنوع مستند معين
  static PrintSettingsModel getDefaultForDocumentType(String documentType) {
    switch (documentType.toLowerCase()) {
      case 'invoice':
        return invoice;
      case 'office_label':
        return officeLabel;
      case 'post_label':
        return postLabel;
      default:
        return invoice;
    }
  }
}