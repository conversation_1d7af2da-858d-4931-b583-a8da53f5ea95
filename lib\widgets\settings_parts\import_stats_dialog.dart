import 'package:flutter/material.dart';

class ImportStatsDialog extends StatelessWidget {
  final String title;
  final int totalImported;
  final int newItems;
  final int skippedItems;

  const ImportStatsDialog({
    super.key,
    required this.title,
    required this.totalImported,
    required this.newItems,
    required this.skippedItems,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إجمالي العناصر المستوردة
          _buildStatRow(
            context,
            'Total Imported Items:',
            '$totalImported',
            Colors.blue,
          ),
          const SizedBox(height: 12),

          // العناصر الجديدة
          _buildStatRow(
            context,
            'New Items Added:',
            '$newItems',
            Colors.green,
          ),
          const SizedBox(height: 12),

          // العناصر المكررة
          _buildStatRow(
            context,
            'Duplicate Items Skipped:',
            '$skippedItems',
            Colors.orange,
          ),

          // نسبة العناصر الجديدة
          if (totalImported > 0) ...[
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 12),
            _buildProgressBar(
              context,
              'New Items Percentage:',
              newItems / totalImported,
              Colors.green,
            ),
            const SizedBox(height: 12),
            _buildProgressBar(
              context,
              'Duplicate Items Percentage:',
              skippedItems / totalImported,
              Colors.orange,
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  // بناء صف إحصائية
  Widget _buildStatRow(
      BuildContext context, String label, String value, Color valueColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: valueColor,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  // بناء شريط تقدم
  Widget _buildProgressBar(
      BuildContext context, String label, double value, Color progressColor) {
    final percentage = (value * 100).toInt();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label $percentage%',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: value,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
          minHeight: 10,
          borderRadius: BorderRadius.circular(5),
        ),
      ],
    );
  }
}
