import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import '../models/germany_address.dart';
import '../models/netherlands_address.dart';
import 'package:flutter/foundation.dart';

typedef ProgressCallback = void Function(
    double progress, int processedRows, int totalRows);

class ExcelImportService {
  // استيراد عناوين ألمانيا من ملف إكسل
  static Future<List<GermanyAddress>> importGermanyAddresses({
    Function(String)? onError,
    ProgressCallback? onProgress,
  }) async {
    List<GermanyAddress> addresses = [];

    try {
      // اختيار ملف إكسل
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.single.path == null) {
        return addresses;
      }

      // قراءة الملف بطريقة آمنة
      File file;
      Uint8List bytes;

      try {
        file = File(result.files.single.path!);

        // التحقق من وجود الملف
        if (!file.existsSync()) {
          if (onError != null) {
            onError('Selected file does not exist or cannot be accessed.');
          }
          return addresses;
        }

        // قراءة الملف
        bytes = file.readAsBytesSync();

        // التحقق من حجم الملف
        if (bytes.isEmpty) {
          if (onError != null) {
            onError('Selected file is empty.');
          }
          return addresses;
        }

        debugPrint(
            'File size: ${(bytes.length / 1024 / 1024).toStringAsFixed(2)} MB');
      } catch (e) {
        if (onError != null) {
          onError(
              'Error reading file: Unable to access or read the selected file. Please make sure the file is not open in another application.');
        }
        return addresses;
      }

      // التعامل مع أخطاء تنسيق Excel بطريقة شاملة
      Excel excel;
      try {
        excel = Excel.decodeBytes(bytes);
      } catch (e) {
        // تحرير الذاكرة في حالة الخطأ
        bytes = Uint8List(0);

        final errorMessage = e.toString().toLowerCase();

        if (errorMessage.contains('custom numfmtid') ||
            errorMessage.contains('format error') ||
            errorMessage.contains('invalid') ||
            errorMessage.contains('corrupt')) {
          if (onError != null) {
            onError(
                'Excel file format error. Please try one of the following solutions:\n'
                '1. Save the file as .xlsx format (Excel Workbook)\n'
                '2. Use CSV format instead\n'
                '3. Create a new Excel file and copy the data\n'
                '4. Make sure the file is not corrupted');
          }
        } else if (errorMessage.contains('permission') ||
            errorMessage.contains('access')) {
          if (onError != null) {
            onError(
                'File access error. Please close the file in Excel or other applications and try again.');
          }
        } else {
          if (onError != null) {
            onError(
                'Unable to read Excel file. Please check the file format and try again.\nError details: ${e.toString()}');
          }
        }
        return addresses;
      }

      // تحرير الذاكرة بعد فك ترميز الملف
      bytes = Uint8List(0);

      // التحقق من وجود الجداول
      if (excel.tables.isEmpty) {
        if (onError != null) {
          onError('Excel file contains no worksheets or data.');
        }
        return addresses;
      }

      // افتراض أن الورقة الأولى تحتوي على البيانات
      var sheet = excel.tables.keys.first;
      var table = excel.tables[sheet];

      if (table == null || table.rows.isEmpty) {
        if (onError != null) {
          onError('The selected worksheet is empty or contains no valid data.');
        }
        return addresses;
      }

      // التحقق من وجود العناوين المطلوبة
      var headers = table.rows[0];
      int? postalCodeIndex;
      int? cityNameIndex;

      // البحث عن الأعمدة مع التسامح في التسمية
      for (int i = 0; i < headers.length; i++) {
        var cellValue = headers[i]?.value;
        var header = cellValue is TextCellValue
            ? cellValue.value.toString().trim().toLowerCase()
            : cellValue.toString().trim().toLowerCase();

        // البحث عن عمود الرمز البريدي
        if (header == 'postal_code' ||
            header == 'postcode' ||
            header == 'post_code' ||
            header == 'zip' ||
            header == 'zip_code') {
          postalCodeIndex = i;
        }
        // البحث عن عمود اسم المدينة
        else if (header == 'city_name' ||
            header == 'city' ||
            header == 'town' ||
            header == 'place') {
          cityNameIndex = i;
        }
      }

      if (postalCodeIndex == null || cityNameIndex == null) {
        List<String> missingColumns = [];
        if (postalCodeIndex == null) missingColumns.add('postal_code');
        if (cityNameIndex == null) missingColumns.add('city_name');

        if (onError != null) {
          onError(
              'The Excel file does not contain the required columns: ${missingColumns.join(", ")}.\n'
              'Please make sure your file has columns named "postal_code" and "city_name" in the first row.');
        }
        return addresses;
      }

      // إجمالي عدد الصفوف (بعد استبعاد صف العناوين)
      final totalRows = table.rows.length - 1;

      if (totalRows <= 0) {
        if (onError != null) {
          onError('Excel file contains headers only, no data rows found.');
        }
        return addresses;
      }

      // زيادة حجم الدفعة لتحسين الأداء مع الملفات الكبيرة
      const int batchSize = 10000;

      // تقليل عدد مرات تحديث واجهة المستخدم
      const int uiUpdateFrequency = 20000;

      // تجهيز قائمة مؤقتة لتخزين العناوين
      List<GermanyAddress> tempAddresses = [];

      // قراءة البيانات من الصف الثاني (بعد العناوين)
      for (int i = 1; i < table.rows.length; i++) {
        try {
          var row = table.rows[i];
          if (row.length <= postalCodeIndex || row.length <= cityNameIndex) {
            continue; // تخطي الصفوف غير المكتملة
          }

          var postalCodeValue = row[postalCodeIndex]?.value;
          var postalCode = postalCodeValue is TextCellValue
              ? postalCodeValue.value.toString().trim()
              : postalCodeValue.toString().trim();

          var cityNameValue = row[cityNameIndex]?.value;
          var cityName = cityNameValue is TextCellValue
              ? cityNameValue.value.toString().trim()
              : cityNameValue.toString().trim();

          if (postalCode.isNotEmpty && cityName.isNotEmpty) {
            tempAddresses.add(GermanyAddress(
              postalCode: postalCode,
              cityName: cityName,
            ));
          }

          // تحديث التقدم كل batchSize صف أو عند الانتهاء
          if (i % batchSize == 0 || i == table.rows.length - 1) {
            // إضافة العناوين المؤقتة إلى القائمة الرئيسية
            addresses.addAll(tempAddresses);
            tempAddresses.clear();

            // تحديث واجهة المستخدم بشكل أقل تكراراً لتحسين الأداء
            if (i % uiUpdateFrequency == 0 || i == table.rows.length - 1) {
              // حساب نسبة التقدم
              final progress = i / totalRows;

              // استدعاء دالة التقدم
              if (onProgress != null) {
                onProgress(progress, i, totalRows);
              }

              // إعطاء فرصة للنظام للتنفس
              await Future.delayed(const Duration(milliseconds: 1));
            }
          }
        } catch (e) {
          // ?? ??? ????? debugPrint
          // استمر في معالجة باقي الصفوف
          continue;
        }
      }

      return addresses;
    } catch (e) {
      if (onError != null) {
        onError(
            'An unexpected error occurred while importing the file. Please try again or contact support if the problem persists.\nError details: ${e.toString()}');
      }
      return [];
    }
  }

  // استيراد عناوين هولندا من ملف إكسل
  static Future<List<NetherlandsAddress>> importNetherlandsAddresses({
    Function(String)? onError,
    ProgressCallback? onProgress,
    bool ignoreNonCriticalColumns = true,
  }) async {
    List<NetherlandsAddress> addresses = [];

    try {
      // اختيار ملف إكسل
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.single.path == null) {
        return addresses;
      }

      // قراءة الملف بطريقة آمنة
      File file;
      Uint8List bytes;

      try {
        file = File(result.files.single.path!);

        // التحقق من وجود الملف
        if (!file.existsSync()) {
          if (onError != null) {
            onError('Selected file does not exist or cannot be accessed.');
          }
          return addresses;
        }

        // قراءة الملف
        bytes = file.readAsBytesSync();

        // التحقق من حجم الملف
        if (bytes.isEmpty) {
          if (onError != null) {
            onError('Selected file is empty.');
          }
          return addresses;
        }

        debugPrint(
            'File size: ${(bytes.length / 1024 / 1024).toStringAsFixed(2)} MB');
      } catch (e) {
        if (onError != null) {
          onError(
              'Error reading file: Unable to access or read the selected file. Please make sure the file is not open in another application.');
        }
        return addresses;
      }

      // التعامل مع أخطاء تنسيق Excel بطريقة شاملة
      Excel excel;
      try {
        excel = Excel.decodeBytes(bytes);
      } catch (e) {
        // تحرير الذاكرة في حالة الخطأ
        bytes = Uint8List(0);

        final errorMessage = e.toString().toLowerCase();

        if (errorMessage.contains('custom numfmtid') ||
            errorMessage.contains('format error') ||
            errorMessage.contains('invalid') ||
            errorMessage.contains('corrupt')) {
          if (onError != null) {
            onError(
                'Excel file format error. Please try one of the following solutions:\n'
                '1. Save the file as .xlsx format (Excel Workbook)\n'
                '2. Use CSV format instead\n'
                '3. Create a new Excel file and copy the data\n'
                '4. Make sure the file is not corrupted');
          }
        } else if (errorMessage.contains('permission') ||
            errorMessage.contains('access')) {
          if (onError != null) {
            onError(
                'File access error. Please close the file in Excel or other applications and try again.');
          }
        } else {
          if (onError != null) {
            onError(
                'Unable to read Excel file. Please check the file format and try again.\nError details: ${e.toString()}');
          }
        }
        return addresses;
      }

      // تحرير الذاكرة بعد فك ترميز الملف
      bytes = Uint8List(0);

      // التحقق من وجود الجداول
      if (excel.tables.isEmpty) {
        if (onError != null) {
          onError('Excel file contains no worksheets or data.');
        }
        return addresses;
      }

      // التحقق من وجود الصفوف
      if (excel.tables.values.first.rows.isEmpty) {
        if (onError != null) {
          onError('Excel file contains no rows or data.');
        }
        return addresses;
      }

      // التحقق من وجود العناوين المطلوبة
      var headers = excel.tables.values.first.rows[0];
      Map<String, int> columnIndices = {};

      // طباعة العناوين الموجودة للتشخيص
      headers.map((cell) {
        var cellValue = cell?.value;
        return cellValue is TextCellValue
            ? cellValue.value.toString().trim().toLowerCase()
            : cellValue.toString().trim().toLowerCase();
      }).toList();
      // ?? ??? ????? debugPrint

      // قائمة الأعمدة الحرجة (المطلوبة دائماً)
      List<String> criticalColumns = ['postal_code', 'street', 'city_name'];

      // قائمة الأعمدة غير الحرجة (يمكن تجاهلها)
      List<String> nonCriticalColumns = [
        'postal_code_numbers',
        'postal_code_letters',
        'min_nummer',
        'max_nummer',
      ];

      // قائمة الأعمدة البديلة (للتوافق مع التنسيقات المختلفة)
      Map<String, List<String>> alternativeColumns = {
        'min_nummer': ['min_number', 'min_num', 'min'],
        'max_nummer': ['max_number', 'max_num', 'max'],
        'postal_code': ['postcode', 'post_code', 'zip', 'zip_code'],
        'street': ['street_name', 'straat', 'straatname'],
        'city_name': ['city', 'stad', 'plaats', 'town'],
      };

      // البحث عن الأعمدة الأساسية (مع مزيد من التسامح)
      for (int i = 0; i < headers.length; i++) {
        var cellValue = headers[i]?.value;
        var header = cellValue is TextCellValue
            ? cellValue.value.toString().trim().toLowerCase()
            : cellValue.toString().trim().toLowerCase();

        // البحث المباشر
        if (criticalColumns.contains(header) ||
            nonCriticalColumns.contains(header)) {
          columnIndices[header] = i;
          // ?? ??? ????? debugPrint
        }

        // البحث عن الأعمدة البديلة
        for (var entry in alternativeColumns.entries) {
          if (!columnIndices.containsKey(entry.key) &&
              entry.value.contains(header)) {
            columnIndices[entry.key] = i;
            debugPrint(
                'Found alternative column: $header for ${entry.key} at index $i');
          }
        }
      }

      // التحقق من وجود جميع الأعمدة الحرجة
      List<String> missingCriticalColumns = [];
      for (var column in criticalColumns) {
        if (!columnIndices.containsKey(column)) {
          missingCriticalColumns.add(column);
        }
      }

      if (missingCriticalColumns.isNotEmpty) {
        if (onError != null) {
          onError(
              'The file does not contain the required critical columns: ${missingCriticalColumns.join(", ")}');
        }
        return addresses;
      }

      // التحقق من وجود الأعمدة min_nummer و max_nummer
      if (!columnIndices.containsKey('min_nummer') ||
          !columnIndices.containsKey('max_nummer')) {
        // محاولة استخدام أعمدة بديلة أو قيم افتراضية
        if (!columnIndices.containsKey('min_nummer')) {
          debugPrint(
              'Warning: min_nummer column not found, using default value "0"');
        }
        if (!columnIndices.containsKey('max_nummer')) {
          debugPrint(
              'Warning: max_nummer column not found, using default value "0"');
        }
      }

      // إجمالي عدد الصفوف (بعد استبعاد صف العناوين)
      final totalRows = excel.tables.values.first.rows.length - 1;

      // تقسيم المعالجة إلى أجزاء أصغر للملفات الكبيرة جداً
      const int batchSize = 5000;
      const int uiUpdateFrequency = 10000;

      // تجهيز قائمة مؤقتة لتخزين العناوين
      List<NetherlandsAddress> tempAddresses = [];

      // عدد الصفوف التي تمت معالجتها
      int processedRows = 0;
      int validRows = 0;
      int invalidRows = 0;

      // قراءة البيانات من الصف الثاني (بعد العناوين)
      for (int i = 1; i < excel.tables.values.first.rows.length; i++) {
        var row = excel.tables.values.first.rows[i];
        processedRows++;

        // التحقق من أن الصف يحتوي على بيانات كافية للأعمدة الحرجة
        bool isValidRow = true;
        for (var column in criticalColumns) {
          if (!columnIndices.containsKey(column) ||
              row.length <= columnIndices[column]!) {
            isValidRow = false;
            break;
          }
          var cellValue = row[columnIndices[column]!]?.value;
          if (cellValue == null) {
            isValidRow = false;
            break;
          }
        }

        if (!isValidRow) {
          invalidRows++;
          continue;
        }

        validRows++;

        try {
          // دالة مساعدة لاستخراج القيم من الخلايا
          String extractCellValue(String columnName,
              [String defaultValue = '']) {
            if (!columnIndices.containsKey(columnName) ||
                row.length <= columnIndices[columnName]!) {
              return defaultValue;
            }
            var cellValue = row[columnIndices[columnName]!]?.value;
            if (cellValue is TextCellValue) {
              return cellValue.value.toString().trim();
            } else if (cellValue is IntCellValue) {
              return cellValue.value.toString().trim();
            } else if (cellValue is DoubleCellValue) {
              return cellValue.value.toString().trim();
            } else if (cellValue != null) {
              return cellValue.toString().trim();
            }
            return defaultValue;
          }

          // استخراج القيم مع التعامل مع الأعمدة المفقودة
          String postalCode = extractCellValue('postal_code');
          String postalCodeNumbers = extractCellValue('postal_code_numbers');
          String postalCodeLetters = extractCellValue('postal_code_letters');
          String street = extractCellValue('street');
          String minNummer = extractCellValue('min_nummer', '0');
          String maxNummer = extractCellValue('max_nummer', '0');
          String cityName = extractCellValue('city_name');

          // إنشاء كائن العنوان
          tempAddresses.add(NetherlandsAddress(
            postalCode: postalCode,
            postalCodeNumbers: postalCodeNumbers,
            postalCodeLetters: postalCodeLetters,
            street: street,
            minNummer: minNummer,
            maxNummer: maxNummer,
            cityName: cityName,
          ));
        } catch (e) {
          // ?? ??? ????? debugPrint
          invalidRows++;
          continue;
        }

        // تحديث التقدم كل batchSize صف أو عند الانتهاء
        if (tempAddresses.length >= batchSize ||
            i == excel.tables.values.first.rows.length - 1) {
          // إضافة العناوين المؤقتة إلى القائمة الرئيسية
          addresses.addAll(tempAddresses);

          // طباعة معلومات تشخيصية
          debugPrint(
              'Added batch of ${tempAddresses.length} addresses. Total: ${addresses.length}');

          tempAddresses.clear();

          // تحديث واجهة المستخدم بشكل أقل تكراراً لتحسين الأداء
          if (i % uiUpdateFrequency == 0 ||
              i == excel.tables.values.first.rows.length - 1) {
            // حساب نسبة التقدم
            final progress = i / totalRows;

            // استدعاء دالة التقدم
            if (onProgress != null) {
              onProgress(progress, i, totalRows);
            }

            // إعطاء فرصة للنظام للتنفس
            await Future.delayed(const Duration(milliseconds: 1));
          }
        }
      }

      // طباعة إحصائيات نهائية
      debugPrint(
          'Netherlands addresses import completed: Processed $processedRows rows, Valid: $validRows, Invalid: $invalidRows, Total addresses: ${addresses.length}');

      return addresses;
    } catch (e) {
      if (onError != null) {
        onError(
            'An unexpected error occurred while importing the file. Please try again or contact support if the problem persists.\nError details: ${e.toString()}');
      }
      return [];
    }
  }
}
