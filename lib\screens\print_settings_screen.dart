import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/print_settings_model.dart';
import '../services/print_settings_database_service.dart';
import '../services/printer_service.dart';

/// شاشة إعدادات الطباعة
class PrintSettingsScreen extends StatefulWidget {
  final String documentType;
  final PrintSettingsModel? initialSettings;
  final Function(PrintSettingsModel)? onSettingsSaved;

  const PrintSettingsScreen({
    super.key,
    required this.documentType,
    this.initialSettings,
    this.onSettingsSaved,
  });

  @override
  State<PrintSettingsScreen> createState() => _PrintSettingsScreenState();
}

class _PrintSettingsScreenState extends State<PrintSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _databaseService = PrintSettingsDatabaseService();
  
  // متحكمات النصوص
  final _marginTopController = TextEditingController();
  final _marginBottomController = TextEditingController();
  final _marginLeftController = TextEditingController();
  final _marginRightController = TextEditingController();
  final _copiesController = TextEditingController();
  
  // المتغيرات
  List<String> _availablePrinters = [];
  String? _selectedPrinter;
  String _selectedPaperSize = 'A4';
  String _selectedOrientation = 'Portrait';
  int _selectedDpi = 300;
  bool _isLoading = false;
  bool _isLoadingPrinters = true;
  
  @override
  void initState() {
    super.initState();
    _initializeSettings();
    _loadPrinters();
  }

  @override
  void dispose() {
    _marginTopController.dispose();
    _marginBottomController.dispose();
    _marginLeftController.dispose();
    _marginRightController.dispose();
    _copiesController.dispose();
    super.dispose();
  }

  /// تهيئة الإعدادات
  void _initializeSettings() {
    final settings = widget.initialSettings ?? DefaultPrintSettings.getDefaultForDocumentType(widget.documentType);
    
    _selectedPrinter = settings.printerName;
    _selectedPaperSize = settings.paperSize;
    _selectedOrientation = settings.orientation;
    _selectedDpi = settings.dpi;
    
    _marginTopController.text = settings.marginTop.toString();
    _marginBottomController.text = settings.marginBottom.toString();
    _marginLeftController.text = settings.marginLeft.toString();
    _marginRightController.text = settings.marginRight.toString();
    _copiesController.text = settings.copies.toString();
  }

  /// تحميل قائمة الطابعات
  Future<void> _loadPrinters() async {
    setState(() {
      _isLoadingPrinters = true;
    });

    try {
      final printers = await PrinterService.getInstalledPrinters();
      final defaultPrinter = await PrinterService.getDefaultPrinter();
      
      setState(() {
        _availablePrinters = printers;
        if (_selectedPrinter == null && defaultPrinter != null) {
          _selectedPrinter = defaultPrinter;
        }
        _isLoadingPrinters = false;
      });
    } catch (e) {
      setState(() {
        _availablePrinters = ['الطابعة الافتراضية'];
        _selectedPrinter = 'الطابعة الافتراضية';
        _isLoadingPrinters = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل قائمة الطابعات: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final settings = PrintSettingsModel(
        documentType: widget.documentType,
        printerName: _selectedPrinter,
        paperSize: _selectedPaperSize,
        orientation: _selectedOrientation,
        marginTop: double.parse(_marginTopController.text),
        marginBottom: double.parse(_marginBottomController.text),
        marginLeft: double.parse(_marginLeftController.text),
        marginRight: double.parse(_marginRightController.text),
        dpi: _selectedDpi,
        copies: int.parse(_copiesController.text),
        isDefault: widget.documentType == 'invoice', // الفاتورة افتراضية
      );

      await _databaseService.savePrintSettings(settings);
      
      if (widget.onSettingsSaved != null) {
        widget.onSettingsSaved!(settings);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ إعدادات الطباعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(settings);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إعادة تعيين الإعدادات إلى القيم الافتراضية
  void _resetToDefaults() {
    final defaultSettings = DefaultPrintSettings.getDefaultForDocumentType(widget.documentType);
    
    setState(() {
      _selectedPaperSize = defaultSettings.paperSize;
      _selectedOrientation = defaultSettings.orientation;
      _selectedDpi = defaultSettings.dpi;
      
      _marginTopController.text = defaultSettings.marginTop.toString();
      _marginBottomController.text = defaultSettings.marginBottom.toString();
      _marginLeftController.text = defaultSettings.marginLeft.toString();
      _marginRightController.text = defaultSettings.marginRight.toString();
      _copiesController.text = defaultSettings.copies.toString();
    });
  }

  /// تحديث قائمة الطابعات
  Future<void> _refreshPrinters() async {
    await _loadPrinters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إعدادات الطباعة - ${_getDocumentTypeDisplayName()}'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshPrinters,
            tooltip: 'تحديث قائمة الطابعات',
          ),
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _resetToDefaults,
            tooltip: 'إعادة تعيين القيم الافتراضية',
          ),
        ],
      ),
      body: _isLoadingPrinters
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل قائمة الطابعات...'),
                ],
              ),
            )
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildPrinterSection(),
                    const SizedBox(height: 24),
                    _buildPaperSection(),
                    const SizedBox(height: 24),
                    _buildMarginsSection(),
                    const SizedBox(height: 24),
                    _buildAdvancedSection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  /// قسم اختيار الطابعة
  Widget _buildPrinterSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.print, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات الطابعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh, size: 20),
                  onPressed: _refreshPrinters,
                  tooltip: 'تحديث',
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedPrinter,
              decoration: const InputDecoration(
                labelText: 'اختيار الطابعة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.print),
              ),
              items: _availablePrinters.map((printer) {
                return DropdownMenuItem(
                  value: printer,
                  child: Text(printer),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPrinter = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار طابعة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  /// قسم إعدادات الورقة
  Widget _buildPaperSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.description, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'إعدادات الورقة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPaperSize,
                    decoration: const InputDecoration(
                      labelText: 'حجم الورقة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.crop_din),
                    ),
                    items: PrintSettingsModel.getSupportedPaperSizes().map((size) {
                      final displayNames = PrintSettingsModel.getPaperSizeDisplayNames();
                      return DropdownMenuItem(
                        value: size,
                        child: Text(displayNames[size] ?? size),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedPaperSize = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedOrientation,
                    decoration: const InputDecoration(
                      labelText: 'اتجاه الورقة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.rotate_90_degrees_ccw),
                    ),
                    items: PrintSettingsModel.getSupportedOrientations().map((orientation) {
                      final displayNames = PrintSettingsModel.getOrientationDisplayNames();
                      return DropdownMenuItem(
                        value: orientation,
                        child: Text(displayNames[orientation] ?? orientation),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedOrientation = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الهوامش
  Widget _buildMarginsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.border_outer, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'الهوامش (مم)',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _marginTopController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش العلوي',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.keyboard_arrow_up),
                      suffixText: 'مم',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _marginBottomController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش السفلي',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.keyboard_arrow_down),
                      suffixText: 'مم',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _marginLeftController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش الأيسر',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.keyboard_arrow_left),
                      suffixText: 'مم',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _marginRightController,
                    decoration: const InputDecoration(
                      labelText: 'الهامش الأيمن',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.keyboard_arrow_right),
                      suffixText: 'مم',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final margin = double.tryParse(value);
                      if (margin == null || margin < 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الإعدادات المتقدمة
  Widget _buildAdvancedSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'إعدادات متقدمة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedDpi,
                    decoration: const InputDecoration(
                      labelText: 'دقة الطباعة (DPI)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.high_quality),
                    ),
                    items: [150, 300, 600, 1200].map((dpi) {
                      return DropdownMenuItem(
                        value: dpi,
                        child: Text('$dpi DPI'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedDpi = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _copiesController,
                    decoration: const InputDecoration(
                      labelText: 'عدد النسخ',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.content_copy),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final copies = int.tryParse(value);
                      if (copies == null || copies < 1) {
                        return 'يجب أن يكون أكبر من 0';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[700],
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('حفظ الإعدادات'),
          ),
        ),
      ],
    );
  }

  /// الحصول على اسم نوع المستند للعرض
  String _getDocumentTypeDisplayName() {
    switch (widget.documentType) {
      case 'invoice':
        return 'الفاتورة';
      case 'office_label':
        return 'ملصق المكتب';
      case 'post_label':
        return 'ملصق البريد';
      default:
        return widget.documentType;
    }
  }
}