import 'package:flutter/material.dart';

/// Mixin يحتوي على المتغيرات المشتركة لشاشة الإعدادات
mixin SettingsVariablesMixin<T extends StatefulWidget> on State<T> {
  // متغيرات حالة قاعدة البيانات
  bool _isUpdating = false;
  bool _isQueryingDatabase = false;
  bool _isDeletingCodeData = false;

  // متغيرات حالة استيراد العناوين
  bool _isImportingGermany = false;
  bool _isImportingNetherlands = false;
  bool _isLoadingGermany = false;
  bool _isLoadingNetherlands = false;

  // متغيرات لتتبع تقدم الاستيراد
  double _importProgress = 0.0;
  int _processedRows = 0;
  int _totalRows = 0;
  String _importStage = '';

  // getters للمتغيرات
  bool get isUpdating => _isUpdating;
  bool get isQueryingDatabase => _isQueryingDatabase;
  bool get isDeletingCodeData => _isDeletingCodeData;
  bool get isImportingGermany => _isImportingGermany;
  bool get isImportingNetherlands => _isImportingNetherlands;
  bool get isLoadingGermany => _isLoadingGermany;
  bool get isLoadingNetherlands => _isLoadingNetherlands;
  double get importProgress => _importProgress;
  int get processedRows => _processedRows;
  int get totalRows => _totalRows;
  String get importStage => _importStage;

  // setters للمتغيرات
  set isUpdating(bool value) => setState(() => _isUpdating = value);
  set isQueryingDatabase(bool value) =>
      setState(() => _isQueryingDatabase = value);
  set isDeletingCodeData(bool value) =>
      setState(() => _isDeletingCodeData = value);
  set isImportingGermany(bool value) =>
      setState(() => _isImportingGermany = value);
  set isImportingNetherlands(bool value) =>
      setState(() => _isImportingNetherlands = value);
  set isLoadingGermany(bool value) => setState(() => _isLoadingGermany = value);
  set isLoadingNetherlands(bool value) =>
      setState(() => _isLoadingNetherlands = value);

  // تحديث متغيرات التقدم
  void updateImportProgress({
    required double progress,
    required int processedRows,
    required int totalRows,
    required String stage,
  }) {
    setState(() {
      _importProgress = progress;
      _processedRows = processedRows;
      _totalRows = totalRows;
      _importStage = stage;
    });
  }
}
