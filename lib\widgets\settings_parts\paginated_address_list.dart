import 'package:flutter/material.dart';
import 'address_list_header.dart';
import 'address_list_row.dart';

class PaginatedAddressList<T> extends StatefulWidget {
  final List<T> items;
  final List<String> columns;
  final List<double> columnWidths;
  final List<String> Function(T item) itemToValues;
  final Function(int index)? onDelete;
  final bool isLoading;
  final String emptyMessage;
  final String searchHint;

  const PaginatedAddressList({
    super.key,
    required this.items,
    required this.columns,
    required this.columnWidths,
    required this.itemToValues,
    this.onDelete,
    this.isLoading = false,
    this.emptyMessage = 'No imported addresses found',
    this.searchHint = 'بحث...',
  });

  @override
  State<PaginatedAddressList<T>> createState() =>
      _PaginatedAddressListState<T>();
}

class _PaginatedAddressListState<T> extends State<PaginatedAddressList<T>> {
  static const int _pageSize = 50;
  int _currentPage = 0;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<T> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _updateFilteredItems();
  }

  @override
  void didUpdateWidget(PaginatedAddressList<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _updateFilteredItems();
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreItems();
    }
  }

  void _loadMoreItems() {
    if (_currentPage * _pageSize < _filteredItems.length) {
      setState(() {
        _currentPage++;
      });
    }
  }

  void _updateFilteredItems() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredItems = List.from(widget.items);
      } else {
        _filteredItems = widget.items.where((item) {
          final values = widget.itemToValues(item);
          return values.any((value) =>
              value.toLowerCase().contains(_searchQuery.toLowerCase()));
        }).toList();
      }
      _currentPage = 1; // Reset to first page
    });
  }

  void _onSearchChanged(String query) {
    _searchQuery = query;
    _updateFilteredItems();
  }

  @override
  Widget build(BuildContext context) {
    final displayedItems =
        _filteredItems.take(_currentPage * _pageSize).toList();
    final hasMore = _filteredItems.length > displayedItems.length;

    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: widget.searchHint,
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
            ),
            onChanged: _onSearchChanged,
          ),
        ),

        // Header
        AddressListHeader(
          columns: widget.columns,
          columnWidths: widget.columnWidths,
        ),

        // List
        Expanded(
          child: widget.isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredItems.isEmpty
                  ? Center(child: Text(widget.emptyMessage))
                  : ListView.builder(
                      controller: _scrollController,
                      itemCount: displayedItems.length + (hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == displayedItems.length) {
                          return const Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        }

                        final item = displayedItems[index];
                        return AddressListRow(
                          values: widget.itemToValues(item),
                          columnWidths: widget.columnWidths,
                          isAlternate: index % 2 == 1,
                          onDelete: widget.onDelete != null
                              ? () => widget.onDelete!(index)
                              : null,
                        );
                      },
                    ),
        ),

        // Status bar
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Showing ${displayedItems.length} of ${_filteredItems.length} items',
                style: const TextStyle(fontSize: 12),
              ),
              if (_searchQuery.isNotEmpty)
                Text(
                  'Search results: ${_filteredItems.length} items',
                  style: const TextStyle(fontSize: 12),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
