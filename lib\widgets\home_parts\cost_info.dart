import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logging/logging.dart';

import '../../mixins/cost_mixin/cost_info_calculations_mixin.dart';
import '../../mixins/cost_mixin/cost_info_data_mixin.dart';
import '../../mixins/cost_mixin/cost_info_state_mixin.dart';
import '../../mixins/cost_mixin/cost_info_ui_mixin.dart';

class CostInfo extends StatefulWidget {
  const CostInfo({super.key});

  @override
  State<CostInfo> createState() => _CostInfoState();
}

class _CostInfoState extends State<CostInfo>
    with
        CostInfoStateMixin<CostInfo>,
        CostInfoCalculationsMixin<CostInfo>,
        CostInfoDataMixin<CostInfo>,
        CostInfoUIMixin<CostInfo> {
  // مسجل للأحداث
  final Logger _logger = Logger('CostInfo');

  // متغيرات لتتبع حالة الأخطاء في الحقول
  bool _postSubCostError = false;
  bool _doorToDoorCostError = false;
  bool _totalPaidError = false;
  bool _insurancePercentError = false;
  bool _goodsValueError = false;

  @override
  void initState() {
    super.initState();

    // استخدام المتغير _logger
    _logger.info('تم تهيئة مكون CostInfo');

    // إعادة تعيين الحقول إلى قيمها الافتراضية
    resetFields();

    // إضافة مستمعين للحقول التي تؤثر على الحسابات
    insurancePercentController.addListener(() {
      calculateInsuranceAmount();
      _validateFields(); // التحقق من الحقول عند تغيير القيمة
    });
    goodsValueController.addListener(() {
      calculateInsuranceAmount();
      _validateFields(); // التحقق من الحقول عند تغيير القيمة
    });
    exportDocController.addListener(calculateTotals);
    boxPackingCostController.addListener(calculateTotals);
    doorToDoorCostController.addListener(() {
      calculateTotals();
      _validateFields(); // التحقق من الحقول عند تغيير القيمة
    });
    discountAmountController.addListener(calculateTotals);
    totalPaidController.addListener(() {
      calculateUnpaid();
      _validateFields(); // التحقق من الحقول عند تغيير القيمة
    });
    postSubCostController.addListener(() {
      calculateTotals();
      _validateFields(); // التحقق من الحقول عند تغيير القيمة
    });

    // إضافة مستمع لحقل إجمالي تكلفة البريد لحساب قيمة اليورو مباشرة
    totalPostCostController.addListener(_calculateEuroValues);

    // إضافة مستمع لحقل المبلغ غير المدفوع لحساب قيمة اليورو مباشرة
    unpaidAmountController.addListener(_calculateEuroValues);

    // التحقق من الحقول عند التهيئة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _validateFields();
      _calculateEuroValues(); // حساب قيم اليورو عند التهيئة
    });
  }

  // دالة لإعادة تعيين جميع الحقول إلى قيمها الافتراضية
  @override
  void resetFields() {
    try {
      // إعادة تعيين قيم الحقول
      insurancePercentController.text = '6';
      goodsValueController.text = '0';
      insuranceAmountController.text = '0';
      exportDocController.text = '0';
      boxPackingCostController.text = '0';
      doorToDoorCostController.text = '0';
      postSubCostController.text = '0';
      discountAmountController.text = '0';
      totalPostCostController.text = '0';
      totalPaidController.text = '0';
      unpaidAmountController.text = '0';
      totalCostEURController.text = '0.00';
      unpaidEURController.text = '0.00';

      // إعادة تعيين القيم الأخرى
      useInsurance = false;
      transferUnpaidToPaid = false;

      // إعادة حساب القيم
      calculateInsuranceAmount();
      calculateTotals();
      calculateUnpaid();

      // إعادة تعيين حالة الأخطاء
      _postSubCostError = false;
      _doorToDoorCostError = false;
      _totalPaidError = false;
      _insurancePercentError = false;
      _goodsValueError = false;

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }

      _logger.info('تم إعادة تعيين جميع حقول التكلفة');
    } catch (e) {
      _logger.severe('خطأ في إعادة تعيين حقول التكلفة: $e');
    }
  }

  // دالة للتحقق من الحقول وتحديد الحقول التي تحتوي على أخطاء
  void _validateFields() {
    if (!mounted) return;

    setState(() {
      // التحقق من حقل Post Sub Cost
      final postSubCost = getPostSubCost();
      _postSubCostError = postSubCost <= 0;

      // التحقق من قيمة Post Sub Cost
      try {
        final textValue = postSubCostController.text.replaceAll(',', '');
        double.tryParse(textValue) ?? 0;
      } catch (e) {
        // خطأ في تحويل النص
      }

      // التحقق من حقل Door To Door Cost
      final doorToDoorCost = getDoorToDoorCost();
      _doorToDoorCostError = doorToDoorCost < 0;

      // التحقق من حقل Total Paid
      final totalPaid = getTotalPaid();
      _totalPaidError = totalPaid < 0;

      // التحقق من حقول التأمين إذا كان مفعل
      if (useInsurance) {
        final insurancePercent = getInsurancePercent();
        _insurancePercentError = insurancePercent <= 0;

        final goodsValue = getGoodsValue();
        _goodsValueError = goodsValue <= 0;
      } else {
        // إعادة تعيين حالة الأخطاء إذا كان التأمين غير مفعل
        _insurancePercentError = false;
        _goodsValueError = false;
      }
    });
  }

  // دالة لحساب قيم اليورو مباشرة
  void _calculateEuroValues() {
    try {
      // الحصول على سعر الصرف
      double exchangeRate = getExchangeRate();

      // حساب إجمالي التكلفة باليورو
      double totalPostCost =
          double.tryParse(totalPostCostController.text.replaceAll(',', '')) ??
              0;
      double totalCostEUR = totalPostCost / exchangeRate;
      totalCostEURController.text = totalCostEUR.toStringAsFixed(2);

      // حساب المبلغ غير المدفوع باليورو
      double unpaidAmount =
          double.tryParse(unpaidAmountController.text.replaceAll(',', '')) ?? 0;
      double unpaidEUR = unpaidAmount / exchangeRate;
      unpaidEURController.text = unpaidEUR.toStringAsFixed(2);

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      // خطأ في حساب قيم اليورو
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد لون خلفية حقل Unpaid Amount بناءً على قيمته
    Color? unpaidBackgroundColor;
    try {
      double unpaidAmount =
          double.tryParse(unpaidAmountController.text.replaceAll(',', '')) ?? 0;
      unpaidBackgroundColor = unpaidAmount > 0 ? Colors.red[100] : null;
    } catch (e) {
      unpaidBackgroundColor = null;
    }

    // تحديد لون خلفية حقل Total Paid بناءً على قيمته
    Color? totalPaidBackgroundColor;
    try {
      double totalPaid =
          double.tryParse(totalPaidController.text.replaceAll(',', '')) ?? 0;
      totalPaidBackgroundColor = totalPaid > 0 ? Colors.green[100] : null;
    } catch (e) {
      totalPaidBackgroundColor = null;
    }

    // تحديد لون خط Door to Door Cost بناءً على قيمته
    Color? doorToDoorTextColor;
    try {
      double doorToDoorCost =
          double.tryParse(doorToDoorCostController.text) ?? 0;
      doorToDoorTextColor = doorToDoorCost != 0 ? Colors.blue : null;
    } catch (e) {
      doorToDoorTextColor = null;
    }

    return Card(
      elevation: 4.r,
      margin: EdgeInsets.all(4.r),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cost Information',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 8.h),

            // استخدام Column بدلاً من SingleChildScrollView لإظهار جميع الحقول
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // حقل نسبة التأمين مع خيار التأمين على اليمين
                Row(
                  children: [
                    Expanded(
                      child: buildTextField(
                        controller: insurancePercentController,
                        label: 'Insurance percent %',
                        isNumeric: true,
                        formatWithCommas: false,
                        backgroundColor:
                            useInsurance ? const Color(0xFFE0F2E9) : null,
                        isError: _insurancePercentError, // إضافة حالة الخطأ
                      ),
                    ),
                    // خيار التأمين كزر صغير
                    Tooltip(
                      message: 'Enable/Disable Insurance',
                      child: Checkbox(
                        value: useInsurance,
                        onChanged: (value) {
                          setState(() {
                            useInsurance = value ?? false;
                            // إعادة حساب مبلغ التأمين عند تغيير حالة الخيار
                            calculateInsuranceAmount();
                            _validateFields(); // التحقق من الحقول عند تغيير حالة التأمين
                          });
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 6.h),
                buildTextField(
                  controller: goodsValueController,
                  label: 'Goods Value',
                  isNumeric: true,
                  formatWithCommas: true,
                  isError: _goodsValueError, // إضافة حالة الخطأ
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: insuranceAmountController,
                  label: 'Insurance Amount',
                  isNumeric: true,
                  readOnly: true,
                  formatWithCommas: true,
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: exportDocController,
                  label: 'Export Doc',
                  isNumeric: true,
                  formatWithCommas: true,
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: boxPackingCostController,
                  label: 'Box Packing cost',
                  isNumeric: true,
                  formatWithCommas: true,
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: doorToDoorCostController,
                  label: 'Door to Door Cost',
                  isNumeric: true,
                  readOnly: true,
                  isBold: true,
                  textColor: doorToDoorTextColor,
                  formatWithCommas: true,
                  isError: _doorToDoorCostError, // إضافة حالة الخطأ
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: postSubCostController,
                  label: 'Post sub cost',
                  isNumeric: true,
                  readOnly: true,
                  textColor: Colors.blue,
                  formatWithCommas: true,
                  isError: _postSubCostError, // إضافة حالة الخطأ
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: discountAmountController,
                  label: 'Discount Amount',
                  isNumeric: true,
                  backgroundColor: Colors.red[50],
                  formatWithCommas: true,
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: totalPostCostController,
                  label: 'Total post cost',
                  isNumeric: true,
                  readOnly: true,
                  isBold: true,
                  formatWithCommas: true,
                ),
                const SizedBox(height: 6),
                // حقل إجمالي المدفوع مع خيار تحويل المبلغ غير المدفوع
                Row(
                  children: [
                    Expanded(
                      child: buildTextField(
                        controller: totalPaidController,
                        label: 'Total Paid',
                        isNumeric: true,
                        backgroundColor: totalPaidBackgroundColor,
                        formatWithCommas: true,
                        isBold: true,
                        isError: _totalPaidError, // إضافة حالة الخطأ
                        onChanged: (value) {
                          // تحديث لون الخلفية عند تغيير القيمة
                          calculateUnpaid();

                          // استخدام addPostFrameCallback لتأجيل استدعاء setState حتى اكتمال عملية البناء
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (mounted) {
                              setState(() {});
                            }
                          });
                        },
                      ),
                    ),
                    // خيار تحويل المبلغ غير المدفوع كزر صغير
                    Tooltip(
                      message: 'Convert unpaid amount to paid',
                      child: Checkbox(
                        value: transferUnpaidToPaid,
                        onChanged: handleTransferUnpaidToPaid,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: unpaidAmountController,
                  label: 'Unpaid Amount',
                  isNumeric: true,
                  readOnly: true,
                  isBold: true,
                  backgroundColor: unpaidBackgroundColor,
                  formatWithCommas: true,
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: totalCostEURController,
                  label: 'Total Cost EUR',
                  isNumeric: true,
                  readOnly: true,
                  backgroundColor: Colors.orange[50],
                  formatWithCommas: false,
                ),
                const SizedBox(height: 6),
                buildTextField(
                  controller: unpaidEURController,
                  label: 'Unpaid and will pay in EUR',
                  isNumeric: true,
                  readOnly: true,
                  isBold: true,
                  backgroundColor: Colors.orange[50],
                  formatWithCommas: false,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
