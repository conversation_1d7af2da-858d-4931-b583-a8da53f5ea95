import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/database_helper.dart';
import '../../utils/constants.dart';

// مزود لقاعدة البيانات
final databaseProvider = Provider<DatabaseHelper>((ref) {
  return DatabaseHelper();
});

// مزود للحصول على جميع المستخدمين
final usersProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final dbHelper = ref.watch(databaseProvider);
  return await dbHelper.getUsers();
});

// مزود للحصول على جميع الشحنات
final shipmentsProvider =
    FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final dbHelper = ref.watch(databaseProvider);
  return await dbHelper.getAll(DbConstants.tableShipments);
});

// مزود للحصول على جميع بيانات الكود
final codeDataProvider =
    FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final dbHelper = ref.watch(databaseProvider);
  return await dbHelper.getAll(DbConstants.tableCodeData);
});
