import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/print_settings_model.dart';

/// خدمة قاعدة بيانات إعدادات الطباعة
class PrintSettingsDatabaseService {
  static final PrintSettingsDatabaseService _instance = PrintSettingsDatabaseService._internal();
  factory PrintSettingsDatabaseService() => _instance;
  PrintSettingsDatabaseService._internal();

  static Database? _database;
  static const String _dbName = 'print_settings.db';
  static const int _dbVersion = 1;
  static const String tablePrintSettings = 'print_settings';

  /// تهيئة قاعدة البيانات
  static Future<void> initializeDatabaseForPlatform() async {
    if (!kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
  }

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    if (_database != null && _database!.isOpen) {
      return _database!;
    }
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      await initializeDatabaseForPlatform();
      
      Directory databaseDirectory = await _getDatabaseDirectory();
      String path = join(databaseDirectory.path, _dbName);
      
      debugPrint('🗄️ تهيئة قاعدة بيانات إعدادات الطباعة: $path');
      
      return await openDatabase(
        path,
        version: _dbVersion,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة قاعدة بيانات إعدادات الطباعة: $e');
      rethrow;
    }
  }

  /// الحصول على مسار قاعدة البيانات
  Future<Directory> _getDatabaseDirectory() async {
    if (kReleaseMode) {
      try {
        String executablePath = Platform.resolvedExecutable;
        String appDirectory = dirname(executablePath);
        String databasePath = join(appDirectory, 'database');
        
        Directory databaseDir = Directory(databasePath);
        if (!await databaseDir.exists()) {
          await databaseDir.create(recursive: true);
        }
        
        return databaseDir;
      } catch (e) {
        return await getApplicationDocumentsDirectory();
      }
    } else {
      return await getApplicationDocumentsDirectory();
    }
  }

  /// إنشاء قاعدة البيانات
  Future<void> _createDatabase(Database db, int version) async {
    await _createPrintSettingsTable(db);
    await _insertDefaultSettings(db);
  }

  /// ترقية قاعدة البيانات
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // في المستقبل، يمكن إضافة منطق الترقية هنا
  }

  /// إنشاء جدول إعدادات الطباعة
  Future<void> _createPrintSettingsTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tablePrintSettings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        document_type TEXT NOT NULL UNIQUE,
        printer_name TEXT,
        paper_size TEXT NOT NULL DEFAULT 'A4',
        orientation TEXT NOT NULL DEFAULT 'Portrait',
        margin_top REAL NOT NULL DEFAULT 10.0,
        margin_bottom REAL NOT NULL DEFAULT 10.0,
        margin_left REAL NOT NULL DEFAULT 10.0,
        margin_right REAL NOT NULL DEFAULT 10.0,
        dpi INTEGER NOT NULL DEFAULT 300,
        copies INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('CREATE INDEX IF NOT EXISTS idx_print_settings_document_type ON $tablePrintSettings (document_type)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_print_settings_is_default ON $tablePrintSettings (is_default)');
  }

  /// إدراج الإعدادات الافتراضية
  Future<void> _insertDefaultSettings(Database db) async {
    final now = DateTime.now().toIso8601String();
    
    // إعدادات افتراضية لأنواع المستندات المختلفة
    final defaultSettings = [
      {
        'document_type': 'invoice',
        'paper_size': 'A4',
        'orientation': 'Portrait',
        'margin_top': 15.0,
        'margin_bottom': 15.0,
        'margin_left': 15.0,
        'margin_right': 15.0,
        'dpi': 300,
        'copies': 1,
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'document_type': 'office_label',
        'paper_size': 'A4',
        'orientation': 'Portrait',
        'margin_top': 5.0,
        'margin_bottom': 5.0,
        'margin_left': 5.0,
        'margin_right': 5.0,
        'dpi': 300,
        'copies': 1,
        'is_default': 0,
        'created_at': now,
        'updated_at': now,
      },
      {
        'document_type': 'post_label',
        'paper_size': 'A4',
        'orientation': 'Portrait',
        'margin_top': 8.0,
        'margin_bottom': 8.0,
        'margin_left': 8.0,
        'margin_right': 8.0,
        'dpi': 300,
        'copies': 1,
        'is_default': 0,
        'created_at': now,
        'updated_at': now,
      },
    ];

    for (var setting in defaultSettings) {
      await db.insert(tablePrintSettings, setting);
    }
    
    debugPrint('✅ تم إدراج الإعدادات الافتراضية لإعدادات الطباعة');
  }

  /// حفظ إعدادات الطباعة
  Future<int> savePrintSettings(PrintSettingsModel settings) async {
    try {
      final db = await database;
      final now = DateTime.now().toIso8601String();
      
      // التحقق من وجود إعدادات لنوع المستند
      final existing = await db.query(
        tablePrintSettings,
        where: 'document_type = ?',
        whereArgs: [settings.documentType],
        limit: 1,
      );
      
      if (existing.isNotEmpty) {
        // تحديث الإعدادات الموجودة
        final updateMap = {
          'printer_name': settings.printerName,
          'paper_size': settings.paperSize,
          'orientation': settings.orientation,
          'margin_top': settings.marginTop,
          'margin_bottom': settings.marginBottom,
          'margin_left': settings.marginLeft,
          'margin_right': settings.marginRight,
          'dpi': settings.dpi,
          'copies': settings.copies,
          'is_default': settings.isDefault ? 1 : 0,
          'updated_at': now,
        };
        
        final result = await db.update(
          tablePrintSettings,
          updateMap,
          where: 'document_type = ?',
          whereArgs: [settings.documentType],
        );
        debugPrint('✅ تم تحديث إعدادات الطباعة لـ ${settings.documentType}');
        return result;
      } else {
        // إدراج إعدادات جديدة
        final insertMap = {
          'document_type': settings.documentType,
          'printer_name': settings.printerName,
          'paper_size': settings.paperSize,
          'orientation': settings.orientation,
          'margin_top': settings.marginTop,
          'margin_bottom': settings.marginBottom,
          'margin_left': settings.marginLeft,
          'margin_right': settings.marginRight,
          'dpi': settings.dpi,
          'copies': settings.copies,
          'is_default': settings.isDefault ? 1 : 0,
          'created_at': now,
          'updated_at': now,
        };
        
        final result = await db.insert(tablePrintSettings, insertMap);
        debugPrint('✅ تم حفظ إعدادات طباعة جديدة لـ ${settings.documentType}');
        return result;
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات الطباعة: $e');
      rethrow;
    }
  }

  /// استرجاع إعدادات الطباعة لنوع مستند معين
  Future<PrintSettingsModel?> getPrintSettings(String documentType) async {
    try {
      final db = await database;
      
      final result = await db.query(
        tablePrintSettings,
        where: 'document_type = ?',
        whereArgs: [documentType],
        limit: 1,
      );
      
      if (result.isNotEmpty) {
        return PrintSettingsModel.fromMap(result.first);
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع إعدادات الطباعة: $e');
      return null;
    }
  }

  /// استرجاع جميع إعدادات الطباعة
  Future<List<PrintSettingsModel>> getAllPrintSettings() async {
    try {
      final db = await database;
      
      final result = await db.query(
        tablePrintSettings,
        orderBy: 'document_type ASC',
      );
      
      return result.map((map) => PrintSettingsModel.fromMap(map)).toList();
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع جميع إعدادات الطباعة: $e');
      return [];
    }
  }

  /// حذف إعدادات الطباعة لنوع مستند معين
  Future<int> deletePrintSettings(String documentType) async {
    try {
      final db = await database;
      
      final result = await db.delete(
        tablePrintSettings,
        where: 'document_type = ?',
        whereArgs: [documentType],
      );
      
      debugPrint('✅ تم حذف إعدادات الطباعة لـ $documentType');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في حذف إعدادات الطباعة: $e');
      return 0;
    }
  }

  /// تعيين إعدادات افتراضية
  Future<int> setDefaultSettings(String documentType) async {
    try {
      final db = await database;
      final now = DateTime.now().toIso8601String();
      
      // إزالة العلامة الافتراضية من جميع الإعدادات
      await db.update(
        tablePrintSettings,
        {'is_default': 0, 'updated_at': now},
      );
      
      // تعيين الإعدادات المحددة كافتراضية
      final result = await db.update(
        tablePrintSettings,
        {'is_default': 1, 'updated_at': now},
        where: 'document_type = ?',
        whereArgs: [documentType],
      );
      
      debugPrint('✅ تم تعيين إعدادات $documentType كافتراضية');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في تعيين الإعدادات الافتراضية: $e');
      return 0;
    }
  }

  /// استرجاع الإعدادات الافتراضية
  Future<PrintSettingsModel?> getDefaultSettings() async {
    try {
      final db = await database;
      
      final result = await db.query(
        tablePrintSettings,
        where: 'is_default = ?',
        whereArgs: [1],
        limit: 1,
      );
      
      if (result.isNotEmpty) {
        return PrintSettingsModel.fromMap(result.first);
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع الإعدادات الافتراضية: $e');
      return null;
    }
  }

  /// إعادة تعيين الإعدادات إلى القيم الافتراضية
  Future<void> resetToDefaults() async {
    try {
      final db = await database;
      
      // حذف جميع الإعدادات الموجودة
      await db.delete(tablePrintSettings);
      
      // إعادة إدراج الإعدادات الافتراضية
      await _insertDefaultSettings(db);
      
      debugPrint('✅ تم إعادة تعيين إعدادات الطباعة إلى القيم الافتراضية');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين الإعدادات: $e');
      rethrow;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    if (_database != null && _database!.isOpen) {
      await _database!.close();
      _database = null;
      debugPrint('🗄️ تم إغلاق قاعدة بيانات إعدادات الطباعة');
    }
  }
}