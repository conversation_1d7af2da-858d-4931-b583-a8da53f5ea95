import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../models/item_data.dart';
import '../../services/goods_data_service.dart';

/// مزيج يحتوي على متغيرات وطرق إدارة البيانات لمكون معلومات المرسل
mixin SenderInfoDataMixin<T extends StatefulWidget> on State<T> {
  // إنشاء مسجل للأحداث
  final Logger _logger = Logger('SenderInfoDataMixin');

  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _senderNameController = TextEditingController();
  final TextEditingController _senderPhoneController = TextEditingController();
  final TextEditingController _senderIdController = TextEditingController();
  final TextEditingController _goodsDescriptionController =
      TextEditingController();

  // متغيرات لتخزين نوع الهوية ومسار صورة الهوية
  String _senderIdType = '';
  String _senderIdImagePath = '';

  // قائمة العناصر المحددة
  final List<ItemData> _selectedItems = [];

  // قائمة جميع العناصر المتاحة
  final List<ItemData> _allItems = [];

  // خدمة بيانات البضائع
  final GoodsDataService _goodsDataService = GoodsDataService();

  @override
  void initState() {
    super.initState();
    _loadGoodsData();
  }

  @override
  void dispose() {
    _senderNameController.dispose();
    _senderPhoneController.dispose();
    _senderIdController.dispose();
    _goodsDescriptionController.dispose();
    super.dispose();
  }

  // دوال الحصول على البيانات
  String getSenderName() => _senderNameController.text;
  String getSenderPhone() => _senderPhoneController.text;
  String getSenderId() => _senderIdController.text;
  String getSenderIdType() => _senderIdType;
  String getSenderIdImagePath() => _senderIdImagePath;
  String getGoodsDescription() => _goodsDescriptionController.text;
  List<ItemData> getSelectedItems() => _selectedItems;
  List<ItemData> getAllItems() => _allItems;

  // دوال تعيين البيانات
  void setSenderName(String value) => _senderNameController.text = value;
  void setSenderPhone(String value) => _senderPhoneController.text = value;
  void setSenderId(String value) {
    _senderIdController.text = value;
    // تسجيل عملية التفريغ
    if (value.isEmpty) {
      _logger.info('تم تفريغ قيمة حقل sender id');
    }
  }

  void setSenderIdType(String value) => setState(() => _senderIdType = value);
  void setSenderIdImagePath(String value) =>
      setState(() => _senderIdImagePath = value);
  void setGoodsDescription(String value) =>
      setState(() => _goodsDescriptionController.text = value);

  // دالة لمسح جميع العناصر المحددة
  void clearSelectedItems() {
    try {
      _selectedItems.clear();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // دالة لإضافة العناصر المحددة
  void addSelectedItems(List<ItemData> items) {
    try {
      _selectedItems.addAll(items);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // دالة لإعادة تعيين الحقول
  void resetFields() {
    setState(() {
      _senderNameController.clear();
      _senderPhoneController.clear();
      _senderIdController.clear(); // تفريغ قيمة حقل sender id
      _goodsDescriptionController.clear();
      _senderIdType = '';
      _senderIdImagePath = '';
      _selectedItems.clear();

      // إعادة تعيين حالة الاختيار لجميع العناصر
      for (var item in _allItems) {
        item.isSelected = false;
        item.quantity = 1;
        item.weight = 0.0;
      }
    });
  }

  // تحميل بيانات البضائع من قاعدة البيانات
  Future<void> _loadGoodsData() async {
    try {
      final goodsDataService = GoodsDataService();
      final goodsItems = await goodsDataService.getAllGoodsAsItems();

      setState(() {
        _allItems.clear();
        _allItems.addAll(goodsItems);
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات البضائع: $e');
    }
  }

  // دالة للحصول على خدمة بيانات البضائع
  dynamic getGoodsDataService() {
    try {
      return _goodsDataService;
    } catch (e) {
      return null;
    }
  }
}
