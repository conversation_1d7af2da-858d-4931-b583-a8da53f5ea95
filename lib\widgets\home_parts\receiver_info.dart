import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logging/logging.dart';
import 'address_info.dart';
import 'price_info.dart';
import '../../services/code_data_service.dart';
import '../../services/event_bus_service.dart';
import '../../services/data_preload_service.dart';
import '../../utils/ui_helper.dart';
import 'dart:async';

class ReceiverInfo extends StatefulWidget {
  final Function(double)? onPostSubCostChanged;
  final Function(double)? onDoorToDoorCostChanged;
  final Function(bool)? onAddressInfoVisibilityChanged;
  final CodeDataService codeDataService;
  final Widget? notesWidget;

  const ReceiverInfo({
    super.key,
    this.onPostSubCostChanged,
    this.onDoorToDoorCostChanged,
    this.onAddressInfoVisibilityChanged,
    required this.codeDataService,
    this.notesWidget,
  });

  @override
  State<ReceiverInfo> createState() => _ReceiverInfoState();
}

class _ReceiverInfoState extends State<ReceiverInfo> {
  // إنشاء مسجل خاص بهذا الكلاس
  final Logger _logger = Logger('ReceiverInfo');

  // تعريف متحكمات النصوص لكل حقل
  final TextEditingController _receiverNameController = TextEditingController();
  final TextEditingController _receiverPhoneController =
      TextEditingController();

  // مفتاح للوصول إلى مكون معلومات العنوان
  final GlobalKey<State<AddressInfo>> _addressInfoKey =
      GlobalKey<State<AddressInfo>>();

  // مفتاح للوصول إلى مكون معلومات الأسعار
  final GlobalKey<State<PriceInfo>> _priceInfoKey =
      GlobalKey<State<PriceInfo>>();

  // إضافة خدمة ناقل الأحداث
  final EventBusService _eventBus = EventBusService();
  StreamSubscription? _eventSubscription;

  // قائمة الدول المفضلة بالترتيب المطلوب
  final List<String> _preferredCountriesOrder = [
    'Germany',
    'Netherlands',
    'United Kingdom',
    'Sweden',
    'Finland',
    'Europe Post',
    'Outside Europe',
    'Australia & New Zealand',
  ];

  // قوائم الدول والمدن الفعلية (سيتم تحميلها من قاعدة البيانات)
  List<String> _countries = [];
  Map<String, List<String>> _citiesByCountry = {};
  bool _isLoading = true;

  // القيم المختارة
  String? _selectedCountry;
  String? _selectedCity;
  bool _showAddressInfo = false;

  // إضافة متغير للوصول إلى DataPreloadService
  final DataPreloadService _dataPreloadService = DataPreloadService();

  @override
  void initState() {
    super.initState();

    // إضافة البيانات الافتراضية إلى قاعدة البيانات ثم تحميل البيانات
    _initializeData();

    // الاستماع إلى أحداث تغيير البيانات
    _listenToDataChanges();
  }

  @override
  void dispose() {
    _receiverNameController.dispose();
    _receiverPhoneController.dispose();
    _eventSubscription?.cancel();
    super.dispose();
  }

  // دالة تهيئة البيانات
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام البيانات المحملة مسبقًا
      final dataPreloadService = DataPreloadService();

      if (dataPreloadService.isDataLoaded) {
        // استخدام البيانات المحملة مسبقًا
        setState(() {
          _countries = List.from(dataPreloadService.countries);
          _citiesByCountry = Map.from(dataPreloadService.citiesByCountry);
          _isLoading = false;
        });

        // تعيين القيم الافتراضية
        _setDefaultValues();

        return;
      }

      // إذا لم تكن البيانات محملة مسبقًا، قم بتحميلها
      await dataPreloadService.preloadData();

      setState(() {
        _countries = List.from(dataPreloadService.countries);
        _citiesByCountry = Map.from(dataPreloadService.citiesByCountry);
        _isLoading = false;
      });

      // تعيين القيم الافتراضية
      _setDefaultValues();
    } catch (e) {
      // خطأ في تهيئة بيانات الدول والمدن
      setState(() {
        _isLoading = false;
      });
    }
  }

  // دالة تحميل الدول من قاعدة البيانات

  // دالة تحميل المدن من قاعدة البيانات

  // تعيين القيم الافتراضية
  void _setDefaultValues() {
    if (_countries.isNotEmpty) {
      // التأكد من عدم وجود تكرار في قائمة الدول
      final uniqueCountries = _countries.toSet().toList();

      if (_countries.length != uniqueCountries.length) {
        setState(() {
          _countries = uniqueCountries;
        });
      }

      // ترتيب الدول حسب القائمة المفضلة
      _sortCountriesByPreferredOrder();

      // التأكد من أن الدولة المختارة موجودة في القائمة
      String defaultCountry = _countries.first;

      setState(() {
        _selectedCountry = defaultCountry;
        _selectedCity = _citiesByCountry[_selectedCountry]?.first;
        _checkIfShowAddressInfo();
      });
    } else {
      setState(() {
        _selectedCountry = null;
        _selectedCity = null;
      });

      // عرض رسالة للمستخدم عندما تكون قائمة الدول فارغة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          UiHelper.showSnackBar(
            context,
            'Countries list not available. Please add countries in pricing section first.',
            isError: true,
          );
        }
      });
    }
  }

  // دالة لترتيب الدول حسب القائمة المفضلة
  void _sortCountriesByPreferredOrder() {
    // إنشاء قائمة جديدة للدول المرتبة
    List<String> sortedCountries = [];

    // إضافة الدول المفضلة أولاً بالترتيب المحدد
    for (String country in _preferredCountriesOrder) {
      if (_countries.contains(country)) {
        sortedCountries.add(country);
      }
    }

    // إضافة الدول الإضافية في نهاية القائمة
    for (String country in _countries) {
      if (!sortedCountries.contains(country)) {
        sortedCountries.add(country);
      }
    }

    // تحديث قائمة الدول
    setState(() {
      _countries = sortedCountries;
    });
  }

  // تحديث المدينة المختارة عند تغيير الدولة
  void _updateSelectedCity() {
    if (_selectedCountry != null) {
      final cities = _citiesByCountry[_selectedCountry] ?? [];

      // التحقق مما إذا كانت المدينة المختارة سابقًا موجودة في قائمة المدن الجديدة
      if (_selectedCity != null && cities.contains(_selectedCity)) {
        // لا تغيير للمدينة المختارة
      } else {
        _selectedCity = cities.isNotEmpty ? cities.first : null;
      }

      // التحقق مما إذا كان يجب إظهار قسم العنوان بعد تغيير المدينة
      _checkIfShowAddressInfo();

      // تحديث قيمة For each 1 Kg بعد تغيير الدولة والمدينة
      // سيتم استدعاء _updatePrices في PriceInfo عند إعادة بناء الواجهة
    } else {
      _selectedCity = null;
      _showAddressInfo = false;
    }
  }

  // التحقق مما إذا كان يجب إظهار قسم العنوان
  void _checkIfShowAddressInfo() {
    if (_selectedCountry != null) {
      bool previousShowAddressInfo = _showAddressInfo;
      bool shouldShowAddressInfo = false;

      // إضافة سجلات للتصحيح
      _logger.info('--------- بداية فحص حالة قسم العنوان ---------');
      _logger.info('الدولة المختارة: $_selectedCountry');
      _logger.info('المدينة المختارة: $_selectedCity');

      // إظهار قسم العنوان في الحالات التالية:
      // 1. إذا كانت المدينة تحتوي على كلمة "Post" أو "post" أو "POST"
      // 2. إذا كانت الدولة هي "Europe Post" أو "Outside Europe" أو "Australia & New Zealand"
      // 3. إذا كانت الدولة تحتوي على "europe post" أو "outside europe post" أو "new zealand and australia"

      // أولاً: التحقق من الدولة (أولوية أعلى)
      if (_selectedCountry != null) {
        final countryLower = _selectedCountry!.toLowerCase().trim();
        _logger.info('الدولة بعد التحويل والتنظيف: $countryLower');

        // التحقق من القيم العامة في الدولة
        if (countryLower.contains('europe post') ||
            countryLower.contains('outside europe post') ||
            countryLower.contains('new zealand and australia') ||
            _selectedCountry == 'Europe Post' ||
            _selectedCountry == 'Outside Europe' ||
            _selectedCountry == 'Australia & New Zealand') {
          shouldShowAddressInfo = true;
          _logger.info(
              'تم تفعيل قسم العنوان لأن الدولة تتطلب معلومات العنوان: $_selectedCountry');
        }
      }

      // ثانياً: التحقق من المدينة إذا لم يتم تفعيل قسم العنوان بناءً على الدولة
      if (!shouldShowAddressInfo && _selectedCity != null) {
        final cityLower = _selectedCity!.toLowerCase().trim();
        _logger.info('المدينة بعد التحويل والتنظيف: $cityLower');

        if (cityLower.contains('post')) {
          shouldShowAddressInfo = true;
          _logger.info(
              'تم تفعيل قسم العنوان لأن المدينة تحتوي على كلمة "post": $_selectedCity');
        }
      }

      // تسجيل حالة قسم العنوان قبل وبعد التغيير
      _logger.info('حالة قسم العنوان قبل التغيير: $previousShowAddressInfo');
      _logger.info('حالة قسم العنوان المقترحة: $shouldShowAddressInfo');

      // تحديث الحالة وإشعار الشاشة الرئيسية بالتغيير
      setState(() {
        _showAddressInfo = shouldShowAddressInfo;
      });
      _logger.info('تم تحديث حالة قسم العنوان إلى: $_showAddressInfo');

      // إشعار الشاشة الرئيسية بتغيير حالة Address info
      if (widget.onAddressInfoVisibilityChanged != null) {
        widget.onAddressInfoVisibilityChanged!(_showAddressInfo);
      }

      _logger.info('--------- نهاية فحص حالة قسم العنوان ---------');

      // إذا تغيرت حالة قسم العنوان، نقوم بتحديث Door to Door Cost
      // استخدام addPostFrameCallback لتأجيل استدعاء الدالة حتى اكتمال عملية البناء
      final currentContext = context;
      final isMounted = mounted;
      final homeScreenState = currentContext.findAncestorStateOfType<State>();

      Future.delayed(const Duration(milliseconds: 100), () {
        if (!isMounted) return;

        if (homeScreenState != null) {
          try {
            (homeScreenState as dynamic).onRealWeightChanged();
            _logger.info(
                'تم استدعاء دالة onRealWeightChanged لتحديث الأسعار بعد تغيير المدينة');
          } catch (e) {
            _logger.severe(
                'خطأ في استدعاء دالة onRealWeightChanged عند تغيير المدينة: $e');
          }
        } else {
          _logger.warning('homeScreenState هو null');
        }
      });
    } else {
      // إذا لم يتم اختيار دولة، نخفي قسم العنوان
      setState(() {
        _showAddressInfo = false;
      });
      _logger.info('تم إخفاء قسم العنوان لأنه لم يتم اختيار دولة');
    }
  }

  // دالة للحصول على اسم المستلم
  String getReceiverName() {
    return _receiverNameController.text;
  }

  void _handlePostSubCostChanged(double value) {
    if (widget.onPostSubCostChanged != null) {
      // استخدام addPostFrameCallback لتأجيل استدعاء onPostSubCostChanged حتى اكتمال عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onPostSubCostChanged!(value);
        }
      });
    }
  }

  // دالة للحصول على رقم هاتف المستلم
  String getReceiverPhone() {
    return _receiverPhoneController.text.trim();
  }

  // دالة للحصول على الدولة المختارة
  String? getSelectedCountry() {
    return _selectedCountry;
  }

  // دالة للحصول على المدينة المختارة
  String? getSelectedCity() {
    return _selectedCity;
  }

  // دالة لمسح حقول المستلم المطلوبة بعد حذف السجل
  void clearReceiverFields() {
    setState(() {
      _receiverNameController.clear();
      _receiverPhoneController.clear();

      // إعادة تعيين الدولة والمدينة إلى القيم الافتراضية
      if (_countries.isNotEmpty) {
        _selectedCountry = _countries.first;
        _selectedCity = _citiesByCountry[_selectedCountry]?.first;
        _checkIfShowAddressInfo();
      } else {
        _selectedCountry = null;
        _selectedCity = null;
        _showAddressInfo = false;
      }

      // مسح حقول العنوان إذا كانت ظاهرة
      final addressInfoState = _addressInfoKey.currentState;
      if (addressInfoState != null) {
        try {
          (addressInfoState as dynamic).clearAddressFields();
        } catch (e) {
          _logger.severe('خطأ في مسح حقول العنوان: $e');
        }
      }
    });
  }

  // دالة للحصول على قيمة Minimum price
  double getMinimumPrice() {
    // الحصول على قيمة Minimum price من PriceInfo
    State<PriceInfo>? priceInfoState = _priceInfoKey.currentState;
    if (priceInfoState != null) {
      try {
        // محاولة الحصول على قيمة Minimum price من PriceInfo
        double minimumPrice =
            (priceInfoState as dynamic).getMinimumPrice().toDouble();
        _logger.info(
            'تم الحصول على قيمة Minimum price من PriceInfo: $minimumPrice');
        return minimumPrice;
      } catch (e) {
        _logger.severe('خطأ في الحصول على قيمة Minimum price من PriceInfo: $e');
      }
    }

    // إذا لم يكن بالإمكان الحصول على القيمة من PriceInfo، استخدم القيم الافتراضية
    if (_selectedCountry == null) return 0;

    double defaultPrice = 0.0;
    if (_selectedCountry == 'Germany' || _selectedCountry == 'Netherlands') {
      defaultPrice = 35000.0;
    } else if (_selectedCountry == 'United Kingdom' ||
        _selectedCountry == 'Sweden') {
      defaultPrice = 50000.0;
    } else if (_selectedCountry == 'Finland') {
      defaultPrice = 60000.0;
    } else if (_selectedCountry == 'Europe Post') {
      defaultPrice = 70000.0;
      _logger.warning('استخدام قيمة افتراضية لـ Europe Post: $defaultPrice');
      // ?? ??? ????? debugPrint
    } else if (_selectedCountry == 'Outside Europe') {
      defaultPrice = 100000.0;
    } else if (_selectedCountry == 'Australia & New Zealand') {
      defaultPrice = 125000.0;
    }

    _logger.info(
        'استخدام قيمة افتراضية للحد الأدنى للسعر: $defaultPrice للدولة: $_selectedCountry');
    // ?? ??? ????? debugPrint
    return defaultPrice;
  }

  // دالة للتحقق مما إذا كانت معلومات العنوان مرئية
  bool isAddressInfoVisible() {
    _logger.info('استعلام عن حالة ظهور معلومات العنوان: $_showAddressInfo');
    return _showAddressInfo;
  }

  // دالة لتعيين حالة ظهور معلومات العنوان
  void setAddressInfoVisible(bool visible) {
    _logger.info(
        'محاولة تعيين حالة ظهور معلومات العنوان من $_showAddressInfo إلى $visible');

    // تحديث الحالة فقط إذا كانت الحاجة للتغيير
    if (_showAddressInfo != visible) {
      setState(() {
        _showAddressInfo = visible;
      });
      _logger.info('تم تعيين حالة ظهور معلومات العنوان إلى: $visible');

      // إذا تم تفعيل قسم العنوان، نتأكد من أن المكون قد تم إنشاؤه بالفعل
      if (visible) {
        // استخدام Future.delayed لضمان تحديث واجهة المستخدم وإنشاء مكون العنوان
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!mounted) return;

          // تسجيل محاولة الوصول إلى مكون العنوان
          _logger.info('محاولة الوصول إلى مكون معلومات العنوان بعد التأخير');

          // إعادة تعيين حالة العنوان هنا سيؤدي إلى إعادة بناء المكون
          setState(() {
            // إعادة تأكيد أن العنوان مرئي
            _showAddressInfo = true;
          });

          // التحقق من وجود مكون العنوان
          final addressInfoState = _addressInfoKey.currentState;
          if (addressInfoState != null) {
            _logger.info('تم الوصول إلى حالة مكون معلومات العنوان بنجاح');
            // يمكن تنفيذ أي إجراءات إضافية هنا
          } else {
            _logger.warning(
                'لم يتم العثور على حالة مكون معلومات العنوان بعد التأخير');
          }
        });
      }
    } else {
      _logger.info(
          'لم يتم تغيير حالة ظهور معلومات العنوان لأنها نفس القيمة المطلوبة');
    }
  }

  // دالة للحصول على حالة مكون معلومات العنوان
  State<AddressInfo>? getAddressInfoState() {
    return _addressInfoKey.currentState;
  }

  // دالة للحصول على حالة مكون معلومات الأسعار
  State<PriceInfo>? getPriceInfoState() {
    return _priceInfoKey.currentState;
  }

  // دالة لتعيين اسم المستلم
  void setReceiverName(String name) {
    _receiverNameController.text = name;
  }

  // دالة لتعيين رقم هاتف المستلم
  void setReceiverPhone(String phone) {
    _receiverPhoneController.text = phone;
  }

  // دالة لتعيين الدولة والمدينة (تستخدم عند تحميل البيانات)
  void setCountryAndCity(String? country, String? city) {
    // تسجيل رسالة للتتبع
    _logger.info('محاولة تعيين الدولة إلى: $country والمدينة إلى: $city');

    // التحقق مما إذا كانت الدولة والمدينة هي نفسها
    if (_selectedCountry == country && _selectedCity == city) {
      // إذا كانت نفس الدولة والمدينة، لا نقوم بأي تغيير
      _logger.info('الدولة والمدينة هي نفسها، لا يوجد تغيير');
      return;
    }

    // تخزين حالة قسم العنوان الحالية قبل التغيير
    bool previousShowAddressInfo = _showAddressInfo;

    setState(() {
      // تعيين الدولة إذا كانت موجودة في القائمة
      if (country != null &&
          country.isNotEmpty &&
          _countries.contains(country)) {
        _selectedCountry = country;
        _logger.info('تم تعيين الدولة إلى: $country');

        // تحديث قائمة المدن بناءً على الدولة المختارة
        final cities = _citiesByCountry[_selectedCountry] ?? [];

        // تعيين المدينة إذا كانت موجودة في قائمة المدن
        if (city != null && city.isNotEmpty && cities.contains(city)) {
          _selectedCity = city;
          _logger.info('تم تعيين المدينة إلى: $city');
        } else {
          // إذا لم تكن المدينة موجودة، اختر أول مدينة في القائمة
          _selectedCity = cities.isNotEmpty ? cities.first : null;
          _logger
              .info('المدينة غير موجودة، تم تعيين المدينة إلى: $_selectedCity');
        }
      } else {
        // إذا لم تكن الدولة موجودة، اختر أول دولة في القائمة
        _selectedCountry = _countries.isNotEmpty ? _countries.first : null;
        _logger
            .info('الدولة غير موجودة، تم تعيين الدولة إلى: $_selectedCountry');

        // تحديث المدينة بناءً على الدولة المختارة
        final cities = _citiesByCountry[_selectedCountry] ?? [];
        _selectedCity = cities.isNotEmpty ? cities.first : null;
        _logger.info('تم تعيين المدينة إلى: $_selectedCity');
      }

      // الحفاظ على حالة قسم العنوان كما هي
      _showAddressInfo = previousShowAddressInfo;
      _logger.info('تم الحفاظ على حالة قسم العنوان: $_showAddressInfo');
    });
  }

  // تحويل النص إلى حروف كبيرة
  void _convertToUpperCase(TextEditingController controller) {
    final String text = controller.text;
    final String upperCaseText = text.toUpperCase();

    if (text != upperCaseText) {
      controller.value = controller.value.copyWith(
        text: upperCaseText,
        selection: TextSelection.collapsed(offset: upperCaseText.length),
        composing: TextRange.empty,
      );
    }
  }

  // دالة لتعيين الدولة
  void _handleCountryChanged(String? newCountry) async {
    if (newCountry == null) return;

    setState(() {
      _selectedCountry = newCountry;
      _selectedCity = null; // إعادة تعيين المدينة عند تغيير الدولة
      _showAddressInfo = true;
    });

    // تحديث قائمة المدن بناءً على الدولة المختارة
    _updateCitiesList(newCountry);

    // تحديث سعر الصرف للدولة المختارة مباشرة من قاعدة البيانات
    try {
      final exchangeRate =
          await widget.codeDataService.getExchangeRateByCountry(newCountry);
      _logger.info('تم الحصول على سعر الصرف للدولة $newCountry: $exchangeRate');

      // تحديث حقول الأسعار في PriceInfo
      final priceInfoState = _priceInfoKey.currentState;
      if (priceInfoState != null && mounted) {
        // تحديث سعر الصرف مباشرة إذا كان أكبر من صفر
        if (exchangeRate > 0) {
          (priceInfoState as dynamic).setExchangeRate(exchangeRate);
          _logger.info('تم تحديث سعر الصرف إلى: $exchangeRate');
        }

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;

          // تصريف إلى النوع الديناميكي
          (priceInfoState as dynamic).clearPriceFields();
          _logger.info('تم إعادة تعيين حقول الأسعار بعد تغيير الدولة');
        });
      }
    } catch (e) {
      _logger.severe('خطأ في الحصول على سعر الصرف للدولة $newCountry: $e');
    }
  }

  // دالة لتعيين المدن بناءً على الدولة المختارة
  void _updateCitiesList(String? country) {
    _updateSelectedCity(); // تحديث قائمة المدن
    _checkIfShowAddressInfo(); // تحديث حالة عرض معلومات العنوان
  }

  // دالة للاستماع إلى أحداث تغيير البيانات
  void _listenToDataChanges() {
    _eventSubscription = _eventBus.eventStream.listen((event) {
      _logger.info('تم استلام حدث: ${event.type}');

      // التعامل مع الأحداث المختلفة
      switch (event.type) {
        case EventType.countryAdded:
        case EventType.countryUpdated:
        case EventType.countryDeleted:
          _logger.info('تم تغيير بيانات الدول، جاري إعادة التحميل...');
          _reloadDataFromService();
          break;
        case EventType.cityAdded:
        case EventType.cityUpdated:
        case EventType.cityDeleted:
          _logger.info('تم تغيير بيانات المدن، جاري إعادة التحميل...');
          _reloadDataFromService();
          break;
        case EventType.dataRefreshNeeded:
          _logger.info('مطلوب تحديث البيانات بالكامل، جاري إعادة التحميل...');
          _reloadDataFromService();
          break;
        case EventType.priceAdded:
        case EventType.priceUpdated:
        case EventType.priceDeleted:
          // لا نحتاج إلى فعل أي شيء لهذه الأحداث في هذا المكون
          break;
      }
    });
  }

  /// إعادة تحميل البيانات من DataPreloadService
  Future<void> _reloadDataFromService() async {
    try {
      _logger.info('🔄 بدء إعادة تحميل البيانات في ReceiverInfo...');

      // حفظ القيم الحالية قبل إعادة التحميل
      final currentCountry = _selectedCountry;
      final currentCity = _selectedCity;
      final currentShowAddressInfo = _showAddressInfo;

      setState(() {
        _isLoading = true;
      });

      // إعادة تحميل البيانات في DataPreloadService
      await _dataPreloadService.refreshData();

      // الحصول على البيانات المحدثة
      final newCountries = List<String>.from(_dataPreloadService.countries);
      final newCitiesByCountry =
          Map<String, List<String>>.from(_dataPreloadService.citiesByCountry);

      // تحديث البيانات المحلية
      setState(() {
        _countries = newCountries;
        _citiesByCountry = newCitiesByCountry;
        _isLoading = false;
      });

      // ترتيب الدول حسب القائمة المفضلة
      _sortCountriesByPreferredOrder();

      // استعادة القيم المحفوظة إذا كانت لا تزال صالحة
      setState(() {
        if (currentCountry != null && _countries.contains(currentCountry)) {
          // الدولة الحالية لا تزال موجودة في القائمة المحدثة
          _selectedCountry = currentCountry;

          // التحقق من صحة المدينة الحالية
          final cities = _citiesByCountry[_selectedCountry] ?? [];
          if (currentCity != null && cities.contains(currentCity)) {
            // المدينة الحالية لا تزال موجودة
            _selectedCity = currentCity;
          } else {
            // المدينة الحالية غير موجودة، اختر أول مدينة متاحة
            _selectedCity = cities.isNotEmpty ? cities.first : null;
          }

          // الحفاظ على حالة قسم العنوان
          _showAddressInfo = currentShowAddressInfo;

          _logger.info(
              'تم الحفاظ على الدولة: $_selectedCountry والمدينة: $_selectedCity');
        } else {
          // الدولة الحالية غير موجودة، استخدم القيم الافتراضية
          if (_countries.isNotEmpty) {
            _selectedCountry = _countries.first;
            _selectedCity = _citiesByCountry[_selectedCountry]?.first;
            _checkIfShowAddressInfo();
            _logger.info(
                'تم تعيين قيم افتراضية جديدة: $_selectedCountry, $_selectedCity');
          } else {
            _selectedCountry = null;
            _selectedCity = null;
            _showAddressInfo = false;
          }
        }
      });

      _logger.info('✅ تم تحديث البيانات من DataPreloadService بنجاح');
      _logger.info(
          '📊 البيانات المحدثة: ${_countries.length} دولة، ${_citiesByCountry.values.fold(0, (sum, cities) => sum + cities.length)} مدينة');
    } catch (e) {
      _logger.severe('❌ خطأ في إعادة تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });

      // إشعار المستخدم بالخطأ
      if (mounted) {
        UiHelper.showSnackBar(
          context,
          'Error updating data: $e',
          isError: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4.r,
      margin: EdgeInsets.symmetric(vertical: 6.h),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Text(
              'Receiver Information',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 16.h),

            // حقل اسم المستلم مع ارتفاع محسن
            SizedBox(
              height: 60.h, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextFormField(
                controller: _receiverNameController,
                decoration: InputDecoration(
                  labelText: 'receiver name',
                  hintText: 'enter receiver name',
                  border: const OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
                  labelStyle: TextStyle(fontSize: 13.sp),
                ),
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
                onChanged: (value) {
                  // تحويل النص إلى حروف كبيرة عند الكتابة
                  _convertToUpperCase(_receiverNameController);
                },
                // تم إلغاء التحديد التلقائي للنص
                // onTap: () {
                //   // تحديد كل النص الموجود في الحقل
                //   _receiverNameController.selection = TextSelection(
                //     baseOffset: 0,
                //     extentOffset: _receiverNameController.text.length,
                //   );
                // },
              ),
            ),
            SizedBox(height: 8.h),

            // حقل رقم هاتف المستلم مع ارتفاع محسن
            SizedBox(
              height: 60.h, // زيادة ارتفاع الحقل لراحة أكبر
              child: TextFormField(
                controller: _receiverPhoneController,
                decoration: InputDecoration(
                  labelText: 'receiver phone',
                  hintText: 'enter receiver phone',
                  border: const OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
                  labelStyle: TextStyle(fontSize: 13.sp),
                ),
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s]')),
                ],
                // تم إلغاء التحديد التلقائي للنص
                // onTap: () {
                //   // تحديد كل النص الموجود في الحقل
                //   _receiverPhoneController.selection = TextSelection(
                //     baseOffset: 0,
                //     extentOffset: _receiverPhoneController.text.length,
                //   );
                // },
              ),
            ),
            SizedBox(height: 8.h),

            // قائمة الدول مع ارتفاع محسن
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _countries.isEmpty
                    ? _buildEmptyCountriesMessage()
                    : SizedBox(
                        height: 60.h, // زيادة ارتفاع الحقل لراحة أكبر
                        child: DropdownButtonFormField<String>(
                          value: _selectedCountry,
                          decoration: InputDecoration(
                            labelText: 'Country',
                            border: const OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12.w, vertical: 18.h),
                            labelStyle: TextStyle(fontSize: 13.sp),
                          ),
                          isExpanded: true,
                          items: _countries.map((String country) {
                            return DropdownMenuItem<String>(
                              value: country,
                              child: Text(
                                country,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: _handleCountryChanged,
                        ),
                      ),
            SizedBox(height: 8.h),

            // قائمة المدن
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _selectedCountry == null
                    ? _buildEmptyCitiesMessage("Please select country first")
                    : (_citiesByCountry[_selectedCountry]?.isEmpty ?? true)
                        ? _buildEmptyCitiesMessage(
                            "No cities available for this country")
                        : DropdownButtonFormField<String>(
                            value: _selectedCity,
                            decoration: InputDecoration(
                              labelText: 'City',
                              border: const OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12.w, vertical: 12.h),
                              isDense: true,
                              labelStyle: TextStyle(fontSize: 13.sp),
                            ),
                            isExpanded: true,
                            items: (_citiesByCountry[_selectedCountry] ?? [])
                                .map((String city) {
                              return DropdownMenuItem<String>(
                                value: city,
                                child: _buildCityText(city),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedCity = newValue;
                                _checkIfShowAddressInfo();
                              });

                              // إعادة تحميل قيم الأسعار في PriceInfo عند تغيير المدينة
                              final priceInfoState = _priceInfoKey.currentState;
                              if (priceInfoState != null) {
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  if (!mounted) return;

                                  // تصريف إلى النوع الديناميكي
                                  (priceInfoState as dynamic)
                                      .clearPriceFields();
                                  _logger.info(
                                      'تم إعادة تعيين حقول الأسعار بعد تغيير المدينة');

                                  // بعد إعادة تحميل الأسعار، انتظر لفترة قصيرة ثم استدع onRealWeightChanged
                                  // تخزين mounted قبل الفجوة غير المتزامنة
                                  final currentContext = context;
                                  final isMounted = mounted;
                                  final homeScreenState = currentContext
                                      .findAncestorStateOfType<State>();

                                  Future.delayed(
                                      const Duration(milliseconds: 100), () {
                                    if (!isMounted) return;

                                    if (homeScreenState != null) {
                                      try {
                                        (homeScreenState as dynamic)
                                            .onRealWeightChanged();
                                        _logger.info(
                                            'تم استدعاء دالة onRealWeightChanged لتحديث الأسعار بعد تغيير المدينة');
                                      } catch (e) {
                                        _logger.severe(
                                            'خطأ في استدعاء دالة onRealWeightChanged عند تغيير المدينة: $e');
                                      }
                                    }
                                  });
                                });
                              }
                            },
                          ),

            // عرض معلومات العنوان إذا كانت مطلوبة حسب الشرط الأصلي
            if (_showAddressInfo) ...[
              SizedBox(height: 16.h),
              AddressInfo(
                key: _addressInfoKey,
                isVisible: _showAddressInfo,
                selectedCountry: _selectedCountry,
              ),
            ],

            // إضافة حقل Notes هنا بين AddressInfo و PriceInfo
            if (widget.notesWidget != null) ...[
              const SizedBox(height: 16),
              widget.notesWidget!,
            ],

            // عرض معلومات الأسعار
            const SizedBox(height: 16),
            PriceInfo(
              key: _priceInfoKey,
              selectedCountry: _selectedCountry,
              selectedCity: _selectedCity,
              onPostSubCostChanged: _handlePostSubCostChanged,
              codeDataService: widget.codeDataService,
            ),
          ],
        ),
      ),
    );
  }

  // دالة لإنشاء رسالة عندما تكون قائمة الدول فارغة
  Widget _buildEmptyCountriesMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        children: [
          Icon(Icons.info_outline, color: Colors.orange),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              'Countries list not available. Please add countries in pricing section first.',
              style: TextStyle(color: Colors.orange),
            ),
          ),
        ],
      ),
    );
  }

  // دالة لإنشاء رسالة عندما تكون قائمة المدن فارغة
  Widget _buildEmptyCitiesMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Colors.orange),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(color: Colors.orange),
            ),
          ),
        ],
      ),
    );
  }

  // دالة لإنشاء نص المدينة مع تمييز كلمة Post باللون الأزرق
  Widget _buildCityText(String city) {
    // التحقق مما إذا كانت المدينة تحتوي على كلمة post
    if (!city.toLowerCase().contains('post')) {
      return Text(
        city,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      );
    }

    // تسجيل معلومات التصحيح
    _logger.info('تحليل نص المدينة: $city، تحتوي على post؟ true');

    // البحث عن موقع كلمة post في النص (بغض النظر عن حالة الأحرف)
    String lowerCityName = city.toLowerCase();
    int postIndex = lowerCityName.indexOf('post');

    // تحديد النص قبل، أثناء، وبعد كلمة "post"
    String beforePost = city.substring(0, postIndex);
    // الحصول على الكلمة "post" بنفس كتابتها الأصلية كما وردت في النص
    String postText = city.substring(postIndex, postIndex + 4);
    String afterPost = city.substring(postIndex + 4);

    return RichText(
      text: TextSpan(
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        children: [
          TextSpan(text: beforePost),
          TextSpan(
            text: postText,
            style: const TextStyle(
                color: Colors.blue, fontWeight: FontWeight.bold),
          ),
          TextSpan(text: afterPost),
        ],
      ),
    );
  }
}
