import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:window_size/window_size.dart';
import 'dart:io';
import 'package:logging/logging.dart';
import 'screens/home_screen.dart';
import 'screens/reports_screen.dart';
import 'screens/admin_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/login_screen.dart';
import 'utils/constants.dart';
import 'widgets/home_parts/sidebar.dart';
import 'services/database_helper.dart';
import 'providers/riverpod/theme_provider.dart';
import 'providers/riverpod/user_provider.dart';
import 'services/data_preload_service.dart';
import 'services/app_lifecycle_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة خدمة دورة حياة التطبيق للإغلاق الآمن
  AppLifecycleService.instance.initialize();

  // تعيين وضع ملء الشاشة
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

  // تعيين اتجاه الشاشة للوضع الأفقي فقط (اختياري)
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Initialize logging system
  _setupLogging();

  // Initialize database
  await _initializeDatabase();

  // تحميل البيانات مسبقًا
  await _preloadData();

  // Setup desktop window size
  _setupDesktopWindow();

  // Set alert messages display state (false to reduce number of messages)

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

// Initialize logging system
void _setupLogging() {
  // Set default logging level
  Logger.root.level = Level.ALL;

  // Add logging listener
  Logger.root.onRecord.listen((record) {
    // In development environment, use debugPrint to display logs
    if (record.level >= Level.INFO) {
      debugPrint('${record.level.name}: ${record.time}: ${record.message}');
    }

    // In production environment, logic can be added to save logs to a file or send them to a logging service
    if (record.level >= Level.SEVERE) {
      // Logic can be added to save critical errors to a file or send them to a logging service
      if (record.error != null) {
        debugPrint('Error: ${record.error}');
      }
      if (record.stackTrace != null) {
        debugPrint('StackTrace: ${record.stackTrace}');
      }
    }
  });
}

// Initialize database
Future<void> _initializeDatabase() async {
  await DatabaseHelper.initializeDatabaseForPlatform();

  try {
    final dbHelper = DatabaseHelper();
    final result = await dbHelper.updateDatabaseSchema();
    if (result) {
      debugPrint('Database schema updated successfully');
    } else {
      debugPrint('Failed to update database schema');
    }
  } catch (e) {
    debugPrint('Error updating database schema: $e');
  }
}

// تحميل البيانات مسبقًا
Future<void> _preloadData() async {
  try {
    final dataPreloadService = DataPreloadService();
    await dataPreloadService.preloadData();
    debugPrint('تم تحميل البيانات مسبقًا بنجاح');
  } catch (e) {
    debugPrint('خطأ في تحميل البيانات مسبقًا: $e');
  }
}

// Setup desktop window size
void _setupDesktopWindow() {
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    setWindowTitle(AppConstants.appName);
    setWindowMinSize(
        const Size(UIConstants.minWindowWidth, UIConstants.minWindowHeight));
    setWindowMaxSize(Size.infinite);

    // تعيين النافذة في وضع ملء الشاشة عند بدء التشغيل
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        // إضافة تأخير قصير لضمان تهيئة النافذة بشكل صحيح
        await Future.delayed(const Duration(milliseconds: 100));

        // الحصول على حجم الشاشة الفعلي للمستخدم
        final screen = await getCurrentScreen();
        if (screen != null) {
          // تعيين النافذة لتملأ الشاشة بالكامل باستخدام الحجم الفعلي للشاشة
          final screenFrame = screen.visibleFrame;

          // طباعة معلومات الشاشة للتشخيص
          debugPrint(
              'تعيين نافذة بحجم: ${screenFrame.width} × ${screenFrame.height}');

          // تعيين إطار النافذة لملء الشاشة بالكامل
          setWindowFrame(Rect.fromLTWH(screenFrame.left, screenFrame.top,
              screenFrame.width, screenFrame.height));
        }
      } catch (e) {
        debugPrint('خطأ في تعيين وضع ملء الشاشة: $e');
        // استخدام الحجم الافتراضي في حالة حدوث خطأ
        setWindowFrame(const Rect.fromLTWH(0, 0, UIConstants.defaultWindowWidth,
            UIConstants.defaultWindowHeight));
      }
    });
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);

    // إعادة تطبيق وضع ملء الشاشة في كل مرة يتم فيها بناء التطبيق
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    return ScreenUtilInit(
      designSize: const Size(1920, 1080), // حجم التصميم المرجعي للشاشات الكبيرة
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.ltr,
          child: MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            theme: theme,
            initialRoute: '/login',
            routes: {
              '/login': (context) => const LoginScreen(),
              '/home': (context) => const MainScreen(),
            },
            // إضافة مراقب للانتقال بين الصفحات
            navigatorObservers: [
              _HomeNavigationObserver(),
            ],
          ),
        );
      },
    );
  }
}

// إضافة مراقب للانتقال بين الصفحات لمنع ظهور الإشعارات عند الانتقال إلى الصفحة الرئيسية
class _HomeNavigationObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    if (previousRoute != null &&
        previousRoute.settings.name == '/login' &&
        route.settings.name == '/home') {
      // تعيين متغير عام أو استخدام خدمة لتحديد أننا انتقلنا للتو من شاشة تسجيل الدخول
      debugPrint('تم الانتقال من شاشة تسجيل الدخول إلى الشاشة الرئيسية');

      // يمكن هنا وضع منطق لمنع ظهور الإشعارات عند الانتقال
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // استخدام طريقة مختلفة لإخفاء شريط النظام
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      });
    }
    super.didPush(route, previousRoute);
  }
}

class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({super.key});

  @override
  ConsumerState<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends ConsumerState<MainScreen> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // تطبيق وضع ملء الشاشة عند تهيئة الشاشة الرئيسية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    });
  }

  // دالة لبناء قائمة الشاشات بناءً على دور المستخدم
  List<Widget> _buildScreens(String userRole) {
    final List<Widget> screens = [
      const HomeScreen(),
      const ReportsScreen(),
    ];

    // إضافة شاشة Admin فقط إذا كان المستخدم أدمن
    if (userRole == 'admin') {
      screens.add(const AdminScreen());
    }

    // إضافة شاشة Settings دائماً في النهاية
    screens.add(const SettingsScreen());

    return screens;
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider);
    final screens = _buildScreens(user.role);

    return PopScope(
      // معالجة زر العودة للبقاء في وضع ملء الشاشة
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        // إعادة تطبيق وضع ملء الشاشة عند الضغط على زر العودة
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      },
      child: Scaffold(
        body: Row(
          children: [
            // Sidebar
            Sidebar(
              selectedIndex: _selectedIndex,
              onItemSelected: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
            ),

            // Use IndexedStack to maintain screen states
            Expanded(
              child: IndexedStack(
                index: _selectedIndex,
                children: screens,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
